<ion-header [translucent]="true">
  <ion-toolbar>
    <ion-buttons slot="start">
      <ion-button (click)="goBack()">
        <ion-icon name="chevron-back-outline"></ion-icon>
      </ion-button>
    </ion-buttons>
    <ion-title>API Endpoint Switcher</ion-title>
  </ion-toolbar>
</ion-header>

<ion-content [fullscreen]="true" class="ion-padding">
  <div class="switcher-container">
    
    <!-- Current Status -->
    <ion-card>
      <ion-card-header>
        <ion-card-title>Current API Endpoint</ion-card-title>
      </ion-card-header>
      <ion-card-content>
        <p><strong>URL:</strong> {{ currentApiUrl }}</p>
        <ion-button expand="block" fill="outline" (click)="autoDetect()">
          <ion-icon name="search-outline" slot="start"></ion-icon>
          Auto-Detect Best Endpoint
        </ion-button>
      </ion-card-content>
    </ion-card>

    <!-- Quick Actions -->
    <ion-card>
      <ion-card-header>
        <ion-card-title>Quick Actions</ion-card-title>
      </ion-card-header>
      <ion-card-content>
        <ion-button expand="block" (click)="testAllEndpoints()">
          <ion-icon name="flash-outline" slot="start"></ion-icon>
          Test All Endpoints
        </ion-button>
      </ion-card-content>
    </ion-card>

    <!-- Available Endpoints -->
    <ion-card>
      <ion-card-header>
        <ion-card-title>Available Endpoints</ion-card-title>
        <ion-card-subtitle>Select an endpoint to switch to</ion-card-subtitle>
      </ion-card-header>
      <ion-card-content>
        
        <ion-list>
          <ion-item 
            *ngFor="let endpoint of endpoints" 
            [class.active-endpoint]="endpoint.isActive"
            button
          >
            <ion-icon 
              [name]="getStatusIcon(endpoint)" 
              [color]="getStatusColor(endpoint)"
              slot="start">
            </ion-icon>
            
            <ion-label>
              <h2>{{ endpoint.name }}</h2>
              <p>{{ endpoint.description }}</p>
              <p class="endpoint-url">{{ endpoint.url }}</p>
              <p *ngIf="endpoint.testResult" 
                 [class.success-message]="endpoint.testResult.success"
                 [class.error-message]="!endpoint.testResult.success">
                {{ endpoint.testResult.message }}
              </p>
            </ion-label>

            <ion-buttons slot="end">
              <ion-button 
                fill="clear" 
                (click)="testEndpoint(endpoint); $event.stopPropagation()">
                <ion-icon name="refresh-outline"></ion-icon>
              </ion-button>
              
              <ion-button 
                *ngIf="!endpoint.isActive"
                fill="clear" 
                (click)="selectEndpoint(endpoint); $event.stopPropagation()">
                <ion-icon name="checkmark-outline"></ion-icon>
              </ion-button>
              
              <ion-badge *ngIf="endpoint.isActive" color="primary">
                Active
              </ion-badge>
            </ion-buttons>
          </ion-item>
        </ion-list>

      </ion-card-content>
    </ion-card>

    <!-- Help Section -->
    <ion-card>
      <ion-card-header>
        <ion-card-title>Troubleshooting Tips</ion-card-title>
      </ion-card-header>
      <ion-card-content>
        <ion-list>
          <ion-item>
            <ion-icon name="wifi-outline" slot="start" color="primary"></ion-icon>
            <ion-label>
              <h3>ngrok (Recommended)</h3>
              <p>Most reliable for device testing. Works from anywhere.</p>
            </ion-label>
          </ion-item>
          
          <ion-item>
            <ion-icon name="home-outline" slot="start" color="warning"></ion-icon>
            <ion-label>
              <h3>Local IP</h3>
              <p>Requires same WiFi network and firewall configuration.</p>
            </ion-label>
          </ion-item>
          
          <ion-item>
            <ion-icon name="desktop-outline" slot="start" color="medium"></ion-icon>
            <ion-label>
              <h3>Localhost</h3>
              <p>Only works in web browser, not on mobile devices.</p>
            </ion-label>
          </ion-item>
        </ion-list>
      </ion-card-content>
    </ion-card>

  </div>
</ion-content>
