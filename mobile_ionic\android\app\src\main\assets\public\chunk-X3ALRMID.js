import{a as s}from"./chunk-FNGMIKCE.js";import{U as n,j as o,l as e}from"./chunk-7LSN6DH6.js";var h=(()=>{let i=class i{constructor(t){this.http=t,this.apiUrl=`${s.apiUrl}/auth`,console.log("Auth Service initialized with API URL:",this.apiUrl)}login(t){return this.http.post(`${this.apiUrl}/login`,t)}register(t){return this.http.post(`${this.apiUrl}/signup`,t)}setToken(t){localStorage.setItem("access_token",t)}};i.\u0275fac=function(a){return new(a||i)(e(n))},i.\u0275prov=o({token:i,factory:i.\u0275fac,providedIn:"root"});let r=i;return r})();export{h as a};
