import{b as f}from"./chunk-2IZDSB5C.js";import"./chunk-FNGMIKCE.js";import{B as t,C as a,D as s,Ea as _,F as c,Ga as v,Ha as C,I as o,Na as y,R as h,Sa as b,Z as x,gb as M,ob as O,pb as k,u as d,v as l,w as g,z as u}from"./chunk-7LSN6DH6.js";import"./chunk-EFGZ4QZO.js";import"./chunk-VKNYRYKJ.js";import"./chunk-NETZAO6G.js";import"./chunk-YLGV66M3.js";import"./chunk-VD4VFVTQ.js";import"./chunk-HC6MZPB3.js";import"./chunk-IP3IRUG3.js";import"./chunk-UKIOCGZG.js";import"./chunk-RMJ7PCZJ.js";import"./chunk-K54AU7WQ.js";import"./chunk-VBU7NMPV.js";import"./chunk-JIZJ6CAM.js";import"./chunk-MCRJI3T3.js";import"./chunk-YNIR5NDL.js";import"./chunk-NO26UXQI.js";import"./chunk-SELJIRKY.js";import"./chunk-ODNE7IRY.js";import"./chunk-F4H6ZFEG.js";import"./chunk-JWIEPCRG.js";import"./chunk-7WD7LEC6.js";import"./chunk-WTCPO44B.js";import"./chunk-SV7S5NYR.js";import"./chunk-L5T6STQ3.js";import"./chunk-3EJRMEWO.js";import"./chunk-GNOVVPTF.js";import"./chunk-BAKMWPBW.js";import"./chunk-5OMUW5VI.js";import"./chunk-OBXDPQ3V.js";import"./chunk-LNJ3S2LQ.js";var F=(()=>{let r=class r{constructor(e,i,n){this.router=e,this.toastCtrl=i,this.fcmService=n,this.isOffline=!1}ngOnInit(){let e=localStorage.getItem("isOffline");this.isOffline=e==="true"}toggleStatus(){this.isOffline=!this.isOffline,localStorage.setItem("isOffline",String(this.isOffline))}openDisasterMap(e){console.log(`Navigating to map tab for disaster type: ${e}`);let i=e;e==="earthquake"?i="Earthquake":e==="typhoon"?i="Typhoon":e==="flashflood"&&(i="Flood"),this.toastCtrl.create({message:`Loading evacuation centers for ${i}...`,duration:2e3,color:"primary"}).then(n=>n.present()),this.router.navigate(["/tabs/map"],{queryParams:{disasterType:i,filterMode:"true"}})}viewMap(){console.log(`Viewing all evacuation centers in ${this.isOffline?"offline":"online"} mode`),this.toastCtrl.create({message:"Loading all evacuation centers...",duration:2e3,color:"primary"}).then(e=>e.present()),this.router.navigate(["/tabs/map"],{queryParams:{disasterType:"all",filterMode:"true"}})}};r.\u0275fac=function(i){return new(i||r)(l(x),l(O),l(f))},r.\u0275cmp=g({type:r,selectors:[["app-home"]],decls:31,vars:1,consts:[[1,"ion-padding"],[1,"disaster-container"],[1,"home-title"],["src","assets/ALERTO.png","alt","App Logo",1,"home-logo"],[2,"font-size","22px"],[2,"font-size","35px","color","#1565c0","margin-top","0px"],[1,"top-disaster"],[1,"disaster","earthquake",3,"click"],["src","assets/earthquake.png","alt","Earthquake"],[1,"disaster","typhoon",3,"click"],["src","assets/typhoon.png","alt","Typhoon"],[1,"disaster","flood",3,"click"],["src","assets/flood.png","alt","Flood"],["expand","block",1,"view-map",2,"margin-top","24px","width","80%","height","45px","--border-radius","25px",3,"click","disabled"],["name","map","slot","start"]],template:function(i,n){i&1&&(t(0,"ion-content")(1,"div",0)(2,"div",1)(3,"div",2),s(4,"img",3),t(5,"div",4),o(6,"Hi, Welcome to "),t(7,"p",5),o(8,"Safe Area!"),a()()(),t(9,"div",6)(10,"ion-card",7),c("click",function(){return n.openDisasterMap("earthquake")}),t(11,"ion-card-content"),s(12,"img",8),t(13,"ion-text")(14,"u"),o(15,"Earthquake"),a()()()(),t(16,"ion-card",9),c("click",function(){return n.openDisasterMap("typhoon")}),t(17,"ion-card-content"),s(18,"img",10),t(19,"ion-text")(20,"u"),o(21,"Typhoon"),a()()()(),t(22,"ion-card",11),c("click",function(){return n.openDisasterMap("flashflood")}),t(23,"ion-card-content"),s(24,"img",12),t(25,"ion-text")(26,"u"),o(27,"Flash Flood"),a()()()()(),t(28,"ion-button",13),c("click",function(){return n.viewMap()}),s(29,"ion-icon",14),o(30," See the Whole Map "),a()()()()),i&2&&(d(28),u("disabled",n.isOffline))},dependencies:[k,_,v,C,y,b,M,h],styles:[".status-text[_ngcontent-%COMP%]{margin-left:8px}ion-header[_ngcontent-%COMP%], ion-title[_ngcontent-%COMP%]{text-align:center;font-family:Poppins,Arial,sans-serif;font-size:2rem;font-weight:700;letter-spacing:1px;text-shadow:1px 2px 4px #ccc}.disaster-container[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;gap:24px;margin:32px 0 0}.disaster[_ngcontent-%COMP%]{margin:0;cursor:pointer;transition:transform .2s}.disaster[_ngcontent-%COMP%]:hover{transform:scale(1.05)}.disaster[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;text-align:center;padding:16px}.disaster[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:60px;height:60px;margin-bottom:8px}.earthquake[_ngcontent-%COMP%]{--background: #ffcc80}.typhoon[_ngcontent-%COMP%]{--background: #c5e1a5;size:100px;width:105px;height:120px}.flood[_ngcontent-%COMP%]{--background: #81d4fa}.view-map[_ngcontent-%COMP%]{margin-top:24px;--background: #00bfff}.view-map[_ngcontent-%COMP%]:hover{--background: #0090cc}.view-map[disabled][_ngcontent-%COMP%]{--background: #999}.top-disaster[_ngcontent-%COMP%]{display:flex;justify-content:space-between;gap:16px;align-items:center}.home-logo[_ngcontent-%COMP%]{width:150px;height:150px}.home-title[_ngcontent-%COMP%]{padding-top:105px;display:flex;align-items:center;justify-content:center;font-size:30px;font-weight:700;letter-spacing:1px;text-shadow:1px 2px 4px #ccc}.notifications-section[_ngcontent-%COMP%]{margin-top:20px;border-top:1px solid var(--ion-color-light);padding-top:10px}ion-item-divider[_ngcontent-%COMP%]{--background: transparent;--color: var(--ion-color-primary);font-weight:700;font-size:1.1rem;letter-spacing:.5px;margin-bottom:8px}"]});let m=r;return m})();export{F as HomePage};
