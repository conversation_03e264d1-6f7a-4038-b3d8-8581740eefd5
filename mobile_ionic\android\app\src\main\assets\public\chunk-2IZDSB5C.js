import{a as y}from"./chunk-FNGMIKCE.js";import{U as S,Z as I,a as T,b as x,c as A,d as F,g as M,j as w,l as p,lb as E,ob as D,p as N,va as R}from"./chunk-7LSN6DH6.js";import{a as G}from"./chunk-NETZAO6G.js";import{a as m,h as g}from"./chunk-LNJ3S2LQ.js";function L(){if(typeof process>"u"){var o=typeof window<"u"?window:{},t=5e3,n=Date.now(),e=!1;o.document.addEventListener("deviceready",function(){console.log("Ionic Native: deviceready event fired after "+(Date.now()-n)+" ms"),e=!0}),setTimeout(function(){!e&&o.cordova&&console.warn("Ionic Native: deviceready did not fire within "+t+"ms. This can happen when plugins are in an inconsistent state. Try removing plugins from plugins/ and reinstalling them.")},t)}}var $={error:"cordova_not_available"},U={error:"plugin_not_installed"};function k(o){var t=function(){if(Promise)return new Promise(function(i,a){o(i,a)});console.error("No Promise support or polyfill found. To enable Ionic Native support, please add the es6-promise polyfill before this script, or run with a library like Angular or on a recent browser.")};if(typeof window<"u"&&window.angular){var n=window.document,e=window.angular.element(n.querySelector("[ng-app]")||n.body).injector();if(e){var r=e.get("$q");return r(function(i,a){o(i,a)})}console.warn("Angular 1 was detected but $q couldn't be retrieved. This is usually when the app is not bootstrapped on the html or body tag. Falling back to native promises which won't trigger an automatic digest when promises resolve.")}return t()}function J(o,t,n,e){e===void 0&&(e={});var r,i,a=k(function(s,c){e.destruct?r=h(o,t,n,e,function(){for(var l=[],f=0;f<arguments.length;f++)l[f]=arguments[f];return s(l)},function(){for(var l=[],f=0;f<arguments.length;f++)l[f]=arguments[f];return c(l)}):r=h(o,t,n,e,s,c),i=c});return r&&r.error&&(a.catch(function(){}),typeof i=="function"&&i(r.error)),a}function O(o,t,n,e){return e===void 0&&(e={}),k(function(r,i){var a=h(o,t,n,e);a?a.error?i(a.error):a.then&&a.then(r).catch(i):i({error:"unexpected_error"})})}function H(o,t,n,e){return e===void 0&&(e={}),new T(function(r){var i;return e.destruct?i=h(o,t,n,e,function(){for(var a=[],s=0;s<arguments.length;s++)a[s]=arguments[s];return r.next(a)},function(){for(var a=[],s=0;s<arguments.length;s++)a[s]=arguments[s];return r.error(a)}):i=h(o,t,n,e,r.next.bind(r),r.error.bind(r)),i&&i.error&&(r.error(i.error),r.complete()),function(){try{if(e.clearFunction)return e.clearWithArgs?h(o,e.clearFunction,n,e,r.next.bind(r),r.error.bind(r)):h(o,e.clearFunction,[])}catch(a){console.warn("Unable to clear the previous observable watch for",o.constructor.getPluginName(),t),console.warn(a)}}})}function _(o,t){return t=typeof window<"u"&&t?B(window,t):t||(typeof window<"u"?window:{}),M(t,o)}function v(o,t,n){var e,r;typeof o=="string"?e=o:(e=o.constructor.getPluginRef(),n=o.constructor.getPluginName(),r=o.constructor.getPluginInstallName());var i=b(e);return!i||t&&typeof i[t]>"u"?typeof window>"u"||!window.cordova?(Z(n,t),$):(z(n,r,t),U):!0}function K(o,t,n,e){if(t===void 0&&(t={}),t.sync)return o;if(t.callbackOrder==="reverse")o.unshift(e),o.unshift(n);else if(t.callbackStyle==="node")o.push(function(s,c){s?e(s):n(c)});else if(t.callbackStyle==="object"&&t.successName&&t.errorName){var r={};r[t.successName]=n,r[t.errorName]=e,o.push(r)}else if(typeof t.successIndex<"u"||typeof t.errorIndex<"u"){var i=function(){t.successIndex>o.length?o[t.successIndex]=n:o.splice(t.successIndex,0,n)},a=function(){t.errorIndex>o.length?o[t.errorIndex]=e:o.splice(t.errorIndex,0,e)};t.successIndex>t.errorIndex?(a(),i()):(i(),a())}else o.push(n),o.push(e);return o}function h(o,t,n,e,r,i){e===void 0&&(e={}),n=K(n,e,r,i);var a=v(o,t);if(a===!0){var s=b(o.constructor.getPluginRef());return s[t].apply(s,n)}else return a}function b(o){return typeof window<"u"?B(window,o):null}function B(o,t){for(var n=t.split("."),e=o,r=0;r<n.length;r++){if(!e)return null;e=e[n[r]]}return e}function z(o,t,n){console.warn(n?"Native: tried calling "+o+"."+n+", but the "+o+" plugin is not installed.":"Native: tried accessing the "+o+" plugin but it's not installed."),t&&console.warn("Install the "+o+" plugin: 'ionic cordova plugin add "+t+"'")}function Z(o,t){typeof process>"u"&&console.warn(t?"Native: tried calling "+o+"."+t+", but Cordova is not available. Make sure to include cordova.js or run in a device/simulator":"Native: tried accessing the "+o+" plugin but Cordova is not available. Make sure to include cordova.js or run in a device/simulator")}var C=function(o,t,n){return n===void 0&&(n={}),function(){for(var e=[],r=0;r<arguments.length;r++)e[r]=arguments[r];return n.sync?h(o,t,e,n):n.observable?H(o,t,e,n):n.eventObservable&&n.event?_(n.event,n.element):n.otherPromise?O(o,t,e,n):J(o,t,e,n)}};function W(o,t){for(var n=t.split("."),e=o,r=0;r<n.length;r++){if(!e)return null;e=e[n[r]]}return e}var P=function(){function o(){}return o.installed=function(){var t=v(this.pluginRef)===!0;return t},o.getPlugin=function(){return typeof window<"u"?W(window,this.pluginRef):null},o.getPluginName=function(){var t=this.pluginName;return t},o.getPluginRef=function(){var t=this.pluginRef;return t},o.getPluginInstallName=function(){var t=this.plugin;return t},o.getSupportedPlatforms=function(){var t=this.platforms;return t},o.pluginName="",o.pluginRef="",o.plugin="",o.repo="",o.platforms=[],o.install="",o}();function u(o,t,n,e){return C(o,t,n).apply(this,e)}L();var q=function(o){A(t,o);function t(){return o!==null&&o.apply(this,arguments)||this}return t.prototype.getAPNSToken=function(){return u(this,"getAPNSToken",{},arguments)},t.prototype.getToken=function(){return u(this,"getToken",{},arguments)},t.prototype.onTokenRefresh=function(){return u(this,"onTokenRefresh",{observable:!0},arguments)},t.prototype.subscribeToTopic=function(n){return u(this,"subscribeToTopic",{},arguments)},t.prototype.unsubscribeFromTopic=function(n){return u(this,"unsubscribeFromTopic",{},arguments)},t.prototype.hasPermission=function(){return u(this,"hasPermission",{},arguments)},t.prototype.onNotification=function(){return u(this,"onNotification",{observable:!0,successIndex:0,errorIndex:2},arguments)},t.prototype.clearAllNotifications=function(){return u(this,"clearAllNotifications",{},arguments)},t.prototype.requestPushPermissionIOS=function(n){return u(this,"requestPushPermissionIOS",{},arguments)},t.prototype.createNotificationChannelAndroid=function(n){return u(this,"createNotificationChannelAndroid",{},arguments)},t.\u0275fac=(()=>{let n;return function(r){return(n||(n=N(t)))(r||t)}})(),t.\u0275prov=w({token:t,factory:t.\u0275fac}),t.pluginName="FCM",t.plugin="cordova-plugin-fcm-with-dependecy-updated",t.pluginRef="FCMPlugin",t.repo="https://github.com/andrehtissot/cordova-plugin-fcm-with-dependecy-updated",t.platforms=["Android","iOS"],t=F([],t),t}(P);var d=G("FirebaseMessaging",{web:()=>import("./chunk-HCFMJL6U.js").then(o=>new o.FirebaseMessagingWeb)});var Be=(()=>{let t=class t{constructor(e,r,i,a,s,c){this.fcm=e,this.http=r,this.platform=i,this.toastCtrl=a,this.alertCtrl=s,this.router=c,this.notificationSubject=new x,this.notifications$=this.notificationSubject.asObservable()}initPush(){return g(this,null,function*(){try{if(this.platform.is("cordova")||this.platform.is("capacitor")){if(console.log("Initializing FCM..."),this.platform.is("android")&&(yield this.createAndroidNotificationChannels()),(yield this.checkGooglePlayServices())?localStorage.removeItem("google_play_services_missing"):(console.warn("Google Play Services not available. FCM may not work properly."),this.alertCtrl.create({header:"Google Play Services Required",message:"This app requires Google Play Services for push notifications. Please install or update Google Play Services and restart the app.",buttons:["OK"]}).then(r=>r.present()),localStorage.setItem("google_play_services_missing","true")),this.platform.is("capacitor"))try{let r=yield d.requestPermissions();console.log("FCM permission result:",r),r.receive==="granted"?(console.log("FCM permission granted"),console.log("Device registered with FCM")):(console.warn("FCM permission not granted:",r.receive),this.alertCtrl.create({header:"Notification Permission Required",message:"This app requires notification permissions to alert you about emergencies. Please enable notifications for this app in your device settings.",buttons:["OK"]}).then(i=>i.present()))}catch(r){console.error("Error initializing Capacitor Firebase Messaging:",r)}try{let r=yield this.getToken();console.log("=========================================="),console.log("FCM TOKEN FOR TESTING:"),console.log(r),console.log("=========================================="),this.alertCtrl.create({header:"FCM Token (For Testing)",message:r,buttons:["OK"]}).then(i=>i.present()),this.registerTokenWithBackend(r)}catch(r){console.error("Error getting FCM token:",r)}try{this.platform.is("cordova")&&this.fcm.onTokenRefresh().subscribe({next:r=>{console.log("FCM Token refreshed (Cordova):",r),this.registerTokenWithBackend(r)},error:r=>{console.error("Error in token refresh (Cordova):",r)}}),this.platform.is("capacitor")&&d.addListener("tokenReceived",r=>{console.log("FCM Token refreshed (Capacitor):",r.token),this.registerTokenWithBackend(r.token)})}catch(r){console.error("Failed to set up token refresh:",r)}this.setupNotificationListeners()}else console.log("FCM not initialized: not running on a device")}catch(e){console.error("Error in initPush:",e)}})}getToken(){return g(this,null,function*(){if(this.platform.is("capacitor"))try{let e=yield d.getToken();return console.log("Got FCM token from Capacitor Firebase Messaging:",e.token),e.token}catch(e){console.error("Error getting token from Capacitor Firebase Messaging:",e);try{let r=yield this.fcm.getToken();return console.log("Got FCM token from Cordova FCM plugin:",r),r}catch(r){throw console.error("Error getting token from Cordova FCM plugin:",r),r}}else{if(this.platform.is("cordova"))return this.fcm.getToken();{let e="browser-mock-token-"+Math.random().toString(36).substring(2,15);return console.log("Using mock FCM token for browser:",e),Promise.resolve(e)}}})}registerTokenWithBackend(e,r){if(localStorage.getItem("fcm_token")===e){console.log("Token already registered, skipping registration");return}let a="web";this.platform.is("ios")?a="ios":this.platform.is("android")&&(a="android"),console.log(`Registering ${a} token with backend...`);let s={token:e,device_type:a,project_id:y.firebase.projectId||"last-5acaf"};if(r)s.user_id=r,console.log(`Associating token with user ID: ${r}`);else{let c=localStorage.getItem("token");if(c)try{let l=this.parseJwt(c);l&&l.sub&&(s.user_id=l.sub,console.log(`Associating token with user ID from JWT: ${l.sub}`))}catch(l){console.error("Error parsing JWT token:",l)}}e&&(y.firebase.projectId||s.project_id)?(localStorage.setItem("fcm_token",e),localStorage.setItem("fcm_token_registering","true"),this.http.post(`${y.apiUrl}/device-token`,s).subscribe({next:c=>{console.log("Token registered with backend:",c),localStorage.removeItem("fcm_token_registering")},error:c=>{console.error("Error registering token:",c),localStorage.removeItem("fcm_token_registering")}})):(console.log("Skipping token registration: Missing project ID or token"),e&&localStorage.setItem("fcm_token",e))}parseJwt(e){try{let i=e.split(".")[1].replace(/-/g,"+").replace(/_/g,"/"),a=decodeURIComponent(atob(i).split("").map(function(s){return"%"+("00"+s.charCodeAt(0).toString(16)).slice(-2)}).join(""));return JSON.parse(a)}catch(r){return console.error("Error parsing JWT token:",r),null}}setupNotificationListeners(){this.setupCapacitorNotificationListeners(),this.setupCordovaNotificationListeners()}setupCapacitorNotificationListeners(){try{this.platform.is("capacitor")&&(console.log("Setting up Capacitor Firebase Messaging notification listeners"),d.addListener("notificationReceived",e=>{console.log("Capacitor: Notification received in foreground:",e);let r=e.notification.data||{};this.processNotification(m({title:e.notification.title||"",body:e.notification.body||"",category:r.category||"",severity:r.severity||"low",wasTapped:!1,notification_id:r.notification_id||null,time:new Date().toISOString()},Object.keys(r).filter(i=>!["category","severity","notification_id"].includes(i)).reduce((i,a)=>(i[a]=r[a],i),{})))}),d.addListener("notificationActionPerformed",e=>{console.log("Capacitor: Notification tapped:",e);let r=e.notification.data||{};this.processNotification(m({title:e.notification.title||"",body:e.notification.body||"",category:r.category||"",severity:r.severity||"low",wasTapped:!0,notification_id:r.notification_id||null,time:new Date().toISOString()},Object.keys(r).filter(i=>!["category","severity","notification_id"].includes(i)).reduce((i,a)=>(i[a]=r[a],i),{})))}))}catch(e){console.error("Failed to set up Capacitor notification listeners:",e)}}setupCordovaNotificationListeners(){try{this.platform.is("cordova")&&(console.log("Setting up Cordova FCM notification listeners"),this.fcm.onNotification().subscribe({next:e=>{console.log("Cordova FCM notification received:",e);let r=m({},e);this.processNotification(m({title:e.title||e.aps&&e.aps.alert&&e.aps.alert.title||"",body:e.body||e.aps&&e.aps.alert&&e.aps.alert.body||e.message||"",category:e.category||"",severity:e.severity||"low",wasTapped:e.wasTapped||!1,notification_id:e.notification_id||null,time:e.time||new Date().toISOString()},Object.keys(r).filter(i=>!["title","body","category","severity","wasTapped","notification_id","time"].includes(i)).reduce((i,a)=>(i[a]=r[a],i),{})))},error:e=>{console.error("Error in Cordova FCM notification subscription:",e)}}))}catch(e){console.error("Failed to set up Cordova FCM notification listeners:",e)}}processNotification(e){try{console.log("Processed notification:",{title:e.title,body:e.body,category:e.category,severity:e.severity,wasTapped:e.wasTapped,notification_id:e.notification_id,project_id:y.firebase.projectId||"new-firebase-project"}),this.notificationSubject.next(e),e.wasTapped?(console.log("Notification tapped in background"),this.handleBackgroundNotification(e)):(console.log("Notification received in foreground"),this.handleForegroundNotification(e))}catch(r){console.error("Error processing notification:",r,e)}}handleForegroundNotification(e){return g(this,null,function*(){this.vibrateDevice(),this.playNotificationSound(),e.severity==="high"?yield(yield this.alertCtrl.create({header:e.title||"IMPORTANT ALERT",subHeader:e.category?`${e.category.toUpperCase()} ALERT`:"",message:e.body||"",buttons:[{text:"View Details",handler:()=>{this.navigateBasedOnNotification(e)}},{text:"Dismiss",role:"cancel"}],cssClass:"emergency-alert"})).present():yield(yield this.toastCtrl.create({header:e.title||"New Notification",message:e.body||"",position:"top",duration:4e3,buttons:[{text:"View",role:"info",handler:()=>{this.navigateBasedOnNotification(e)}},{text:"Dismiss",role:"cancel"}]})).present()})}vibrateDevice(){"vibrate"in navigator?(navigator.vibrate([1e3,200,1e3,200,1e3]),console.log("Device vibration triggered with strong pattern")):console.log("Vibration API not supported on this device")}playNotificationSound(){try{let e=new Audio;e.src="data:audio/wav;base64,UklGRl9vT19XQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YU...",e.volume=1,e.play().catch(r=>{console.error("Error playing notification sound:",r)})}catch(e){console.error("Error creating audio element:",e)}}handleBackgroundNotification(e){this.vibrateDevice(),this.navigateBasedOnNotification(e)}navigateBasedOnNotification(e){if(e.category)switch(e.category.toLowerCase()){case"flood":case"earthquake":case"fire":case"typhoon":this.router.navigate(["/tabs/home"],{queryParams:{disasterType:e.category,filterMode:"true"}});break;default:this.router.navigate(["/tabs/home"]);break}else this.router.navigate(["/tabs/home"])}checkGooglePlayServices(){return g(this,null,function*(){try{if(this.platform.is("capacitor")&&this.platform.is("android"))try{return yield d.getToken(),!0}catch(e){console.error("Error checking Google Play Services:",e);let r=e.message||"";return!(r.includes("Google Play Services")||r.includes("GoogleApiAvailability")||r.includes("API unavailable"))}return!0}catch(e){return console.error("Error in checkGooglePlayServices:",e),!0}})}createAndroidNotificationChannels(){return g(this,null,function*(){try{this.platform.is("android")&&(console.log("Creating Android notification channels"),yield this.sendTestChannelNotification("emergency-alerts","Emergency Alerts","High priority notifications for emergencies","high"),yield this.sendTestChannelNotification("general-notifications","General Notifications","Standard notifications","default"),console.log("Android notification channels created successfully"))}catch(e){console.error("Error creating Android notification channels:",e)}})}sendTestChannelNotification(e,r,i,a){return g(this,null,function*(){try{let s={notification:{title:"Channel Setup",body:"Setting up notification channels",android:{channelId:e,priority:a==="high"?"high":a==="default"?"default":"low",sound:a!=="low",vibrate:a!=="low",visibility:"public"}}};console.log(`Created notification channel: ${e} (${r})`)}catch(s){console.error(`Error creating notification channel ${e}:`,s)}})}refreshFCMToken(e){return g(this,null,function*(){try{if(console.log("Refreshing FCM token..."),this.platform.is("capacitor"))try{yield d.deleteToken(),console.log("Existing FCM token deleted");let r=yield d.getToken();return console.log("New FCM token obtained:",r.token),this.registerTokenWithBackend(r.token,e),!0}catch(r){return console.error("Error refreshing Capacitor FCM token:",r),!1}else if(this.platform.is("cordova"))try{let r=yield this.fcm.getToken();return console.log("New FCM token obtained from Cordova:",r),this.registerTokenWithBackend(r,e),!0}catch(r){return console.error("Error refreshing Cordova FCM token:",r),!1}else{let r="browser-mock-token-"+Math.random().toString(36).substring(2,15);return console.log("New mock FCM token generated:",r),this.registerTokenWithBackend(r,e),!0}}catch(r){return console.error("Error in refreshFCMToken:",r),!1}})}};t.\u0275fac=function(r){return new(r||t)(p(q),p(S),p(R),p(D),p(E),p(I))},t.\u0275prov=w({token:t,factory:t.\u0275fac,providedIn:"root"});let o=t;return o})();export{q as a,Be as b};
