<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\DeviceToken;
use Illuminate\Foundation\Testing\RefreshDatabase;

class DeviceTokenControllerTest extends TestCase
{
    use RefreshDatabase;

    public function test_register_token_success()
    {
        $response = $this->postJson('/api/fcm/tokens', [
            'token' => 'test_token_123',
            'device_type' => 'android',
            'project_id' => 'test_project'
        ]);

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => 'Device token registered successfully'
            ]);

        $this->assertDatabaseHas('device_tokens', [
            'token' => 'test_token_123',
            'device_type' => 'android',
            'project_id' => 'test_project',
            'is_active' => true
        ]);
    }

    public function test_register_token_validation()
    {
        $response = $this->postJson('/api/fcm/tokens', [
            'token' => '',
            'device_type' => 'invalid',
            'project_id' => ''
        ]);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['token', 'device_type', 'project_id']);
    }

    public function test_register_token_duplicate()
    {
        // Create existing token
        DeviceToken::create([
            'token' => 'test_token_123',
            'device_type' => 'android',
            'project_id' => 'test_project',
            'is_active' => true
        ]);

        $response = $this->postJson('/api/fcm/tokens', [
            'token' => 'test_token_123',
            'device_type' => 'android',
            'project_id' => 'test_project'
        ]);

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => 'Device token already registered'
            ]);

        $this->assertDatabaseCount('device_tokens', 1);
    }

    public function test_unregister_token_success()
    {
        // Create test token
        $token = DeviceToken::create([
            'token' => 'test_token_123',
            'device_type' => 'android',
            'project_id' => 'test_project',
            'is_active' => true
        ]);

        $response = $this->deleteJson('/api/fcm/tokens', [
            'token' => 'test_token_123',
            'project_id' => 'test_project'
        ]);

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => 'Device token unregistered successfully'
            ]);

        $this->assertFalse($token->fresh()->is_active);
        $this->assertNotNull($token->fresh()->deactivated_at);
    }

    public function test_unregister_token_not_found()
    {
        $response = $this->deleteJson('/api/fcm/tokens', [
            'token' => 'non_existent_token',
            'project_id' => 'test_project'
        ]);

        $response->assertStatus(404)
            ->assertJson([
                'success' => false,
                'message' => 'Device token not found'
            ]);
    }

    public function test_unregister_token_validation()
    {
        $response = $this->deleteJson('/api/fcm/tokens', [
            'token' => '',
            'project_id' => ''
        ]);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['token', 'project_id']);
    }

    public function test_get_tokens_by_project()
    {
        // Create tokens for different projects
        DeviceToken::create([
            'token' => 'project1_token',
            'device_type' => 'android',
            'project_id' => 'project1',
            'is_active' => true
        ]);

        DeviceToken::create([
            'token' => 'project2_token',
            'device_type' => 'android',
            'project_id' => 'project2',
            'is_active' => true
        ]);

        $response = $this->getJson('/api/fcm/tokens/project1');

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'data' => [
                    [
                        'token' => 'project1_token',
                        'device_type' => 'android',
                        'project_id' => 'project1',
                        'is_active' => true
                    ]
                ]
            ]);
    }

    public function test_get_tokens_by_project_not_found()
    {
        $response = $this->getJson('/api/fcm/tokens/non_existent_project');

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'data' => []
            ]);
    }

    public function test_get_tokens_by_device_type()
    {
        // Create tokens for different device types
        DeviceToken::create([
            'token' => 'android_token',
            'device_type' => 'android',
            'project_id' => 'test_project',
            'is_active' => true
        ]);

        DeviceToken::create([
            'token' => 'ios_token',
            'device_type' => 'ios',
            'project_id' => 'test_project',
            'is_active' => true
        ]);

        $response = $this->getJson('/api/fcm/tokens/device/android');

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'data' => [
                    [
                        'token' => 'android_token',
                        'device_type' => 'android',
                        'project_id' => 'test_project',
                        'is_active' => true
                    ]
                ]
            ]);
    }

    public function test_get_tokens_by_device_type_not_found()
    {
        $response = $this->getJson('/api/fcm/tokens/device/invalid_device');

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'data' => []
            ]);
    }
} 