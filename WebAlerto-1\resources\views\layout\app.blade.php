<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ALERTO</title>

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>

    <!-- Leaflet CSS -->
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.3/dist/leaflet.css" />

    <!-- Geocoder CSS -->
    <link rel="stylesheet" href="https://unpkg.com/leaflet-control-geocoder/dist/Control.Geocoder.css" />

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css" crossorigin="anonymous" />

    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    @yield('head')
</head>
<body class="bg-gradient-to-br from-gray-100 to-blue-50 min-h-screen flex">

    <!-- Mobile Sidebar Overlay -->
    <div id="mobileSidebarOverlay" class="fixed inset-0 bg-black bg-opacity-40 z-40 hidden" onclick="toggleSidebar()"></div>

    <!-- Sidebar -->
    <aside id="sidebar" class="fixed top-0 left-0 h-screen w-64 bg-white flex flex-col shadow-lg transform -translate-x-full md:translate-x-0 transition-transform duration-300 z-50">
        <div class="flex items-center justify-center h-20 border-b">
            <span class="text-3xl font-bold text-red-600 flex items-center gap-2">
                <i class="fa-solid fa-paper-plane text-blue-500"></i> ALERTO
            </span>
        </div>
        <nav class="flex-1 p-6 space-y-4 overflow-y-auto">
            <a href="{{ route('components.dashboard') }}" class="flex items-center gap-3 text-gray-700 hover:bg-red-100 p-3 rounded transition {{ Request::is('dashboard') ? 'bg-red-100 font-bold text-red-600' : '' }}">
                <i class="fa-solid fa-house"></i> <span class="hidden md:inline">Dashboard</span>
            </a>
            <a href="{{ route('components.evacuation_management.evacuation-dashboard') }}" class="flex items-center gap-3 text-gray-700 hover:bg-red-100 p-3 rounded transition {{ Request::is('evac-management') ? 'bg-red-100 font-bold text-red-600' : '' }}">
                <i class="fa-solid fa-building"></i> <span class="hidden md:inline">Evacuation Centers</span>
            </a>
            <a href="{{ route('map') }}" class="flex items-center gap-3 text-gray-700 hover:bg-red-100 p-3 rounded transition">
                <i class="fa-solid fa-map"></i> <span class="hidden md:inline">Map</span>
            </a>
            <a href="{{ route('components.user-management') }}" class="flex items-center gap-3 text-gray-700 hover:bg-red-100 p-3 rounded transition {{ Request::is('user-management') ? 'bg-red-100 font-bold text-red-600' : '' }}">
                <i class="fa-solid fa-user-group"></i> <span class="hidden md:inline">Users</span>
            </a>
            <a href="{{ route('components.notification.index') }}" class="flex items-center gap-3 text-gray-700 hover:bg-red-100 p-3 rounded transition {{ Request::is('notification*') ? 'bg-red-100 font-bold text-red-600' : '' }}">
                <i class="fa-solid fa-envelope-open-text"></i> <span class="hidden md:inline">Notification</span>
            </a>
            <a href="{{ route('test-fcm') }}" class="flex items-center gap-3 text-gray-700 hover:bg-red-100 p-3 rounded transition {{ Request::is('test-fcm*') ? 'bg-red-100 font-bold text-red-600' : '' }}">
                <i class="fa-solid fa-bell"></i> <span class="hidden md:inline">Test FCM</span>
            </a>
        </nav>
        <div class="p-6 border-t text-center text-xs text-gray-400">
            &copy; {{ date('Y') }} ALERTO
        </div>
    </aside>

    <!-- Main Content -->
<div class="flex-1 flex flex-col min-h-screen md:ml-64">

        <!-- Top Bar -->
        <header class="bg-white shadow flex justify-between items-center px-4 py-4 md:px-8">
            <button class="md:hidden text-2xl text-gray-700" onclick="toggleSidebar()">
                <i class="fa-solid fa-bars"></i>
            </button>
            <div class="flex items-center gap-4">
                <h1 class="text-lg md:text-xl font-bold text-gray-700">Admin Dashboard</h1>
            </div>
            <div class="flex items-center gap-4">
                <div class="hidden sm:block text-sm text-gray-600">Welcome, Admin</div>
                <img src="https://ui-avatars.com/api/?name=Admin&background=F87171&color=fff" alt="Admin Avatar" class="h-9 w-9 rounded-full border-2 border-red-200 shadow">
                <a href="{{ route('logout') }}" class="text-red-600 font-semibold hover:bg-red-100 px-3 py-2 rounded transition">Logout</a>
            </div>
        </header>

        <!-- Page Content -->
        <main class="flex-1 overflow-y-auto p-4 md:p-6">
            @yield('content')
        </main>
    </div>

    <!-- Scripts -->
    <script src="https://unpkg.com/leaflet@1.9.3/dist/leaflet.js"></script>
    <script src="https://unpkg.com/leaflet-control-geocoder/dist/Control.Geocoder.js"></script>

    <script>
        function toggleSidebar() {
            const sidebar = document.getElementById('sidebar');
            const overlay = document.getElementById('mobileSidebarOverlay');
            if (sidebar.classList.contains('-translate-x-full')) {
                sidebar.classList.remove('-translate-x-full');
                overlay.classList.remove('hidden');
            } else {
                sidebar.classList.add('-translate-x-full');
                overlay.classList.add('hidden');
            }
        }
    </script>

    @yield('scripts')

</body>
</html>
