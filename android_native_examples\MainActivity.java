package com.yourpackage.webalerto;

import android.Manifest;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.os.Build;
import android.os.Bundle;
import android.util.Log;
import android.view.View;
import android.widget.Button;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.google.android.gms.tasks.OnCompleteListener;
import com.google.android.gms.tasks.Task;
import com.google.firebase.FirebaseApp;
import com.google.firebase.messaging.FirebaseMessaging;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;

public class MainActivity extends AppCompatActivity {
    private static final String TAG = "MainActivity";
    private static final int PERMISSION_REQUEST_CODE = 1001;
    
    private ApiService apiService;
    private Button btnTestNotification;
    private Button btnViewEvacuationCenters;
    private Button btnRefreshToken;
    private RecyclerView recyclerViewAlerts;
    private DisasterAlertAdapter alertAdapter;
    private List<DisasterAlert> alertList;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_main);

        initializeViews();
        initializeServices();
        requestPermissions();
        setupFirebase();
        setupRecyclerView();
        setupClickListeners();
        
        // Check if app was opened from notification
        handleNotificationIntent(getIntent());
    }

    private void initializeViews() {
        btnTestNotification = findViewById(R.id.btn_test_notification);
        btnViewEvacuationCenters = findViewById(R.id.btn_view_evacuation_centers);
        btnRefreshToken = findViewById(R.id.btn_refresh_token);
        recyclerViewAlerts = findViewById(R.id.recycler_view_alerts);
    }

    private void initializeServices() {
        apiService = new ApiService();
        alertList = new ArrayList<>();
    }

    private void requestPermissions() {
        List<String> permissionsToRequest = new ArrayList<>();

        // Check for location permissions
        if (ContextCompat.checkSelfPermission(this, Manifest.permission.ACCESS_FINE_LOCATION) 
                != PackageManager.PERMISSION_GRANTED) {
            permissionsToRequest.add(Manifest.permission.ACCESS_FINE_LOCATION);
        }

        if (ContextCompat.checkSelfPermission(this, Manifest.permission.ACCESS_COARSE_LOCATION) 
                != PackageManager.PERMISSION_GRANTED) {
            permissionsToRequest.add(Manifest.permission.ACCESS_COARSE_LOCATION);
        }

        // Check for notification permission (Android 13+)
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            if (ContextCompat.checkSelfPermission(this, Manifest.permission.POST_NOTIFICATIONS) 
                    != PackageManager.PERMISSION_GRANTED) {
                permissionsToRequest.add(Manifest.permission.POST_NOTIFICATIONS);
            }
        }

        if (!permissionsToRequest.isEmpty()) {
            ActivityCompat.requestPermissions(this, 
                permissionsToRequest.toArray(new String[0]), 
                PERMISSION_REQUEST_CODE);
        }
    }

    private void setupFirebase() {
        // Initialize Firebase
        FirebaseApp.initializeApp(this);

        // Get FCM token
        FirebaseMessaging.getInstance().getToken()
            .addOnCompleteListener(new OnCompleteListener<String>() {
                @Override
                public void onComplete(@NonNull Task<String> task) {
                    if (!task.isSuccessful()) {
                        Log.w(TAG, "Fetching FCM registration token failed", task.getException());
                        showToast("Failed to get FCM token");
                        return;
                    }

                    // Get new FCM registration token
                    String token = task.getResult();
                    Log.d(TAG, "FCM Registration Token: " + token);

                    // Register token with backend
                    registerTokenWithBackend(token);
                }
            });
    }

    private void setupRecyclerView() {
        alertAdapter = new DisasterAlertAdapter(alertList);
        recyclerViewAlerts.setLayoutManager(new LinearLayoutManager(this));
        recyclerViewAlerts.setAdapter(alertAdapter);
    }

    private void setupClickListeners() {
        btnTestNotification.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                sendTestNotification();
            }
        });

        btnViewEvacuationCenters.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                loadEvacuationCenters();
            }
        });

        btnRefreshToken.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                refreshFCMToken();
            }
        });
    }

    private void registerTokenWithBackend(String token) {
        apiService.registerDeviceToken(token, new ApiService.ApiCallback() {
            @Override
            public void onSuccess(String response) {
                runOnUiThread(() -> {
                    Log.d(TAG, "Token registered successfully: " + response);
                    showToast("Device registered for notifications");
                });
            }

            @Override
            public void onError(String error) {
                runOnUiThread(() -> {
                    Log.e(TAG, "Failed to register token: " + error);
                    showToast("Failed to register device: " + error);
                });
            }
        });
    }

    private void sendTestNotification() {
        apiService.sendTestNotification(
            "Test Emergency Alert",
            "This is a test disaster notification from WebAlerto",
            "high",
            "earthquake",
            new ApiService.ApiCallback() {
                @Override
                public void onSuccess(String response) {
                    runOnUiThread(() -> {
                        showToast("Test notification sent successfully");
                        Log.d(TAG, "Test notification response: " + response);
                    });
                }

                @Override
                public void onError(String error) {
                    runOnUiThread(() -> {
                        showToast("Failed to send test notification: " + error);
                        Log.e(TAG, "Test notification error: " + error);
                    });
                }
            }
        );
    }

    private void loadEvacuationCenters() {
        apiService.getEvacuationCenters(new ApiService.ApiCallback() {
            @Override
            public void onSuccess(String response) {
                runOnUiThread(() -> {
                    try {
                        JSONObject jsonResponse = new JSONObject(response);
                        JSONArray centers = jsonResponse.getJSONArray("data");
                        
                        // Start EvacuationCentersActivity with the data
                        Intent intent = new Intent(MainActivity.this, EvacuationCentersActivity.class);
                        intent.putExtra("evacuation_centers", centers.toString());
                        startActivity(intent);
                        
                    } catch (JSONException e) {
                        Log.e(TAG, "Error parsing evacuation centers response", e);
                        showToast("Error loading evacuation centers");
                    }
                });
            }

            @Override
            public void onError(String error) {
                runOnUiThread(() -> {
                    showToast("Failed to load evacuation centers: " + error);
                    Log.e(TAG, "Evacuation centers error: " + error);
                });
            }
        });
    }

    private void refreshFCMToken() {
        FirebaseMessaging.getInstance().deleteToken()
            .addOnCompleteListener(new OnCompleteListener<Void>() {
                @Override
                public void onComplete(@NonNull Task<Void> task) {
                    if (task.isSuccessful()) {
                        // Get new token
                        FirebaseMessaging.getInstance().getToken()
                            .addOnCompleteListener(new OnCompleteListener<String>() {
                                @Override
                                public void onComplete(@NonNull Task<String> task) {
                                    if (task.isSuccessful()) {
                                        String newToken = task.getResult();
                                        Log.d(TAG, "New FCM token: " + newToken);
                                        registerTokenWithBackend(newToken);
                                        showToast("FCM token refreshed");
                                    } else {
                                        showToast("Failed to get new FCM token");
                                    }
                                }
                            });
                    } else {
                        showToast("Failed to delete old FCM token");
                    }
                }
            });
    }

    private void handleNotificationIntent(Intent intent) {
        if (intent != null && intent.getExtras() != null) {
            String title = intent.getStringExtra("title");
            String message = intent.getStringExtra("message");
            String severity = intent.getStringExtra("severity");
            String category = intent.getStringExtra("category");

            if (title != null && message != null) {
                // Add the notification to the alerts list
                DisasterAlert alert = new DisasterAlert(title, message, category, severity, System.currentTimeMillis());
                alertList.add(0, alert); // Add to top of list
                if (alertAdapter != null) {
                    alertAdapter.notifyItemInserted(0);
                    recyclerViewAlerts.scrollToPosition(0);
                }
            }
        }
    }

    @Override
    protected void onNewIntent(Intent intent) {
        super.onNewIntent(intent);
        handleNotificationIntent(intent);
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        
        if (requestCode == PERMISSION_REQUEST_CODE) {
            boolean allPermissionsGranted = true;
            for (int result : grantResults) {
                if (result != PackageManager.PERMISSION_GRANTED) {
                    allPermissionsGranted = false;
                    break;
                }
            }
            
            if (allPermissionsGranted) {
                showToast("All permissions granted");
            } else {
                showToast("Some permissions were denied. App functionality may be limited.");
            }
        }
    }

    private void showToast(String message) {
        Toast.makeText(this, message, Toast.LENGTH_SHORT).show();
    }
}
