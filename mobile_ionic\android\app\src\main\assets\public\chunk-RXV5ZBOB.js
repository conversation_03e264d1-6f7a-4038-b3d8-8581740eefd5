import{a as ne}from"./chunk-SV7S5NYR.js";import{a as ee,b as pe,c as be}from"./chunk-WTCPO44B.js";var te,un=r=>{if(te===void 0){let c=r.style.animationName!==void 0,s=r.style.webkitAnimationName!==void 0;te=!c&&s?"-webkit-":""}return te},ie=(r,c,s)=>{let y=c.startsWith("animation")?un(r):"";r.style.setProperty(y+c,s)},z=(r=[],c)=>{if(c!==void 0){let s=Array.isArray(c)?c:[c];return[...r,...s]}return r},yn=r=>{let c,s,y,m,w,b,a=[],v=[],K=[],A=!1,f,N={},Y=[],H=[],$={},F=0,D=!1,x=!1,L,_,k,P=!0,I=!1,W=!0,t,M=!1,re=r,G=[],T=[],O=[],p=[],u=[],oe=[],se=[],ae=[],ce=[],fe=[],h=[],Ae=typeof AnimationEffect=="function"||ne!==void 0&&typeof ne.AnimationEffect=="function",g=typeof Element=="function"&&typeof Element.prototype.animate=="function"&&Ae,le=()=>h,Ce=e=>(u.forEach(n=>{n.destroy(e)}),we(e),p.length=0,u.length=0,a.length=0,Pe(),A=!1,W=!0,t),we=e=>{de(),e&&Se()},Fe=()=>{D=!1,x=!1,W=!0,L=void 0,_=void 0,k=void 0,F=0,I=!1,P=!0,M=!1},Le=()=>F!==0&&!M,ue=(e,n)=>{let i=n.findIndex(o=>o.c===e);i>-1&&n.splice(i,1)},ke=(e,n)=>(O.push({c:e,o:n}),t),Z=(e,n)=>((n!=null&&n.oneTimeCallback?T:G).push({c:e,o:n}),t),Pe=()=>(G.length=0,T.length=0,t),de=()=>{g&&(h.forEach(e=>{e.cancel()}),h.length=0)},Se=()=>{oe.forEach(e=>{e!=null&&e.parentNode&&e.parentNode.removeChild(e)}),oe.length=0},_e=e=>(se.push(e),t),Ie=e=>(ae.push(e),t),We=e=>(ce.push(e),t),Te=e=>(fe.push(e),t),Ve=e=>(v=z(v,e),t),Re=e=>(K=z(K,e),t),De=(e={})=>(N=e,t),xe=(e=[])=>{for(let n of e)N[n]="";return t},Me=e=>(Y=z(Y,e),t),Oe=e=>(H=z(H,e),t),Ue=(e={})=>($=e,t),Be=(e=[])=>{for(let n of e)$[n]="";return t},J=()=>w!==void 0?w:f?f.getFill():"both",U=()=>L!==void 0?L:b!==void 0?b:f?f.getDirection():"normal",Q=()=>D?"linear":y!==void 0?y:f?f.getEasing():"linear",S=()=>x?0:_!==void 0?_:s!==void 0?s:f?f.getDuration():0,X=()=>m!==void 0?m:f?f.getIterations():1,j=()=>k!==void 0?k:c!==void 0?c:f?f.getDelay():0,ze=()=>a,qe=e=>(b=e,d(!0),t),Ke=e=>(w=e,d(!0),t),Ne=e=>(c=e,d(!0),t),Ye=e=>(y=e,d(!0),t),He=e=>(!g&&e===0&&(e=1),s=e,d(!0),t),$e=e=>(m=e,d(!0),t),Ge=e=>(f=e,t),Ze=e=>{if(e!=null)if(e.nodeType===1)p.push(e);else if(e.length>=0)for(let n=0;n<e.length;n++)p.push(e[n]);else be("createAnimation - Invalid addElement value.");return t},Je=e=>{if(e!=null)if(Array.isArray(e))for(let n of e)n.parent(t),u.push(n);else e.parent(t),u.push(e);return t},Qe=e=>{let n=a!==e;return a=e,n&&Xe(a),t},Xe=e=>{g&&le().forEach(n=>{let i=n.effect;if(i.setKeyframes)i.setKeyframes(e);else{let o=new KeyframeEffect(i.target,e,i.getTiming());n.effect=o}})},je=()=>{se.forEach(o=>o()),ae.forEach(o=>o());let e=v,n=K,i=N;p.forEach(o=>{let l=o.classList;e.forEach(E=>l.add(E)),n.forEach(E=>l.remove(E));for(let E in i)i.hasOwnProperty(E)&&ie(o,E,i[E])})},en=()=>{ce.forEach(l=>l()),fe.forEach(l=>l());let e=P?1:0,n=Y,i=H,o=$;p.forEach(l=>{let E=l.classList;n.forEach(C=>E.add(C)),i.forEach(C=>E.remove(C));for(let C in o)o.hasOwnProperty(C)&&ie(l,C,o[C])}),_=void 0,L=void 0,k=void 0,G.forEach(l=>l.c(e,t)),T.forEach(l=>l.c(e,t)),T.length=0,W=!0,P&&(I=!0),P=!0},B=()=>{F!==0&&(F--,F===0&&(en(),f&&f.animationFinish()))},nn=()=>{p.forEach(e=>{let n=e.animate(a,{id:re,delay:j(),duration:S(),easing:Q(),iterations:X(),fill:J(),direction:U()});n.pause(),h.push(n)}),h.length>0&&(h[0].onfinish=()=>{B()})},me=()=>{je(),a.length>0&&g&&nn(),A=!0},V=e=>{e=Math.min(Math.max(e,0),.9999),g&&h.forEach(n=>{n.currentTime=n.effect.getComputedTiming().delay+S()*e,n.pause()})},ge=e=>{h.forEach(n=>{n.effect.updateTiming({delay:j(),duration:S(),easing:Q(),iterations:X(),fill:J(),direction:U()})}),e!==void 0&&V(e)},d=(e=!1,n=!0,i)=>(e&&u.forEach(o=>{o.update(e,n,i)}),g&&ge(i),t),tn=(e=!1,n)=>(u.forEach(i=>{i.progressStart(e,n)}),he(),D=e,A||me(),d(!1,!0,n),t),rn=e=>(u.forEach(n=>{n.progressStep(e)}),V(e),t),on=(e,n,i)=>(D=!1,u.forEach(o=>{o.progressEnd(e,n,i)}),i!==void 0&&(_=i),I=!1,P=!0,e===0?(L=U()==="reverse"?"normal":"reverse",L==="reverse"&&(P=!1),g?(d(),V(1-n)):(k=(1-n)*S()*-1,d(!1,!1))):e===1&&(g?(d(),V(n)):(k=n*S()*-1,d(!1,!1))),e!==void 0&&!f&&Ee(),t),he=()=>{A&&(g?h.forEach(e=>{e.pause()}):p.forEach(e=>{ie(e,"animation-play-state","paused")}),M=!0)},sn=()=>(u.forEach(e=>{e.pause()}),he(),t),an=()=>{B()},cn=()=>{h.forEach(e=>{e.play()}),(a.length===0||p.length===0)&&B()},fn=()=>{g&&(V(0),ge())},Ee=e=>new Promise(n=>{e!=null&&e.sync&&(x=!0,Z(()=>x=!1,{oneTimeCallback:!0})),A||me(),I&&(fn(),I=!1),W&&(F=u.length+1,W=!1);let i=()=>{ue(o,T),n()},o=()=>{ue(i,O),n()};Z(o,{oneTimeCallback:!0}),ke(i,{oneTimeCallback:!0}),u.forEach(l=>{l.play()}),g?cn():an(),M=!1}),ln=()=>{u.forEach(e=>{e.stop()}),A&&(de(),A=!1),Fe(),O.forEach(e=>e.c(0,t)),O.length=0},ye=(e,n)=>{let i=a[0];return i!==void 0&&(i.offset===void 0||i.offset===0)?i[e]=n:a=[{offset:0,[e]:n},...a],t};return t={parentAnimation:f,elements:p,childAnimations:u,id:re,animationFinish:B,from:ye,to:(e,n)=>{let i=a[a.length-1];return i!==void 0&&(i.offset===void 0||i.offset===1)?i[e]=n:a=[...a,{offset:1,[e]:n}],t},fromTo:(e,n,i)=>ye(e,n).to(e,i),parent:Ge,play:Ee,pause:sn,stop:ln,destroy:Ce,keyframes:Qe,addAnimation:Je,addElement:Ze,update:d,fill:Ke,direction:qe,iterations:$e,duration:He,easing:Ye,delay:Ne,getWebAnimations:le,getKeyframes:ze,getFill:J,getDirection:U,getDelay:j,getIterations:X,getEasing:Q,getDuration:S,afterAddRead:We,afterAddWrite:Te,afterClearStyles:Be,afterStyles:Ue,afterRemoveClass:Oe,afterAddClass:Me,beforeAddRead:_e,beforeAddWrite:Ie,beforeClearStyles:xe,beforeStyles:De,beforeRemoveClass:Re,beforeAddClass:Ve,onFinish:Z,isRunning:Le,progressStart:tn,progressStep:rn,progressEnd:on}};var An="ionViewWillEnter",Cn="ionViewDidEnter",wn="ionViewWillLeave",Fn="ionViewDidLeave",Ln="ionViewWillUnload",R=r=>{r.tabIndex=-1,r.focus()},q=r=>r.offsetParent!==null,dn=()=>({saveViewFocus:s=>{if(ee.get("focusManagerPriority",!1)){let m=document.activeElement;m!==null&&(s!=null&&s.contains(m))&&m.setAttribute(ve,"true")}},setViewFocus:s=>{let y=ee.get("focusManagerPriority",!1);if(Array.isArray(y)&&!s.contains(document.activeElement)){let m=s.querySelector(`[${ve}]`);if(m&&q(m)){R(m);return}for(let w of y)switch(w){case"content":let b=s.querySelector('main, [role="main"]');if(b&&q(b)){R(b);return}break;case"heading":let a=s.querySelector('h1, [role="heading"][aria-level="1"]');if(a&&q(a)){R(a);return}break;case"banner":let v=s.querySelector('header, [role="banner"]');if(v&&q(v)){R(v);return}break;default:pe(`Unrecognized focus manager priority value ${w}`);break}R(s)}}}),ve="ion-last-focus";var kn=dn();var Pn=r=>{if(r.classList.contains("ion-page"))return r;let c=r.querySelector(":scope > .ion-page, :scope > ion-nav, :scope > ion-tabs");return c||r};export{yn as a,An as b,Cn as c,wn as d,Fn as e,Ln as f,Pn as g};
