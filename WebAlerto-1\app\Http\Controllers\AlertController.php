<?php

namespace App\Http\Controllers;

use App\Models\Alert;
use Illuminate\Http\Request;

class AlertController extends Controller
{
    // Fetch all alerts
    public function index()
    {
        $alerts = Alert::all();
        return response()->json($alerts);
    }

    // Create a new alert
    public function store(Request $request)
    {
        $validatedData = $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'required|string',
            'created_by' => 'required|exists:users,id', // Ensure the user exists
        ]);

        $alert = Alert::create($validatedData);

        return response()->json([
            'message' => 'Alert created successfully',
            'alert' => $alert,
        ], 201);
    }

    // Fetch a single alert
    public function show($id)
    {
        $alert = Alert::findOrFail($id);
        return response()->json($alert);
    }

    // Update an alert
    public function update(Request $request, $id)
    {
        $alert = Alert::findOrFail($id);

        $validatedData = $request->validate([
            'title' => 'sometimes|required|string|max:255',
            'description' => 'sometimes|required|string',
        ]);

        $alert->update($validatedData);

        return response()->json([
            'message' => 'Alert updated successfully',
            'alert' => $alert,
        ]);
    }

    // Delete an alert
    public function destroy($id)
    {
        $alert = Alert::findOrFail($id);
        $alert->delete();

        return response()->json([
            'message' => 'Alert deleted successfully',
        ]);
    }
}
