package io.ionic.starter;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.graphics.Color;
import android.os.Build;
import android.os.Bundle;
import android.os.Vibrator;
import android.util.Log;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.Button;
import android.widget.TextView;
import android.widget.ImageView;
import android.widget.LinearLayout;

public class EmergencyAlertActivity extends Activity {

    // Store view IDs for later access
    private int titleViewId;
    private int subtitleViewId;
    private int messageViewId;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        // Make this activity show over lock screen
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O_MR1) {
            setShowWhenLocked(true);
            setTurnScreenOn(true);
        } else {
            Window window = getWindow();
            window.addFlags(WindowManager.LayoutParams.FLAG_SHOW_WHEN_LOCKED |
                           WindowManager.LayoutParams.FLAG_TURN_SCREEN_ON |
                           WindowManager.LayoutParams.FLAG_DISMISS_KEYGUARD);
        }

        // Create the emergency alert layout programmatically
        createEmergencyLayout();

        // Get notification data from intent
        Intent intent = getIntent();
        String title = intent.getStringExtra("title");
        String message = intent.getStringExtra("message");
        String category = intent.getStringExtra("category");
        String severity = intent.getStringExtra("severity");

        // Update the UI with notification data
        updateAlertContent(title, message, category, severity);

        // Vibrate device for emergency alert
        vibrateDevice();
    }

    private void vibrateDevice() {
        try {
            Vibrator vibrator = (Vibrator) getSystemService(Context.VIBRATOR_SERVICE);
            if (vibrator != null && vibrator.hasVibrator()) {
                // Create emergency vibration pattern: long-short-long-short
                long[] pattern = {0, 1000, 200, 1000, 200, 1000};
                vibrator.vibrate(pattern, -1);
            }
        } catch (Exception e) {
            Log.e("EmergencyAlert", "Error vibrating device", e);
        }
    }

    private void createEmergencyLayout() {
        // Create main container
        LinearLayout mainLayout = new LinearLayout(this);
        mainLayout.setOrientation(LinearLayout.VERTICAL);
        mainLayout.setBackgroundColor(getDisasterColor("earthquake")); // Default orange
        mainLayout.setPadding(40, 80, 40, 40);

        // Create exclamation icon container
        LinearLayout iconContainer = new LinearLayout(this);
        iconContainer.setOrientation(LinearLayout.HORIZONTAL);
        iconContainer.setGravity(android.view.Gravity.CENTER);

        // Create exclamation mark
        TextView exclamationIcon = new TextView(this);
        exclamationIcon.setText("!");
        exclamationIcon.setTextSize(32);
        exclamationIcon.setTextColor(Color.WHITE);
        exclamationIcon.setBackgroundResource(android.R.drawable.btn_default);
        exclamationIcon.setBackgroundColor(Color.parseColor("#d32f2f"));
        exclamationIcon.setPadding(20, 10, 20, 10);
        exclamationIcon.setGravity(android.view.Gravity.CENTER);

        iconContainer.addView(exclamationIcon);
        mainLayout.addView(iconContainer);

        // Add spacing
        View spacer1 = new View(this);
        spacer1.setLayoutParams(new LinearLayout.LayoutParams(
            LinearLayout.LayoutParams.MATCH_PARENT, 30));
        mainLayout.addView(spacer1);

        // Create title
        TextView titleView = new TextView(this);
        titleViewId = View.generateViewId(); // Generate and store unique ID
        titleView.setId(titleViewId);
        titleView.setTextSize(24);
        titleView.setTextColor(Color.WHITE);
        titleView.setGravity(android.view.Gravity.CENTER);
        titleView.setTypeface(null, android.graphics.Typeface.BOLD);
        mainLayout.addView(titleView);

        // Create subtitle
        TextView subtitleView = new TextView(this);
        subtitleViewId = View.generateViewId(); // Generate and store unique ID
        subtitleView.setId(subtitleViewId);
        subtitleView.setTextSize(16);
        subtitleView.setTextColor(Color.parseColor("#f0f0f0"));
        subtitleView.setGravity(android.view.Gravity.CENTER);
        mainLayout.addView(subtitleView);

        // Add spacing
        View spacer2 = new View(this);
        spacer2.setLayoutParams(new LinearLayout.LayoutParams(
            LinearLayout.LayoutParams.MATCH_PARENT, 30));
        mainLayout.addView(spacer2);

        // Create message
        TextView messageView = new TextView(this);
        messageViewId = View.generateViewId(); // Generate and store unique ID
        messageView.setId(messageViewId);
        messageView.setTextSize(18);
        messageView.setTextColor(Color.WHITE);
        messageView.setGravity(android.view.Gravity.CENTER);
        messageView.setPadding(20, 20, 20, 20);
        messageView.setBackgroundColor(Color.parseColor("#33000000"));
        mainLayout.addView(messageView);

        // Add spacing
        View spacer3 = new View(this);
        spacer3.setLayoutParams(new LinearLayout.LayoutParams(
            LinearLayout.LayoutParams.MATCH_PARENT, 40));
        mainLayout.addView(spacer3);

        // Create button container
        LinearLayout buttonContainer = new LinearLayout(this);
        buttonContainer.setOrientation(LinearLayout.HORIZONTAL);
        buttonContainer.setGravity(android.view.Gravity.CENTER);

        // Create "Open App" button
        Button openAppButton = new Button(this);
        openAppButton.setText("Open App");
        openAppButton.setTextColor(Color.WHITE);
        openAppButton.setBackgroundColor(Color.parseColor("#33ffffff"));
        openAppButton.setPadding(40, 20, 40, 20);
        openAppButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                openMainApp();
            }
        });

        // Create "Dismiss" button
        Button dismissButton = new Button(this);
        dismissButton.setText("Dismiss");
        dismissButton.setTextColor(Color.WHITE);
        dismissButton.setBackgroundColor(Color.parseColor("#33ffffff"));
        dismissButton.setPadding(40, 20, 40, 20);
        dismissButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                finish();
            }
        });

        // Add buttons to container
        LinearLayout.LayoutParams buttonParams = new LinearLayout.LayoutParams(
            LinearLayout.LayoutParams.WRAP_CONTENT,
            LinearLayout.LayoutParams.WRAP_CONTENT);
        buttonParams.setMargins(10, 0, 10, 0);

        buttonContainer.addView(openAppButton, buttonParams);
        buttonContainer.addView(dismissButton, buttonParams);
        mainLayout.addView(buttonContainer);

        setContentView(mainLayout);
    }

    private void updateAlertContent(String title, String message, String category, String severity) {
        // Update background color based on disaster type
        LinearLayout mainLayout = (LinearLayout) findViewById(android.R.id.content).getRootView();
        if (mainLayout instanceof LinearLayout) {
            mainLayout.setBackgroundColor(getDisasterColor(category));
        }

        // Update title
        TextView titleView = findViewById(titleViewId);
        if (titleView != null) {
            titleView.setText(title != null ? title.toUpperCase() : "EMERGENCY ALERT");
        }

        // Update subtitle
        TextView subtitleView = findViewById(subtitleViewId);
        if (subtitleView != null) {
            String subtitle = category != null ? category.toUpperCase() + " ALERT" : "EMERGENCY ALERT";
            subtitleView.setText(subtitle);
        }

        // Update message
        TextView messageView = findViewById(messageViewId);
        if (messageView != null) {
            messageView.setText(message != null ? message : "Emergency notification received");
        }
    }

    private int getDisasterColor(String category) {
        if (category == null) return Color.parseColor("#FFA500"); // Default orange

        switch (category.toLowerCase()) {
            case "earthquake":
                return Color.parseColor("#FFA500"); // Orange
            case "flood":
                return Color.parseColor("#0066CC"); // Blue
            case "typhoon":
                return Color.parseColor("#008000"); // Green
            case "fire":
                return Color.parseColor("#FF0000"); // Red
            default:
                return Color.parseColor("#FFA500"); // Default orange
        }
    }

    private void openMainApp() {
        Intent intent = new Intent(this, MainActivity.class);
        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TOP);

        // Get notification data from intent
        Intent currentIntent = getIntent();
        String category = currentIntent.getStringExtra("category");

        // Add navigation data to open map with disaster filter
        if (category != null) {
            intent.putExtra("navigate_to", "map");
            intent.putExtra("disaster_type", category.toLowerCase());
            intent.putExtra("filter_mode", "true");
            intent.putExtra("from_notification", "true");
        }

        startActivity(intent);
        finish();
    }

    @Override
    public void onBackPressed() {
        // Prevent back button from closing emergency alert
        // User must tap dismiss or open app
    }
}
