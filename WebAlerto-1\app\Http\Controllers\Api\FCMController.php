<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\DeviceToken;
use App\Models\Notification;
use App\Services\FCMService;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\RateLimiter;
use Illuminate\Support\Facades\Cache;

class FCMController extends Controller
{
    protected $fcmService;
    protected $maxNotificationsPerMinute = 60;
    protected $maxTestNotificationsPerMinute = 10;

    public function __construct(FCMService $fcmService)
    {
        $this->fcmService = $fcmService;
    }

    public function storeToken(Request $request)
    {
        $request->validate([
            'token' => 'required|string',
            'user_id' => 'nullable|integer',
            'device_type' => 'nullable|string|in:android,ios,web',
        ]);

        $token = DeviceToken::updateOrCreate(
            [
                'token' => $request->token,
            ],
            [
                'user_id' => $request->user_id,
                'device_type' => $request->device_type ?? 'android',
                'is_active' => true,
            ]
        );

        return response()->json([
            'success' => true,
            'message' => 'Token saved successfully',
            'data' => $token
        ], 201);
    }

    /**
     * Test sending a notification to a specific token or all tokens
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function testNotification(Request $request)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'body' => 'required|string',
            'category' => 'nullable|string',
            'severity' => 'nullable|string|in:low,medium,high',
            'token' => 'nullable|string',
        ]);

        // Create a notification record
        $notification = Notification::create([
            'title' => $request->title,
            'message' => $request->body,
            'category' => $request->category ?? 'General',
            'severity' => $request->severity ?? 'medium',
            'sent' => false
        ]);

        // Get tokens - either the specific token or all active tokens
        $tokens = [];
        if ($request->has('token') && $request->token) {
            $tokens = [$request->token];

            // Make sure the token is registered
            DeviceToken::updateOrCreate(
                ['token' => $request->token],
                ['is_active' => true]
            );
        }

        try {
            // Send notification using FCM service
            $result = $this->fcmService->sendNotification($notification, $tokens);

            if (!$result['success']) {
                return response()->json([
                    'success' => false,
                    'message' => $result['message'],
                ], 500);
            }

            return response()->json([
                'success' => true,
                'message' => 'Test notification sent successfully',
                'notification_id' => $notification->id,
                'tokens_count' => count($tokens),
                'success_count' => $result['success_count'],
                'failure_count' => $result['failure_count'],
                'invalid_tokens' => $result['invalid_tokens']
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to send test notification', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to send test notification: ' . $e->getMessage()
            ], 500);
        }
    }

    public function sendNotification(Request $request)
    {
        $request->validate([
            'title' => 'required|string',
            'body' => 'required|string',
            'category' => 'nullable|string',
            'severity' => 'nullable|string|in:low,medium,high',
            'device_type' => 'nullable|string|in:android,ios,web,all',
            'data' => 'nullable|array',
            'user_id' => 'nullable|integer',
        ]);

        try {
            // Create a notification record in the database first
            $notification = Notification::create([
                'title' => $request->title,
                'message' => $request->body,
                'category' => $request->category ?? 'general',
                'severity' => $request->severity ?? 'medium',
                'sent' => false
            ]);

            // Query to get active tokens
            $query = DeviceToken::where('is_active', true);

            // Filter by device type if specified
            if ($request->has('device_type') && $request->device_type !== 'all') {
                $query->where('device_type', $request->device_type);
            }

            // Filter by user_id if specified
            if ($request->has('user_id') && $request->user_id) {
                $query->where('user_id', $request->user_id);
            }

            $tokens = $query->pluck('token')->toArray();

            if (empty($tokens)) {
                return response()->json([
                    'success' => false,
                    'message' => 'No active device tokens found',
                ], 404);
            }

            // Send notification using FCM service
            $result = $this->fcmService->sendNotification($notification, $tokens);

            if (!$result['success']) {
                return response()->json([
                    'success' => false,
                    'message' => $result['message'],
                ], 500);
            }

            return response()->json([
                'success' => true,
                'message' => 'Notification sent successfully',
                'notification_id' => $notification->id,
                'tokens_count' => count($tokens),
                'success_count' => $result['success_count'],
                'failure_count' => $result['failure_count'],
                'invalid_tokens' => $result['invalid_tokens']
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to send notification', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to send notification',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    public function sendTestNotification(Request $request)
    {
        try {
            // Rate limiting for test notifications
            $key = 'test_notification_' . $request->ip();
            if (RateLimiter::tooManyAttempts($key, $this->maxTestNotificationsPerMinute)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Too many test notifications. Please try again later.',
                    'retry_after' => RateLimiter::availableIn($key)
                ], 429);
            }
            RateLimiter::hit($key);

            // Validate request
            $validator = Validator::make($request->all(), [
                'title' => 'required|string|max:255',
                'message' => 'required|string|max:1000',
                'severity' => 'nullable|string|in:low,normal,high',
                'category' => 'nullable|string|max:50',
                'data' => 'nullable|array',
                'sound' => 'nullable|string|max:50',
                'badge' => 'nullable|string|max:10',
                'icon' => 'nullable|string|max:255',
                'color' => 'nullable|string|max:50',
                'tag' => 'nullable|string|max:50',
                'group' => 'nullable|string|max:50',
                'silent' => 'nullable|boolean',
                'vibrate' => 'nullable|boolean',
                'actions' => 'nullable|array'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            // Create notification
            $notification = Notification::create([
                'title' => $request->title,
                'message' => $request->message,
                'severity' => $request->severity ?? 'normal',
                'category' => $request->category,
                'data' => $request->data,
                'sound' => $request->sound,
                'badge' => $request->badge ?? '1',
                'icon' => $request->icon,
                'color' => $request->color,
                'tag' => $request->tag,
                'group' => $request->group,
                'silent' => $request->silent ?? false,
                'vibrate' => $request->vibrate ?? true,
                'actions' => $request->actions
            ]);

            // Send notification
            $result = $this->fcmService->sendNotification($notification);

            if (!$result['success']) {
                $notification->markAsFailed($result['message']);
                return response()->json([
                    'success' => false,
                    'message' => $result['message'],
                    'error' => $result['message']
                ], 500);
            }

            $notification->markAsSent();

            return response()->json([
                'success' => true,
                'message' => 'Test notification sent successfully',
                'data' => [
                    'notification' => $notification,
                    'result' => $result
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to send test notification', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to send test notification',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    public function sendBulkNotifications(Request $request)
    {
        try {
            // Rate limiting for bulk notifications
            $key = 'bulk_notification_' . $request->ip();
            if (RateLimiter::tooManyAttempts($key, $this->maxNotificationsPerMinute)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Too many bulk notifications. Please try again later.',
                    'retry_after' => RateLimiter::availableIn($key)
                ], 429);
            }
            RateLimiter::hit($key);

            // Validate request
            $validator = Validator::make($request->all(), [
                'notifications' => 'required|array|max:100',
                'notifications.*.title' => 'required|string|max:255',
                'notifications.*.message' => 'required|string|max:1000',
                'notifications.*.severity' => 'nullable|string|in:low,normal,high',
                'notifications.*.category' => 'nullable|string|max:50',
                'notifications.*.data' => 'nullable|array',
                'notifications.*.sound' => 'nullable|string|max:50',
                'notifications.*.badge' => 'nullable|string|max:10',
                'notifications.*.icon' => 'nullable|string|max:255',
                'notifications.*.color' => 'nullable|string|max:50',
                'notifications.*.tag' => 'nullable|string|max:50',
                'notifications.*.group' => 'nullable|string|max:50',
                'notifications.*.silent' => 'nullable|boolean',
                'notifications.*.vibrate' => 'nullable|boolean',
                'notifications.*.actions' => 'nullable|array'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $results = [];
            $successCount = 0;
            $failureCount = 0;

            foreach ($request->notifications as $notificationData) {
                try {
                    // Create notification
                    $notification = Notification::create([
                        'title' => $notificationData['title'],
                        'message' => $notificationData['message'],
                        'severity' => $notificationData['severity'] ?? 'normal',
                        'category' => $notificationData['category'] ?? null,
                        'data' => $notificationData['data'] ?? null,
                        'sound' => $notificationData['sound'] ?? null,
                        'badge' => $notificationData['badge'] ?? '1',
                        'icon' => $notificationData['icon'] ?? null,
                        'color' => $notificationData['color'] ?? null,
                        'tag' => $notificationData['tag'] ?? null,
                        'group' => $notificationData['group'] ?? null,
                        'silent' => $notificationData['silent'] ?? false,
                        'vibrate' => $notificationData['vibrate'] ?? true,
                        'actions' => $notificationData['actions'] ?? null
                    ]);

                    // Send notification
                    $result = $this->fcmService->sendNotification($notification);

                    if ($result['success']) {
                        $notification->markAsSent();
                        $successCount++;
                    } else {
                        $notification->markAsFailed($result['message']);
                        $failureCount++;
                    }

                    $results[] = [
                        'notification' => $notification,
                        'result' => $result
                    ];

                } catch (\Exception $e) {
                    Log::error('Failed to process notification in bulk send', [
                        'error' => $e->getMessage(),
                        'notification_data' => $notificationData
                    ]);
                    $failureCount++;
                }
            }

            return response()->json([
                'success' => true,
                'message' => "Bulk notification process completed. Success: {$successCount}, Failures: {$failureCount}",
                'data' => [
                    'success_count' => $successCount,
                    'failure_count' => $failureCount,
                    'results' => $results
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to send bulk notifications', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to send bulk notifications',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    public function getNotificationStatus($id)
    {
        try {
            $notification = Notification::findOrFail($id);

            return response()->json([
                'success' => true,
                'data' => [
                    'id' => $notification->id,
                    'title' => $notification->title,
                    'message' => $notification->message,
                    'sent' => $notification->sent,
                    'created_at' => $notification->created_at,
                    'updated_at' => $notification->updated_at
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to get notification status', [
                'id' => $id,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to get notification status',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Send a notification to a specific user
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function sendNotificationToUser(Request $request)
    {
        $request->validate([
            'title' => 'required|string',
            'body' => 'required|string',
            'category' => 'nullable|string',
            'severity' => 'nullable|string|in:low,medium,high',
            'user_id' => 'required|integer|exists:users,id',
            'data' => 'nullable|array',
        ]);

        try {
            // Create a notification record in the database first
            $notification = Notification::create([
                'title' => $request->title,
                'message' => $request->body,
                'category' => $request->category ?? 'general',
                'severity' => $request->severity ?? 'medium',
                'sent' => false
            ]);

            // Get the user's active tokens
            $tokens = DeviceToken::where('user_id', $request->user_id)
                ->where('is_active', true)
                ->pluck('token')
                ->toArray();

            if (empty($tokens)) {
                return response()->json([
                    'success' => false,
                    'message' => 'No active device tokens found for this user',
                ], 404);
            }

            // Send notification using FCM service
            $result = $this->fcmService->sendNotification($notification, $tokens);

            if (!$result['success']) {
                return response()->json([
                    'success' => false,
                    'message' => $result['message'],
                ], 500);
            }

            return response()->json([
                'success' => true,
                'message' => 'Notification sent successfully to user',
                'notification_id' => $notification->id,
                'tokens_count' => count($tokens),
                'success_count' => $result['success_count'],
                'failure_count' => $result['failure_count'],
                'invalid_tokens' => $result['invalid_tokens']
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to send notification to user', [
                'user_id' => $request->user_id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to send notification to user',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
