# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# Logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/

# nyc test coverage
.nyc_output

# Dependency directories
node_modules/
jspm_packages/

# Optional npm cache directory
.npm

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Build outputs
dist/
build/
www/

# Laravel specific
/vendor/
/storage/*.key
/storage/logs/*.log
/storage/framework/cache/data/*
/storage/framework/sessions/*
/storage/framework/views/*
/bootstrap/cache/*

# Android specific
*.apk
*.ap_
*.dex
*.class
bin/
gen/
out/
local.properties
proguard/

# Gradle
.gradle/
build/

# Firebase
google-services.json
firebase-service-account.json

# Capacitor
.capacitor/
ios/
android/app/src/main/assets/public/

# Temporary files
*.tmp
*.temp

# Additional Node.js
.npm
.eslintcache
.nyc_output
.cache/
.parcel-cache/
.next/
.nuxt/
.vuepress/dist
.serverless/
.fusebox/
.dynamodb/
.tern-port

# Additional Laravel
/public/hot
/public/storage
/storage/app/public
/storage/debugbar
/storage/clockwork
/storage/logs
/storage/framework/cache
/storage/framework/sessions
/storage/framework/testing
/storage/framework/views
/bootstrap/cache
.phpunit.result.cache
Homestead.json
Homestead.yaml
npm-debug.log
yarn-error.log
.env.backup

# Composer
/vendor/
composer.phar
composer.lock

# Database
*.sqlite
*.sqlite3
*.db

# Additional Android/Mobile
*.iml
.gradle
/local.properties
/.idea/caches
/.idea/libraries
/.idea/modules.xml
/.idea/workspace.xml
/.idea/navEditor.xml
/.idea/assetWizardSettings.xml
.DS_Store
/build
/captures
.externalNativeBuild
.cxx
*.apk
*.aab
*.ipa

# iOS specific
*.xcodeproj
*.xcworkspace
DerivedData/
*.hmap
*.ipa
*.dSYM.zip
*.dSYM

# Ionic/Capacitor specific
/www/
/platforms/
/plugins/
.sourcemaps/
.angular/

# Additional IDE files
*.sublime-project
*.sublime-workspace
.vscode/settings.json
.vscode/tasks.json
.vscode/launch.json
.vscode/extensions.json
*.code-workspace

# Package managers
package-lock.json
yarn.lock
pnpm-lock.yaml

# Testing
/coverage/
/.nyc_output
/test-results/
/playwright-report/
/playwright/.cache/

# Backup files
*.bak
*.backup
*.old
*.orig

# System files
.fuse_hidden*
.directory
.Trash-*
.nfs*

# Security
*.pem
*.key
*.crt
*.p12
*.pfx
.env.production
.env.staging
secrets.json

# Large files
*.zip
*.tar.gz
*.rar
*.7z
*.dmg
*.iso

# Generated documentation
/docs/build/
/docs/.vuepress/dist/
