<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        // Get the list of existing columns
        $columns = Schema::getColumnListing('notifications');

        // Add each column only if it doesn't already exist
        if (!in_array('severity', $columns)) {
            Schema::table('notifications', function (Blueprint $table) {
                $table->string('severity')->default('normal')->after('category');
            });
        }

        if (!in_array('sound', $columns)) {
            Schema::table('notifications', function (Blueprint $table) {
                $table->string('sound')->nullable()->after('severity');
            });
        }

        if (!in_array('badge', $columns)) {
            Schema::table('notifications', function (Blueprint $table) {
                $table->string('badge')->default('1')->after('sound');
            });
        }

        if (!in_array('icon', $columns)) {
            Schema::table('notifications', function (Blueprint $table) {
                $table->string('icon')->nullable()->after('badge');
            });
        }

        if (!in_array('color', $columns)) {
            Schema::table('notifications', function (Blueprint $table) {
                $table->string('color')->nullable()->after('icon');
            });
        }

        if (!in_array('tag', $columns)) {
            Schema::table('notifications', function (Blueprint $table) {
                $table->string('tag')->nullable()->after('color');
            });
        }

        if (!in_array('group', $columns)) {
            Schema::table('notifications', function (Blueprint $table) {
                $table->string('group')->nullable()->after('tag');
            });
        }

        if (!in_array('silent', $columns)) {
            Schema::table('notifications', function (Blueprint $table) {
                $table->boolean('silent')->default(false)->after('group');
            });
        }

        if (!in_array('vibrate', $columns)) {
            Schema::table('notifications', function (Blueprint $table) {
                $table->boolean('vibrate')->default(true)->after('silent');
            });
        }

        if (!in_array('actions', $columns)) {
            Schema::table('notifications', function (Blueprint $table) {
                $table->json('actions')->nullable()->after('vibrate');
            });
        }

        if (!in_array('scheduled_at', $columns)) {
            Schema::table('notifications', function (Blueprint $table) {
                $table->timestamp('scheduled_at')->nullable()->after('actions');
            });
        }

        if (!in_array('expires_at', $columns)) {
            Schema::table('notifications', function (Blueprint $table) {
                $table->timestamp('expires_at')->nullable()->after('scheduled_at');
            });
        }
    }

    public function down()
    {
        // Get the list of existing columns
        $columns = Schema::getColumnListing('notifications');

        // Create an array of columns to drop
        $columnsToDrop = [];

        // Check each column and add it to the drop list if it exists
        $columnsToCheck = [
            'severity', 'sound', 'badge', 'icon', 'color', 'tag', 'group',
            'silent', 'vibrate', 'actions', 'scheduled_at', 'expires_at'
        ];

        foreach ($columnsToCheck as $column) {
            if (in_array($column, $columns)) {
                $columnsToDrop[] = $column;
            }
        }

        // Only drop columns if there are any to drop
        if (!empty($columnsToDrop)) {
            Schema::table('notifications', function (Blueprint $table) use ($columnsToDrop) {
                $table->dropColumn($columnsToDrop);
            });
        }
    }
};