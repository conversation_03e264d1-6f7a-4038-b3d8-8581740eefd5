

<ion-content>
  <div class="ion-padding">
    <div class="disaster-container">
      <div class="home-title"><img src="assets/ALERTO.png" alt="App Logo" class="home-logo"/> <div style="font-size: 22px;">Hi, Welcome to <p style="font-size: 35px; color: #1565c0; margin-top: 0px;">Safe Area!</p></div></div>
      <div class="top-disaster">

      <ion-card class="disaster earthquake" (click)="openDisasterMap('earthquake')">
        <ion-card-content>
          <img src="assets/earthquake.png" alt="Earthquake">
          <ion-text><u>Earthquake</u></ion-text>
        </ion-card-content>
      </ion-card>

      <ion-card class="disaster typhoon" (click)="openDisasterMap('typhoon')">
        <ion-card-content>
          <img src="assets/typhoon.png" alt="Typhoon">
          <ion-text><u>Typhoon</u></ion-text>
        </ion-card-content>
      </ion-card>

      <ion-card class="disaster flood" (click)="openDisasterMap('flashflood')">
        <ion-card-content>
          <img src="assets/flood.png" alt="Flood">
          <ion-text><u>Flash Flood</u></ion-text>
        </ion-card-content>
      </ion-card>
    </div>

    <ion-button expand="block" class="view-map" (click)="viewMap()" [disabled]="isOffline" style="margin-top: 24px; width: 80%; height: 45px; --border-radius: 25px;">
      <ion-icon name="map" slot="start"></ion-icon>
      See the Whole Map
    </ion-button>


  </div>
</div>


