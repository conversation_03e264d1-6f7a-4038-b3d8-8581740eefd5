<?php
namespace App\Http\Controllers;

use App\Models\Notification;
use App\Models\DeviceToken;
use App\Services\FCMService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class NotificationController extends Controller
{
    protected $fcmService;

    public function __construct(FCMService $fcmService)
    {
        $this->fcmService = $fcmService;
    }
    // List all notifications
    public function index()
    {
        $notifications = Notification::orderBy('created_at', 'desc')->paginate(8);

        // Group by month and category
        $monthlyCategoryCounts = Notification::selectRaw(
            "
        DATE_FORMAT(created_at, '%Y-%m') as month,
        category,
        COUNT(*) as count
        ",
        )
            ->groupBy('month', 'category')
            ->orderBy('month')
            ->get();

        // Get recent alerts for the sidebar
        $recentAlerts = Notification::orderBy('created_at', 'desc')
            ->take(3)
            ->get()
            ->map(function ($notification) {
                return (object) [
                    'type' => $notification->category,
                    'message' => $notification->title,
                    'location' => 'All Users',
                    'time_ago' => $notification->created_at->diffForHumans(),
                ];
            });

        return view('components.notification.index', compact('notifications', 'monthlyCategoryCounts', 'recentAlerts'));
    }

    public function view($id)
    {
        $notification = Notification::find($id);
        return view('components.notification.create', compact('notification'));
    }

    // Show the form to create a new notification
    public function create()
    {
        return view('components.notification.create');
    }

    // Store a new notification
    public function store(Request $request)
    {
        // Validate form
        $request->validate([
            'title' => 'required|string|max:255',
            'category' => 'required|string',
            'message' => 'required|string',
            'severity' => 'required|string|in:low,medium,high',
        ]);

        // Create notification in database
        $notification = Notification::create([
            'title' => $request->title,
            'category' => $request->category,
            'message' => $request->message,
            'severity' => $request->severity,
            'sent' => false, // We will update later after sending
        ]);

        try {
            // Send notification using FCM service
            $result = $this->fcmService->sendNotification($notification);

            if (!$result['success']) {
                return redirect()->route('components.notification.index')
                    ->with('warning', 'Notification created but ' . $result['message']);
            }

            return redirect()->route('components.notification.index')
                ->with('success', "Notification created and sent to {$result['success_count']} devices" .
                    ($result['failure_count'] > 0 ? " ({$result['failure_count']} failures)" : ""));
        } catch (\Exception $e) {
            Log::error('Failed to send notification', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return redirect()->route('components.notification.index')
                ->with('error', 'Notification created but failed to send: ' . $e->getMessage());
        }
    }
}

