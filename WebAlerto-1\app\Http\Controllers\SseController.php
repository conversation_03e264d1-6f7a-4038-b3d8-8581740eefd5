<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\StreamedResponse;

class SseController extends Controller
{
    public function stream()
    {
        $response = new StreamedResponse(function () {
            echo "data: " . json_encode(['message' => 'Connected to SSE stream']) . "\n\n";
            ob_flush();
            flush();

            while (true) {
                // Check for new notifications every 5 seconds
                if (connection_aborted()) {
                    break;
                }

                // Get latest notification
                $notification = \App\Models\Notification::where('sent', true)
                    ->orderBy('created_at', 'desc')
                    ->first();

                if ($notification) {
                    echo "data: " . json_encode([
                        'id' => $notification->id,
                        'title' => $notification->title,
                        'message' => $notification->message,
                        'category' => $notification->category,
                        'severity' => $notification->severity,
                        'created_at' => $notification->created_at->toIso8601String()
                    ]) . "\n\n";
                    ob_flush();
                    flush();
                }

                sleep(5);
            }
        });

        $response->headers->set('Content-Type', 'text/event-stream');
        $response->headers->set('Cache-Control', 'no-cache');
        $response->headers->set('Connection', 'keep-alive');
        $response->headers->set('X-Accel-Buffering', 'no');
        $response->headers->set('Access-Control-Allow-Origin', '*');

        return $response;
    }
}
