import"./chunk-LNJ3S2LQ.js";var B=e=>e.replace(/[.*+?^${}()|[\]\\]/g,"\\$&");var x=e=>{let t=[],r=0;return e=e.replace(/(\[[^\]]*\])/g,(s,a)=>{let c=`__ph-${r}__`;return t.push(a),r++,c}),{content:e.replace(/(:nth-[-\w]+)(\([^)]+\))/g,(s,a,c)=>{let l=`__ph-${r}__`;return t.push(c),r++,a+l}),placeholders:t}},L=(e,t)=>t.replace(/__ph-(\d+)__/g,(r,n)=>e[+n]),f="-shadowcsshost",E="-shadowcssslotted",w="-shadowcsscontext",$=")(?:\\(((?:\\([^)(]*\\)|[^)(]*)+?)\\))?([^,{]*)",P=new RegExp("("+f+$,"gim"),W=new RegExp("("+w+$,"gim"),j=new RegExp("("+E+$,"gim"),h=f+"-no-combinator",N=/-shadowcsshost-no-combinator([^\s]*)/,M=[/::shadow/g,/::content/g],D="([>\\s~+[.,{:][\\s\\S]*)?$",R=/-shadowcsshost/gim,H=e=>new RegExp(`((?<!(^@supports(.*)))|(?<={.*))(${e}\\b)`,"gim"),K=H("::slotted"),U=H(":host"),Y=H(":host-context"),A=/\/\*\s*[\s\S]*?\*\//g,G=e=>e.replace(A,""),I=/\/\*\s*#\s*source(Mapping)?URL=[\s\S]+?\*\//g,q=e=>e.match(I)||[],z=/(\s*)([^;\{\}]+?)(\s*)((?:{%BLOCK%}?\s*;?)|(?:\s*;))/g,F=/([{}])/g,J=/(^.*?[^\\])??((:+)(.*)|$)/,Q="{",V="}",S="%BLOCK%",X=(e,t)=>{let r=Z(e),n=0;return r.escapedString.replace(z,(...o)=>{let s=o[2],a="",c=o[4],l="";c&&c.startsWith("{"+S)&&(a=r.blocks[n++],c=c.substring(S.length+1),l="{");let d=t({selector:s,content:a});return`${o[1]}${d.selector}${o[3]}${l}${d.content}${c}`})},Z=e=>{let t=e.split(F),r=[],n=[],o=0,s=[];for(let c=0;c<t.length;c++){let l=t[c];l===V&&o--,o>0?s.push(l):(s.length>0&&(n.push(s.join("")),r.push(S),s=[]),r.push(l)),l===Q&&o++}return s.length>0&&(n.push(s.join("")),r.push(S)),{escapedString:r.join(""),blocks:n}},T=e=>(e=e.replace(Y,`$1${w}`).replace(U,`$1${f}`).replace(K,`$1${E}`),e),O=(e,t,r)=>e.replace(t,(...n)=>{if(n[2]){let o=n[2].split(","),s=[];for(let a=0;a<o.length;a++){let c=o[a].trim();if(!c)break;s.push(r(h,c,n[3]))}return s.join(",")}else return h+n[3]}),b=(e,t,r)=>e+t.replace(f,"")+r,ee=e=>O(e,P,b),te=(e,t,r)=>t.indexOf(f)>-1?b(e,t,r):e+t+r+", "+t+" "+e+r,re=(e,t)=>{let r="."+t+" > ",n=[];return e=e.replace(j,(...o)=>{if(o[2]){let s=o[2].trim(),a=o[3],c=r+s+a,l="";for(let g=o[4]-1;g>=0;g--){let p=o[5][g];if(p==="}"||p===",")break;l=p+l}let u=(l+c).trim(),d=`${l.trimEnd()}${c.trim()}`.trim();if(u!==d){let g=`${d}, ${u}`;n.push({orgSelector:u,updatedSelector:g})}return c}else return h+o[3]}),{selectors:n,cssText:e}},oe=e=>O(e,W,te),ne=e=>M.reduce((t,r)=>t.replace(r," "),e),se=e=>{let t=/\[/g,r=/\]/g;return e=e.replace(t,"\\[").replace(r,"\\]"),new RegExp("^("+e+")"+D,"m")},ce=(e,t)=>!se(t).test(e),k=(e,t)=>e.replace(J,(r,n="",o,s="",a="")=>n+t+s+a),ae=(e,t,r)=>{if(R.lastIndex=0,R.test(e)){let n=`.${r}`;return e.replace(N,(o,s)=>k(s,n)).replace(R,n+" ")}return t+" "+e},le=(e,t,r)=>{let n=/\[is=([^\]]*)\]/g;t=t.replace(n,(v,...i)=>i[0]);let o="."+t,s=v=>{let i=v.trim();if(!i)return"";if(v.indexOf(h)>-1)i=ae(v,t,r);else{let _=v.replace(R,"");_.length>0&&(i=k(_,o))}return i},a=x(e);e=a.content;let c="",l=0,u,d=/( |>|\+|~(?!=))\s*/g,p=!(e.indexOf(h)>-1);for(;(u=d.exec(e))!==null;){let v=u[1],i=e.slice(l,u.index).trim();p=p||i.indexOf(h)>-1;let _=p?s(i):i;c+=`${_} ${v} `,l=d.lastIndex}let m=e.substring(l);return p=p||m.indexOf(h)>-1,c+=p?s(m):m,L(a.placeholders,c)},pe=(e,t,r,n)=>e.split(",").map(o=>n&&o.indexOf("."+n)>-1?o.trim():ce(o,t)?le(o,t,r).trim():o.trim()).join(", "),y=(e,t,r,n)=>X(e,o=>{let s=o.selector,a=o.content;return o.selector[0]!=="@"?s=pe(o.selector,t,r,n):(o.selector.startsWith("@media")||o.selector.startsWith("@supports")||o.selector.startsWith("@page")||o.selector.startsWith("@document"))&&(a=y(o.content,t,r,n)),{selector:s.replace(/\s{2,}/g," ").trim(),content:a}}),ie=(e,t,r,n)=>{e=T(e),e=ee(e),e=oe(e);let o=re(e,n);return e=o.cssText,e=ne(e),t&&(e=y(e,t,r,n)),e=C(e,r),e=e.replace(/>\s*\*\s+([^{, ]+)/gm," $1 "),{cssText:e.trim(),slottedSelectors:o.selectors.map(s=>({orgSelector:C(s.orgSelector,r),updatedSelector:C(s.updatedSelector,r)}))}},C=(e,t)=>e.replace(/-shadowcsshost-no-combinator/g,`.${t}`),ue=(e,t)=>{let r=t+"-h",n=t+"-s",o=q(e);e=G(e);let s=ie(e,t,r,n);return e=[s.cssText,...o].join(`
`),s.slottedSelectors.forEach(a=>{let c=new RegExp(B(a.orgSelector),"g");e=e.replace(c,a.updatedSelector)}),e};export{ue as scopeCss};
