#all-maps {
  height: 100%;
  width: 100%;
  z-index: 1;
}

.floating-info {
  position: absolute;
  top: 20px;
  right: 20px;
  z-index: 1000;
  max-width: 280px;

  ion-card {
    margin: 0;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    border-radius: 12px;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
  }

  ion-card-content {
    padding: 12px;
  }

  .info-header {
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 600;
    color: var(--ion-color-secondary);
    margin-bottom: 8px;

    ion-icon {
      font-size: 18px;
    }
  }

  .disaster-counts {
    margin: 8px 0;

    .count-row {
      display: flex;
      align-items: center;
      gap: 6px;
      margin: 4px 0;
      font-size: 13px;

      .disaster-icon {
        font-size: 14px;
        width: 16px;
        text-align: center;
      }

      .disaster-label {
        flex: 1;
        color: var(--ion-color-dark);
      }

      .disaster-count {
        font-weight: 600;
        color: var(--ion-color-secondary);
        min-width: 20px;
        text-align: right;
      }
    }
  }

  .info-text {
    font-size: 11px;
    color: var(--ion-color-medium);
    line-height: 1.3;
    margin-top: 8px;
    padding-top: 8px;
    border-top: 1px solid var(--ion-color-light);
  }
}

// All maps styling
ion-toolbar {
  --background: var(--ion-color-secondary);
  --color: white;
}

ion-title {
  font-weight: 600;
}

// Transportation Controls
.transport-controls {
  position: absolute;
  bottom: 120px;
  left: 20px;
  z-index: 1000;
  max-width: 280px;

  ion-card {
    margin: 0;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    border-radius: 12px;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
  }

  ion-card-content {
    padding: 12px;
  }

  .transport-header {
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 600;
    color: var(--ion-color-primary);
    margin-bottom: 8px;

    ion-icon {
      font-size: 18px;
    }
  }

  ion-segment {
    --background: rgba(var(--ion-color-light-rgb), 0.3);
    border-radius: 8px;
  }

  ion-segment-button {
    --color: var(--ion-color-medium);
    --color-checked: var(--ion-color-primary);
    --indicator-color: var(--ion-color-primary);
    min-height: 40px;

    ion-icon {
      font-size: 16px;
      margin-bottom: 2px;
    }

    ion-label {
      font-size: 12px;
      font-weight: 500;
    }
  }
}

// Route Information
.route-info {
  position: absolute;
  bottom: 20px;
  left: 20px;
  z-index: 1000;
  max-width: 200px;

  ion-card {
    margin: 0;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    border-radius: 12px;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
  }

  ion-card-content {
    padding: 12px;
  }

  .route-header {
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 600;
    color: var(--ion-color-success);
    margin-bottom: 8px;

    ion-icon {
      font-size: 18px;
    }
  }

  .route-details {
    display: flex;
    flex-direction: column;
    gap: 4px;
  }

  .route-item {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    color: var(--ion-color-dark);

    ion-icon {
      font-size: 16px;
      color: var(--ion-color-primary);
    }
  }
}

// FAB Label
.fab-label {
  position: absolute;
  right: 60px;
  top: 50%;
  transform: translateY(-50%);
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  white-space: nowrap;
  pointer-events: none;
}

// Pulsing Marker Animation
:global(.pulsing-marker) {
  .pulse-container {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .pulse {
    position: absolute;
    width: 60px;
    height: 60px;
    border-radius: 50%;
    opacity: 0.6;
    animation: pulse 2s infinite;
    z-index: 1;
  }

  .marker-icon {
    width: 40px;
    height: 40px;
    z-index: 2;
    position: relative;
  }

  .marker-label {
    position: absolute;
    top: -8px;
    right: -8px;
    background: var(--ion-color-primary);
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    font-weight: bold;
    z-index: 3;
    border: 2px solid white;
  }
}

@keyframes pulse {
  0% {
    transform: scale(0.8);
    opacity: 0.8;
  }
  50% {
    transform: scale(1.2);
    opacity: 0.4;
  }
  100% {
    transform: scale(0.8);
    opacity: 0.8;
  }
}

// Popup styling for all centers
:global(.leaflet-popup-content) {
  .evacuation-popup {
    text-align: center;
    min-width: 200px;

    h3 {
      margin: 0 0 8px 0;
      color: var(--ion-color-secondary);
      font-size: 16px;
      font-weight: 600;
    }

    h4 {
      margin: 4px 0;
      color: var(--ion-color-dark);
      font-size: 14px;
      font-weight: 500;
    }

    p {
      margin: 4px 0;
      font-size: 14px;

      strong {
        color: var(--ion-color-dark);
      }
    }

    &.nearest-popup {
      h3 {
        color: var(--ion-color-success);
        font-size: 18px;
      }
    }
  }
}
