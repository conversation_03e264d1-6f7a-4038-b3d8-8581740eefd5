import{a as ec}from"./chunk-CD7GTA6F.js";import{a as Ze}from"./chunk-FNGMIKCE.js";import{A as Sa,B as G,C as Y,D as wA,E as Ve,Ea as un,F as at,Fa as ka,G as yA,Ga as Na,H as tr,Ha as Ra,I as hA,Ia as Ga,J as Mt,K as Tt,Ka as Va,L as Oa,Ma as Za,Na as go,Oa as za,P as fo,Pa as Wa,Q as cn,Qa as Xa,R as ln,Ra as Ja,Sa as hn,U as Bo,Ua as po,X as Da,Xa as fn,Ya,Za as qa,db as $a,f as sn,ga as Ka,hb as ja,ib as Ac,lb as tc,m as mi,n as me,nb as wo,o as Ce,ob as mo,pb as dn,r as Ta,t as Pa,u as J,v as ho,w as an,y as Ct,z as uA}from"./chunk-7LSN6DH6.js";import"./chunk-EFGZ4QZO.js";import"./chunk-VKNYRYKJ.js";import{a as Co}from"./chunk-NETZAO6G.js";import"./chunk-YLGV66M3.js";import"./chunk-VD4VFVTQ.js";import"./chunk-HC6MZPB3.js";import"./chunk-IP3IRUG3.js";import"./chunk-UKIOCGZG.js";import"./chunk-RMJ7PCZJ.js";import"./chunk-K54AU7WQ.js";import"./chunk-VBU7NMPV.js";import"./chunk-JIZJ6CAM.js";import"./chunk-MCRJI3T3.js";import"./chunk-YNIR5NDL.js";import"./chunk-NO26UXQI.js";import"./chunk-SELJIRKY.js";import"./chunk-ODNE7IRY.js";import"./chunk-F4H6ZFEG.js";import"./chunk-JWIEPCRG.js";import"./chunk-7WD7LEC6.js";import"./chunk-WTCPO44B.js";import"./chunk-SV7S5NYR.js";import"./chunk-L5T6STQ3.js";import"./chunk-3EJRMEWO.js";import"./chunk-GNOVVPTF.js";import"./chunk-BAKMWPBW.js";import"./chunk-5OMUW5VI.js";import"./chunk-OBXDPQ3V.js";import{e as ba,f as Ma,h as HA}from"./chunk-LNJ3S2LQ.js";var nc=ba((vo,ic)=>{"use strict";(function(v,N){typeof vo=="object"&&typeof ic<"u"?N(vo):typeof define=="function"&&define.amd?define(["exports"],N):(v=typeof globalThis<"u"?globalThis:v||self,N(v.leaflet={}))})(vo,function(v){"use strict";var N="1.9.4";function I(A){var t,r,s,c;for(r=1,s=arguments.length;r<s;r++){c=arguments[r];for(t in c)A[t]=c[t]}return A}var B=Object.create||function(){function A(){}return function(t){return A.prototype=t,new A}}();function g(A,t){var r=Array.prototype.slice;if(A.bind)return A.bind.apply(A,r.call(arguments,1));var s=r.call(arguments,2);return function(){return A.apply(t,s.length?s.concat(r.call(arguments)):arguments)}}var Q=0;function C(A){return"_leaflet_id"in A||(A._leaflet_id=++Q),A._leaflet_id}function T(A,t,r){var s,c,h,p;return p=function(){s=!1,c&&(h.apply(r,c),c=!1)},h=function(){s?c=arguments:(A.apply(r,arguments),setTimeout(p,t),s=!0)},h}function x(A,t,r){var s=t[1],c=t[0],h=s-c;return A===s&&r?A:((A-c)%h+h)%h+c}function b(){return!1}function F(A,t){if(t===!1)return A;var r=Math.pow(10,t===void 0?6:t);return Math.round(A*r)/r}function Z(A){return A.trim?A.trim():A.replace(/^\s+|\s+$/g,"")}function R(A){return Z(A).split(/\s+/)}function X(A,t){Object.prototype.hasOwnProperty.call(A,"options")||(A.options=A.options?B(A.options):{});for(var r in t)A.options[r]=t[r];return A.options}function IA(A,t,r){var s=[];for(var c in A)s.push(encodeURIComponent(r?c.toUpperCase():c)+"="+encodeURIComponent(A[c]));return(!t||t.indexOf("?")===-1?"?":"&")+s.join("&")}var j=/\{ *([\w_ -]+) *\}/g;function PA(A,t){return A.replace(j,function(r,s){var c=t[s];if(c===void 0)throw new Error("No value provided for variable "+r);return typeof c=="function"&&(c=c(t)),c})}var fA=Array.isArray||function(A){return Object.prototype.toString.call(A)==="[object Array]"};function ve(A,t){for(var r=0;r<A.length;r++)if(A[r]===t)return r;return-1}var KA="data:image/gif;base64,R0lGODlhAQABAAD/ACwAAAAAAQABAAACADs=";function Pt(A){return window["webkit"+A]||window["moz"+A]||window["ms"+A]}var Ci=0;function vi(A){var t=+new Date,r=Math.max(0,16-(t-Ci));return Ci=t+r,window.setTimeout(A,r)}var Bn=window.requestAnimationFrame||Pt("RequestAnimationFrame")||vi,gn=window.cancelAnimationFrame||Pt("CancelAnimationFrame")||Pt("CancelRequestAnimationFrame")||function(A){window.clearTimeout(A)};function et(A,t,r){if(r&&Bn===vi)A.call(t);else return Bn.call(window,g(A,t))}function it(A){A&&gn.call(window,A)}var Qo={__proto__:null,extend:I,create:B,bind:g,get lastId(){return Q},stamp:C,throttle:T,wrapNum:x,falseFn:b,formatNum:F,trim:Z,splitWords:R,setOptions:X,getParamString:IA,template:PA,isArray:fA,indexOf:ve,emptyImageUrl:KA,requestFn:Bn,cancelFn:gn,requestAnimFrame:et,cancelAnimFrame:it};function St(){}St.extend=function(A){var t=function(){X(this),this.initialize&&this.initialize.apply(this,arguments),this.callInitHooks()},r=t.__super__=this.prototype,s=B(r);s.constructor=t,t.prototype=s;for(var c in this)Object.prototype.hasOwnProperty.call(this,c)&&c!=="prototype"&&c!=="__super__"&&(t[c]=this[c]);return A.statics&&I(t,A.statics),A.includes&&(_o(A.includes),I.apply(null,[s].concat(A.includes))),I(s,A),delete s.statics,delete s.includes,s.options&&(s.options=r.options?B(r.options):{},I(s.options,A.options)),s._initHooks=[],s.callInitHooks=function(){if(!this._initHooksCalled){r.callInitHooks&&r.callInitHooks.call(this),this._initHooksCalled=!0;for(var h=0,p=s._initHooks.length;h<p;h++)s._initHooks[h].call(this)}},t},St.include=function(A){var t=this.prototype.options;return I(this.prototype,A),A.options&&(this.prototype.options=t,this.mergeOptions(A.options)),this},St.mergeOptions=function(A){return I(this.prototype.options,A),this},St.addInitHook=function(A){var t=Array.prototype.slice.call(arguments,1),r=typeof A=="function"?A:function(){this[A].apply(this,t)};return this.prototype._initHooks=this.prototype._initHooks||[],this.prototype._initHooks.push(r),this};function _o(A){if(!(typeof L>"u"||!L||!L.Mixin)){A=fA(A)?A:[A];for(var t=0;t<A.length;t++)A[t]===L.Mixin.Events&&console.warn("Deprecated include of L.Mixin.Events: this property will be removed in future releases, please inherit from L.Evented instead.",new Error().stack)}}var ct={on:function(A,t,r){if(typeof A=="object")for(var s in A)this._on(s,A[s],t);else{A=R(A);for(var c=0,h=A.length;c<h;c++)this._on(A[c],t,r)}return this},off:function(A,t,r){if(!arguments.length)delete this._events;else if(typeof A=="object")for(var s in A)this._off(s,A[s],t);else{A=R(A);for(var c=arguments.length===1,h=0,p=A.length;h<p;h++)c?this._off(A[h]):this._off(A[h],t,r)}return this},_on:function(A,t,r,s){if(typeof t!="function"){console.warn("wrong listener type: "+typeof t);return}if(this._listens(A,t,r)===!1){r===this&&(r=void 0);var c={fn:t,ctx:r};s&&(c.once=!0),this._events=this._events||{},this._events[A]=this._events[A]||[],this._events[A].push(c)}},_off:function(A,t,r){var s,c,h;if(this._events&&(s=this._events[A],!!s)){if(arguments.length===1){if(this._firingCount)for(c=0,h=s.length;c<h;c++)s[c].fn=b;delete this._events[A];return}if(typeof t!="function"){console.warn("wrong listener type: "+typeof t);return}var p=this._listens(A,t,r);if(p!==!1){var U=s[p];this._firingCount&&(U.fn=b,this._events[A]=s=s.slice()),s.splice(p,1)}}},fire:function(A,t,r){if(!this.listens(A,r))return this;var s=I({},t,{type:A,target:this,sourceTarget:t&&t.sourceTarget||this});if(this._events){var c=this._events[A];if(c){this._firingCount=this._firingCount+1||1;for(var h=0,p=c.length;h<p;h++){var U=c[h],y=U.fn;U.once&&this.off(A,y,U.ctx),y.call(U.ctx||this,s)}this._firingCount--}}return r&&this._propagateEvent(s),this},listens:function(A,t,r,s){typeof A!="string"&&console.warn('"string" type argument expected');var c=t;typeof t!="function"&&(s=!!t,c=void 0,r=void 0);var h=this._events&&this._events[A];if(h&&h.length&&this._listens(A,c,r)!==!1)return!0;if(s){for(var p in this._eventParents)if(this._eventParents[p].listens(A,t,r,s))return!0}return!1},_listens:function(A,t,r){if(!this._events)return!1;var s=this._events[A]||[];if(!t)return!!s.length;r===this&&(r=void 0);for(var c=0,h=s.length;c<h;c++)if(s[c].fn===t&&s[c].ctx===r)return c;return!1},once:function(A,t,r){if(typeof A=="object")for(var s in A)this._on(s,A[s],t,!0);else{A=R(A);for(var c=0,h=A.length;c<h;c++)this._on(A[c],t,r,!0)}return this},addEventParent:function(A){return this._eventParents=this._eventParents||{},this._eventParents[C(A)]=A,this},removeEventParent:function(A){return this._eventParents&&delete this._eventParents[C(A)],this},_propagateEvent:function(A){for(var t in this._eventParents)this._eventParents[t].fire(A.type,I({layer:A.target,propagatedFrom:A.target},A),!0)}};ct.addEventListener=ct.on,ct.removeEventListener=ct.clearAllEventListeners=ct.off,ct.addOneTimeEventListener=ct.once,ct.fireEvent=ct.fire,ct.hasEventListeners=ct.listens;var ze=St.extend(ct);function rA(A,t,r){this.x=r?Math.round(A):A,this.y=r?Math.round(t):t}var er=Math.trunc||function(A){return A>0?Math.floor(A):Math.ceil(A)};rA.prototype={clone:function(){return new rA(this.x,this.y)},add:function(A){return this.clone()._add(eA(A))},_add:function(A){return this.x+=A.x,this.y+=A.y,this},subtract:function(A){return this.clone()._subtract(eA(A))},_subtract:function(A){return this.x-=A.x,this.y-=A.y,this},divideBy:function(A){return this.clone()._divideBy(A)},_divideBy:function(A){return this.x/=A,this.y/=A,this},multiplyBy:function(A){return this.clone()._multiplyBy(A)},_multiplyBy:function(A){return this.x*=A,this.y*=A,this},scaleBy:function(A){return new rA(this.x*A.x,this.y*A.y)},unscaleBy:function(A){return new rA(this.x/A.x,this.y/A.y)},round:function(){return this.clone()._round()},_round:function(){return this.x=Math.round(this.x),this.y=Math.round(this.y),this},floor:function(){return this.clone()._floor()},_floor:function(){return this.x=Math.floor(this.x),this.y=Math.floor(this.y),this},ceil:function(){return this.clone()._ceil()},_ceil:function(){return this.x=Math.ceil(this.x),this.y=Math.ceil(this.y),this},trunc:function(){return this.clone()._trunc()},_trunc:function(){return this.x=er(this.x),this.y=er(this.y),this},distanceTo:function(A){A=eA(A);var t=A.x-this.x,r=A.y-this.y;return Math.sqrt(t*t+r*r)},equals:function(A){return A=eA(A),A.x===this.x&&A.y===this.y},contains:function(A){return A=eA(A),Math.abs(A.x)<=Math.abs(this.x)&&Math.abs(A.y)<=Math.abs(this.y)},toString:function(){return"Point("+F(this.x)+", "+F(this.y)+")"}};function eA(A,t,r){return A instanceof rA?A:fA(A)?new rA(A[0],A[1]):A==null?A:typeof A=="object"&&"x"in A&&"y"in A?new rA(A.x,A.y):new rA(A,t,r)}function EA(A,t){if(A)for(var r=t?[A,t]:A,s=0,c=r.length;s<c;s++)this.extend(r[s])}EA.prototype={extend:function(A){var t,r;if(!A)return this;if(A instanceof rA||typeof A[0]=="number"||"x"in A)t=r=eA(A);else if(A=nt(A),t=A.min,r=A.max,!t||!r)return this;return!this.min&&!this.max?(this.min=t.clone(),this.max=r.clone()):(this.min.x=Math.min(t.x,this.min.x),this.max.x=Math.max(r.x,this.max.x),this.min.y=Math.min(t.y,this.min.y),this.max.y=Math.max(r.y,this.max.y)),this},getCenter:function(A){return eA((this.min.x+this.max.x)/2,(this.min.y+this.max.y)/2,A)},getBottomLeft:function(){return eA(this.min.x,this.max.y)},getTopRight:function(){return eA(this.max.x,this.min.y)},getTopLeft:function(){return this.min},getBottomRight:function(){return this.max},getSize:function(){return this.max.subtract(this.min)},contains:function(A){var t,r;return typeof A[0]=="number"||A instanceof rA?A=eA(A):A=nt(A),A instanceof EA?(t=A.min,r=A.max):t=r=A,t.x>=this.min.x&&r.x<=this.max.x&&t.y>=this.min.y&&r.y<=this.max.y},intersects:function(A){A=nt(A);var t=this.min,r=this.max,s=A.min,c=A.max,h=c.x>=t.x&&s.x<=r.x,p=c.y>=t.y&&s.y<=r.y;return h&&p},overlaps:function(A){A=nt(A);var t=this.min,r=this.max,s=A.min,c=A.max,h=c.x>t.x&&s.x<r.x,p=c.y>t.y&&s.y<r.y;return h&&p},isValid:function(){return!!(this.min&&this.max)},pad:function(A){var t=this.min,r=this.max,s=Math.abs(t.x-r.x)*A,c=Math.abs(t.y-r.y)*A;return nt(eA(t.x-s,t.y-c),eA(r.x+s,r.y+c))},equals:function(A){return A?(A=nt(A),this.min.equals(A.getTopLeft())&&this.max.equals(A.getBottomRight())):!1}};function nt(A,t){return!A||A instanceof EA?A:new EA(A,t)}function rt(A,t){if(A)for(var r=t?[A,t]:A,s=0,c=r.length;s<c;s++)this.extend(r[s])}rt.prototype={extend:function(A){var t=this._southWest,r=this._northEast,s,c;if(A instanceof CA)s=A,c=A;else if(A instanceof rt){if(s=A._southWest,c=A._northEast,!s||!c)return this}else return A?this.extend(dA(A)||SA(A)):this;return!t&&!r?(this._southWest=new CA(s.lat,s.lng),this._northEast=new CA(c.lat,c.lng)):(t.lat=Math.min(s.lat,t.lat),t.lng=Math.min(s.lng,t.lng),r.lat=Math.max(c.lat,r.lat),r.lng=Math.max(c.lng,r.lng)),this},pad:function(A){var t=this._southWest,r=this._northEast,s=Math.abs(t.lat-r.lat)*A,c=Math.abs(t.lng-r.lng)*A;return new rt(new CA(t.lat-s,t.lng-c),new CA(r.lat+s,r.lng+c))},getCenter:function(){return new CA((this._southWest.lat+this._northEast.lat)/2,(this._southWest.lng+this._northEast.lng)/2)},getSouthWest:function(){return this._southWest},getNorthEast:function(){return this._northEast},getNorthWest:function(){return new CA(this.getNorth(),this.getWest())},getSouthEast:function(){return new CA(this.getSouth(),this.getEast())},getWest:function(){return this._southWest.lng},getSouth:function(){return this._southWest.lat},getEast:function(){return this._northEast.lng},getNorth:function(){return this._northEast.lat},contains:function(A){typeof A[0]=="number"||A instanceof CA||"lat"in A?A=dA(A):A=SA(A);var t=this._southWest,r=this._northEast,s,c;return A instanceof rt?(s=A.getSouthWest(),c=A.getNorthEast()):s=c=A,s.lat>=t.lat&&c.lat<=r.lat&&s.lng>=t.lng&&c.lng<=r.lng},intersects:function(A){A=SA(A);var t=this._southWest,r=this._northEast,s=A.getSouthWest(),c=A.getNorthEast(),h=c.lat>=t.lat&&s.lat<=r.lat,p=c.lng>=t.lng&&s.lng<=r.lng;return h&&p},overlaps:function(A){A=SA(A);var t=this._southWest,r=this._northEast,s=A.getSouthWest(),c=A.getNorthEast(),h=c.lat>t.lat&&s.lat<r.lat,p=c.lng>t.lng&&s.lng<r.lng;return h&&p},toBBoxString:function(){return[this.getWest(),this.getSouth(),this.getEast(),this.getNorth()].join(",")},equals:function(A,t){return A?(A=SA(A),this._southWest.equals(A.getSouthWest(),t)&&this._northEast.equals(A.getNorthEast(),t)):!1},isValid:function(){return!!(this._southWest&&this._northEast)}};function SA(A,t){return A instanceof rt?A:new rt(A,t)}function CA(A,t,r){if(isNaN(A)||isNaN(t))throw new Error("Invalid LatLng object: ("+A+", "+t+")");this.lat=+A,this.lng=+t,r!==void 0&&(this.alt=+r)}CA.prototype={equals:function(A,t){if(!A)return!1;A=dA(A);var r=Math.max(Math.abs(this.lat-A.lat),Math.abs(this.lng-A.lng));return r<=(t===void 0?1e-9:t)},toString:function(A){return"LatLng("+F(this.lat,A)+", "+F(this.lng,A)+")"},distanceTo:function(A){return Gt.distance(this,dA(A))},wrap:function(){return Gt.wrapLatLng(this)},toBounds:function(A){var t=180*A/40075017,r=t/Math.cos(Math.PI/180*this.lat);return SA([this.lat-t,this.lng-r],[this.lat+t,this.lng+r])},clone:function(){return new CA(this.lat,this.lng,this.alt)}};function dA(A,t,r){return A instanceof CA?A:fA(A)&&typeof A[0]!="object"?A.length===3?new CA(A[0],A[1],A[2]):A.length===2?new CA(A[0],A[1]):null:A==null?A:typeof A=="object"&&"lat"in A?new CA(A.lat,"lng"in A?A.lng:A.lon,A.alt):t===void 0?null:new CA(A,t,r)}var lt={latLngToPoint:function(A,t){var r=this.projection.project(A),s=this.scale(t);return this.transformation._transform(r,s)},pointToLatLng:function(A,t){var r=this.scale(t),s=this.transformation.untransform(A,r);return this.projection.unproject(s)},project:function(A){return this.projection.project(A)},unproject:function(A){return this.projection.unproject(A)},scale:function(A){return 256*Math.pow(2,A)},zoom:function(A){return Math.log(A/256)/Math.LN2},getProjectedBounds:function(A){if(this.infinite)return null;var t=this.projection.bounds,r=this.scale(A),s=this.transformation.transform(t.min,r),c=this.transformation.transform(t.max,r);return new EA(s,c)},infinite:!1,wrapLatLng:function(A){var t=this.wrapLng?x(A.lng,this.wrapLng,!0):A.lng,r=this.wrapLat?x(A.lat,this.wrapLat,!0):A.lat,s=A.alt;return new CA(r,t,s)},wrapLatLngBounds:function(A){var t=A.getCenter(),r=this.wrapLatLng(t),s=t.lat-r.lat,c=t.lng-r.lng;if(s===0&&c===0)return A;var h=A.getSouthWest(),p=A.getNorthEast(),U=new CA(h.lat-s,h.lng-c),y=new CA(p.lat-s,p.lng-c);return new rt(U,y)}},Gt=I({},lt,{wrapLng:[-180,180],R:6371e3,distance:function(A,t){var r=Math.PI/180,s=A.lat*r,c=t.lat*r,h=Math.sin((t.lat-A.lat)*r/2),p=Math.sin((t.lng-A.lng)*r/2),U=h*h+Math.cos(s)*Math.cos(c)*p*p,y=2*Math.atan2(Math.sqrt(U),Math.sqrt(1-U));return this.R*y}}),pn=6378137,wn={R:pn,MAX_LATITUDE:85.0511287798,project:function(A){var t=Math.PI/180,r=this.MAX_LATITUDE,s=Math.max(Math.min(r,A.lat),-r),c=Math.sin(s*t);return new rA(this.R*A.lng*t,this.R*Math.log((1+c)/(1-c))/2)},unproject:function(A){var t=180/Math.PI;return new CA((2*Math.atan(Math.exp(A.y/this.R))-Math.PI/2)*t,A.x*t/this.R)},bounds:function(){var A=pn*Math.PI;return new EA([-A,-A],[A,A])}()};function Qi(A,t,r,s){if(fA(A)){this._a=A[0],this._b=A[1],this._c=A[2],this._d=A[3];return}this._a=A,this._b=t,this._c=r,this._d=s}Qi.prototype={transform:function(A,t){return this._transform(A.clone(),t)},_transform:function(A,t){return t=t||1,A.x=t*(this._a*A.x+this._b),A.y=t*(this._c*A.y+this._d),A},untransform:function(A,t){return t=t||1,new rA((A.x/t-this._b)/this._a,(A.y/t-this._d)/this._c)}};function Qe(A,t,r,s){return new Qi(A,t,r,s)}var mn=I({},Gt,{code:"EPSG:3857",projection:wn,transformation:function(){var A=.5/(Math.PI*wn.R);return Qe(A,.5,-A,.5)}()}),Uo=I({},mn,{code:"EPSG:900913"});function Cn(A){return document.createElementNS("http://www.w3.org/2000/svg",A)}function vn(A,t){var r="",s,c,h,p,U,y;for(s=0,h=A.length;s<h;s++){for(U=A[s],c=0,p=U.length;c<p;c++)y=U[c],r+=(c?"L":"M")+y.x+" "+y.y;r+=t?z.svg?"z":"x":""}return r||"M0 0"}var _i=document.documentElement.style,vt="ActiveXObject"in window,Qn=vt&&!document.addEventListener,_n="msLaunchUri"in navigator&&!("documentMode"in document),We=AA("webkit"),ir=AA("android"),_e=AA("android 2")||AA("android 3"),Un=parseInt(/WebKit\/([0-9]+)|$/.exec(navigator.userAgent)[1],10),Ui=ir&&AA("Google")&&Un<537&&!("AudioNode"in window),ee=!!window.opera,nr=!_n&&AA("chrome"),Fn=AA("gecko")&&!We&&!ee&&!vt,yn=!nr&&AA("safari"),Ue=AA("phantom"),Fi="OTransition"in _i,Fe=navigator.platform.indexOf("Win")===0,ot=vt&&"transition"in _i,ie="WebKitCSSMatrix"in window&&"m11"in new window.WebKitCSSMatrix&&!_e,ye="MozPerspective"in _i,Ee=!window.L_DISABLE_3D&&(ot||ie||ye)&&!Fi&&!Ue,Xe=typeof orientation<"u"||AA("mobile"),ne=Xe&&We,Fo=Xe&&ie,Je=!window.PointerEvent&&window.MSPointerEvent,Ye=!!(window.PointerEvent||Je),yi="ontouchstart"in window||!!window.TouchEvent,En=!window.L_NO_TOUCH&&(yi||Ye),In=Xe&&ee,qe=Xe&&Fn,xn=(window.devicePixelRatio||window.screen.deviceXDPI/window.screen.logicalXDPI)>1,Ei=function(){var A=!1;try{var t=Object.defineProperty({},"passive",{get:function(){A=!0}});window.addEventListener("testPassiveEventSupport",b,t),window.removeEventListener("testPassiveEventSupport",b,t)}catch{}return A}(),Ii=function(){return!!document.createElement("canvas").getContext}(),$e=!!(document.createElementNS&&Cn("svg").createSVGRect),rr=!!$e&&function(){var A=document.createElement("div");return A.innerHTML="<svg/>",(A.firstChild&&A.firstChild.namespaceURI)==="http://www.w3.org/2000/svg"}(),yo=!$e&&function(){try{var A=document.createElement("div");A.innerHTML='<v:shape adj="1"/>';var t=A.firstChild;return t.style.behavior="url(#default#VML)",t&&typeof t.adj=="object"}catch{return!1}}(),Eo=navigator.platform.indexOf("Mac")===0,or=navigator.platform.indexOf("Linux")===0;function AA(A){return navigator.userAgent.toLowerCase().indexOf(A)>=0}var z={ie:vt,ielt9:Qn,edge:_n,webkit:We,android:ir,android23:_e,androidStock:Ui,opera:ee,chrome:nr,gecko:Fn,safari:yn,phantom:Ue,opera12:Fi,win:Fe,ie3d:ot,webkit3d:ie,gecko3d:ye,any3d:Ee,mobile:Xe,mobileWebkit:ne,mobileWebkit3d:Fo,msPointer:Je,pointer:Ye,touch:En,touchNative:yi,mobileOpera:In,mobileGecko:qe,retina:xn,passiveEvents:Ei,canvas:Ii,svg:$e,vml:yo,inlineSvg:rr,mac:Eo,linux:or},xi=z.msPointer?"MSPointerDown":"pointerdown",Ft=z.msPointer?"MSPointerMove":"pointermove",Hi=z.msPointer?"MSPointerUp":"pointerup",Hn=z.msPointer?"MSPointerCancel":"pointercancel",Li={touchstart:xi,touchmove:Ft,touchend:Hi,touchcancel:Hn},sr={touchstart:Lo,touchmove:bi,touchend:bi,touchcancel:bi},re={},ar=!1;function Io(A,t,r){return t==="touchstart"&&Ho(),sr[t]?(r=sr[t].bind(this,r),A.addEventListener(Li[t],r,!1),r):(console.warn("wrong event specified:",t),b)}function Ln(A,t,r){if(!Li[t]){console.warn("wrong event specified:",t);return}A.removeEventListener(Li[t],r,!1)}function cr(A){re[A.pointerId]=A}function xo(A){re[A.pointerId]&&(re[A.pointerId]=A)}function lr(A){delete re[A.pointerId]}function Ho(){ar||(document.addEventListener(xi,cr,!0),document.addEventListener(Ft,xo,!0),document.addEventListener(Hi,lr,!0),document.addEventListener(Hn,lr,!0),ar=!0)}function bi(A,t){if(t.pointerType!==(t.MSPOINTER_TYPE_MOUSE||"mouse")){t.touches=[];for(var r in re)t.touches.push(re[r]);t.changedTouches=[t],A(t)}}function Lo(A,t){t.MSPOINTER_TYPE_TOUCH&&t.pointerType===t.MSPOINTER_TYPE_TOUCH&&GA(t),bi(A,t)}function bo(A){var t={},r,s;for(s in A)r=A[s],t[s]=r&&r.bind?r.bind(A):r;return A=t,t.type="dblclick",t.detail=2,t.isTrusted=!1,t._simulated=!0,t}var je=200;function ur(A,t){A.addEventListener("dblclick",t);var r=0,s;function c(h){if(h.detail!==1){s=h.detail;return}if(!(h.pointerType==="mouse"||h.sourceCapabilities&&!h.sourceCapabilities.firesTouchEvents)){var p=gr(h);if(!(p.some(function(y){return y instanceof HTMLLabelElement&&y.attributes.for})&&!p.some(function(y){return y instanceof HTMLInputElement||y instanceof HTMLSelectElement}))){var U=Date.now();U-r<=je?(s++,s===2&&t(bo(h))):s=1,r=U}}}return A.addEventListener("click",c),{dblclick:t,simDblclick:c}}function Mi(A,t){A.removeEventListener("dblclick",t.dblclick),A.removeEventListener("click",t.simDblclick)}var Ti=Oi(["transform","webkitTransform","OTransform","MozTransform","msTransform"]),Ot=Oi(["webkitTransition","transition","OTransition","MozTransition","msTransition"]),hr=Ot==="webkitTransition"||Ot==="OTransition"?Ot+"End":"transitionend";function fr(A){return typeof A=="string"?document.getElementById(A):A}function Vt(A,t){var r=A.style[t]||A.currentStyle&&A.currentStyle[t];if((!r||r==="auto")&&document.defaultView){var s=document.defaultView.getComputedStyle(A,null);r=s?s[t]:null}return r==="auto"?null:r}function lA(A,t,r){var s=document.createElement(A);return s.className=t||"",r&&r.appendChild(s),s}function xA(A){var t=A.parentNode;t&&t.removeChild(A)}function Pi(A){for(;A.firstChild;)A.removeChild(A.firstChild)}function Ie(A){var t=A.parentNode;t&&t.lastChild!==A&&t.appendChild(A)}function Dt(A){var t=A.parentNode;t&&t.firstChild!==A&&t.insertBefore(A,t.firstChild)}function xe(A,t){if(A.classList!==void 0)return A.classList.contains(t);var r=Si(A);return r.length>0&&new RegExp("(^|\\s)"+t+"(\\s|$)").test(r)}function iA(A,t){if(A.classList!==void 0)for(var r=R(t),s=0,c=r.length;s<c;s++)A.classList.add(r[s]);else if(!xe(A,t)){var h=Si(A);XA(A,(h?h+" ":"")+t)}}function bA(A,t){A.classList!==void 0?A.classList.remove(t):XA(A,Z((" "+Si(A)+" ").replace(" "+t+" "," ")))}function XA(A,t){A.className.baseVal===void 0?A.className=t:A.className.baseVal=t}function Si(A){return A.correspondingElement&&(A=A.correspondingElement),A.className.baseVal===void 0?A.className:A.className.baseVal}function ut(A,t){"opacity"in A.style?A.style.opacity=t:"filter"in A.style&&Mo(A,t)}function Mo(A,t){var r=!1,s="DXImageTransform.Microsoft.Alpha";try{r=A.filters.item(s)}catch{if(t===1)return}t=Math.round(t*100),r?(r.Enabled=t!==100,r.Opacity=t):A.style.filter+=" progid:"+s+"(opacity="+t+")"}function Oi(A){for(var t=document.documentElement.style,r=0;r<A.length;r++)if(A[r]in t)return A[r];return!1}function oe(A,t,r){var s=t||new rA(0,0);A.style[Ti]=(z.ie3d?"translate("+s.x+"px,"+s.y+"px)":"translate3d("+s.x+"px,"+s.y+"px,0)")+(r?" scale("+r+")":"")}function OA(A,t){A._leaflet_pos=t,z.any3d?oe(A,t):(A.style.left=t.x+"px",A.style.top=t.y+"px")}function se(A){return A._leaflet_pos||new rA(0,0)}var Ai,Zt,bn;if("onselectstart"in document)Ai=function(){oA(window,"selectstart",GA)},Zt=function(){_A(window,"selectstart",GA)};else{var He=Oi(["userSelect","WebkitUserSelect","OUserSelect","MozUserSelect","msUserSelect"]);Ai=function(){if(He){var A=document.documentElement.style;bn=A[He],A[He]="none"}},Zt=function(){He&&(document.documentElement.style[He]=bn,bn=void 0)}}function Mn(){oA(window,"dragstart",GA)}function Tn(){_A(window,"dragstart",GA)}var ti,ei;function Kt(A){for(;A.tabIndex===-1;)A=A.parentNode;A.style&&(Di(),ti=A,ei=A.style.outlineStyle,A.style.outlineStyle="none",oA(window,"keydown",Di))}function Di(){ti&&(ti.style.outlineStyle=ei,ti=void 0,ei=void 0,_A(window,"keydown",Di))}function dr(A){do A=A.parentNode;while((!A.offsetWidth||!A.offsetHeight)&&A!==document.body);return A}function Pn(A){var t=A.getBoundingClientRect();return{x:t.width/A.offsetWidth||1,y:t.height/A.offsetHeight||1,boundingClientRect:t}}var ii={__proto__:null,TRANSFORM:Ti,TRANSITION:Ot,TRANSITION_END:hr,get:fr,getStyle:Vt,create:lA,remove:xA,empty:Pi,toFront:Ie,toBack:Dt,hasClass:xe,addClass:iA,removeClass:bA,setClass:XA,getClass:Si,setOpacity:ut,testProp:Oi,setTransform:oe,setPosition:OA,getPosition:se,get disableTextSelection(){return Ai},get enableTextSelection(){return Zt},disableImageDrag:Mn,enableImageDrag:Tn,preventOutline:Kt,restoreOutline:Di,getSizedParentNode:dr,getScale:Pn};function oA(A,t,r,s){if(t&&typeof t=="object")for(var c in t)On(A,c,t[c],r);else{t=R(t);for(var h=0,p=t.length;h<p;h++)On(A,t[h],r,s)}return this}var yt="_leaflet_events";function _A(A,t,r,s){if(arguments.length===1)Br(A),delete A[yt];else if(t&&typeof t=="object")for(var c in t)ht(A,c,t[c],r);else if(t=R(t),arguments.length===2)Br(A,function(U){return ve(t,U)!==-1});else for(var h=0,p=t.length;h<p;h++)ht(A,t[h],r,s);return this}function Br(A,t){for(var r in A[yt]){var s=r.split(/\d/)[0];(!t||t(s))&&ht(A,s,null,null,r)}}var Sn={mouseenter:"mouseover",mouseleave:"mouseout",wheel:!("onwheel"in window)&&"mousewheel"};function On(A,t,r,s){var c=t+C(r)+(s?"_"+C(s):"");if(A[yt]&&A[yt][c])return this;var h=function(U){return r.call(s||A,U||window.event)},p=h;!z.touchNative&&z.pointer&&t.indexOf("touch")===0?h=Io(A,t,h):z.touch&&t==="dblclick"?h=ur(A,h):"addEventListener"in A?t==="touchstart"||t==="touchmove"||t==="wheel"||t==="mousewheel"?A.addEventListener(Sn[t]||t,h,z.passiveEvents?{passive:!1}:!1):t==="mouseenter"||t==="mouseleave"?(h=function(U){U=U||window.event,kn(A,U)&&p(U)},A.addEventListener(Sn[t],h,!1)):A.addEventListener(t,p,!1):A.attachEvent("on"+t,h),A[yt]=A[yt]||{},A[yt][c]=h}function ht(A,t,r,s,c){c=c||t+C(r)+(s?"_"+C(s):"");var h=A[yt]&&A[yt][c];if(!h)return this;!z.touchNative&&z.pointer&&t.indexOf("touch")===0?Ln(A,t,h):z.touch&&t==="dblclick"?Mi(A,h):"removeEventListener"in A?A.removeEventListener(Sn[t]||t,h,!1):A.detachEvent("on"+t,h),A[yt][c]=null}function zt(A){return A.stopPropagation?A.stopPropagation():A.originalEvent?A.originalEvent._stopped=!0:A.cancelBubble=!0,this}function Ki(A){return On(A,"wheel",zt),this}function Le(A){return oA(A,"mousedown touchstart dblclick contextmenu",zt),A._leaflet_disable_click=!0,this}function GA(A){return A.preventDefault?A.preventDefault():A.returnValue=!1,this}function ae(A){return GA(A),zt(A),this}function gr(A){if(A.composedPath)return A.composedPath();for(var t=[],r=A.target;r;)t.push(r),r=r.parentNode;return t}function Dn(A,t){if(!t)return new rA(A.clientX,A.clientY);var r=Pn(t),s=r.boundingClientRect;return new rA((A.clientX-s.left)/r.x-t.clientLeft,(A.clientY-s.top)/r.y-t.clientTop)}var pr=z.linux&&z.chrome?window.devicePixelRatio:z.mac?window.devicePixelRatio*3:window.devicePixelRatio>0?2*window.devicePixelRatio:1;function Kn(A){return z.edge?A.wheelDeltaY/2:A.deltaY&&A.deltaMode===0?-A.deltaY/pr:A.deltaY&&A.deltaMode===1?-A.deltaY*20:A.deltaY&&A.deltaMode===2?-A.deltaY*60:A.deltaX||A.deltaZ?0:A.wheelDelta?(A.wheelDeltaY||A.wheelDelta)/2:A.detail&&Math.abs(A.detail)<32765?-A.detail*20:A.detail?A.detail/-32765*60:0}function kn(A,t){var r=t.relatedTarget;if(!r)return!0;try{for(;r&&r!==A;)r=r.parentNode}catch{return!1}return r!==A}var To={__proto__:null,on:oA,off:_A,stopPropagation:zt,disableScrollPropagation:Ki,disableClickPropagation:Le,preventDefault:GA,stop:ae,getPropagationPath:gr,getMousePosition:Dn,getWheelDelta:Kn,isExternalTarget:kn,addListener:oA,removeListener:_A},VA=ze.extend({run:function(A,t,r,s){this.stop(),this._el=A,this._inProgress=!0,this._duration=r||.25,this._easeOutPower=1/Math.max(s||.5,.2),this._startPos=se(A),this._offset=t.subtract(this._startPos),this._startTime=+new Date,this.fire("start"),this._animate()},stop:function(){this._inProgress&&(this._step(!0),this._complete())},_animate:function(){this._animId=et(this._animate,this),this._step()},_step:function(A){var t=+new Date-this._startTime,r=this._duration*1e3;t<r?this._runFrame(this._easeOut(t/r),A):(this._runFrame(1),this._complete())},_runFrame:function(A,t){var r=this._startPos.add(this._offset.multiplyBy(A));t&&r._round(),OA(this._el,r),this.fire("step")},_complete:function(){it(this._animId),this._inProgress=!1,this.fire("end")},_easeOut:function(A){return 1-Math.pow(1-A,this._easeOutPower)}}),BA=ze.extend({options:{crs:mn,center:void 0,zoom:void 0,minZoom:void 0,maxZoom:void 0,layers:[],maxBounds:void 0,renderer:void 0,zoomAnimation:!0,zoomAnimationThreshold:4,fadeAnimation:!0,markerZoomAnimation:!0,transform3DLimit:8388608,zoomSnap:1,zoomDelta:1,trackResize:!0},initialize:function(A,t){t=X(this,t),this._handlers=[],this._layers={},this._zoomBoundLayers={},this._sizeChanged=!0,this._initContainer(A),this._initLayout(),this._onResize=g(this._onResize,this),this._initEvents(),t.maxBounds&&this.setMaxBounds(t.maxBounds),t.zoom!==void 0&&(this._zoom=this._limitZoom(t.zoom)),t.center&&t.zoom!==void 0&&this.setView(dA(t.center),t.zoom,{reset:!0}),this.callInitHooks(),this._zoomAnimated=Ot&&z.any3d&&!z.mobileOpera&&this.options.zoomAnimation,this._zoomAnimated&&(this._createAnimProxy(),oA(this._proxy,hr,this._catchTransitionEnd,this)),this._addLayers(this.options.layers)},setView:function(A,t,r){if(t=t===void 0?this._zoom:this._limitZoom(t),A=this._limitCenter(dA(A),t,this.options.maxBounds),r=r||{},this._stop(),this._loaded&&!r.reset&&r!==!0){r.animate!==void 0&&(r.zoom=I({animate:r.animate},r.zoom),r.pan=I({animate:r.animate,duration:r.duration},r.pan));var s=this._zoom!==t?this._tryAnimatedZoom&&this._tryAnimatedZoom(A,t,r.zoom):this._tryAnimatedPan(A,r.pan);if(s)return clearTimeout(this._sizeTimer),this}return this._resetView(A,t,r.pan&&r.pan.noMoveStart),this},setZoom:function(A,t){return this._loaded?this.setView(this.getCenter(),A,{zoom:t}):(this._zoom=A,this)},zoomIn:function(A,t){return A=A||(z.any3d?this.options.zoomDelta:1),this.setZoom(this._zoom+A,t)},zoomOut:function(A,t){return A=A||(z.any3d?this.options.zoomDelta:1),this.setZoom(this._zoom-A,t)},setZoomAround:function(A,t,r){var s=this.getZoomScale(t),c=this.getSize().divideBy(2),h=A instanceof rA?A:this.latLngToContainerPoint(A),p=h.subtract(c).multiplyBy(1-1/s),U=this.containerPointToLatLng(c.add(p));return this.setView(U,t,{zoom:r})},_getBoundsCenterZoom:function(A,t){t=t||{},A=A.getBounds?A.getBounds():SA(A);var r=eA(t.paddingTopLeft||t.padding||[0,0]),s=eA(t.paddingBottomRight||t.padding||[0,0]),c=this.getBoundsZoom(A,!1,r.add(s));if(c=typeof t.maxZoom=="number"?Math.min(t.maxZoom,c):c,c===1/0)return{center:A.getCenter(),zoom:c};var h=s.subtract(r).divideBy(2),p=this.project(A.getSouthWest(),c),U=this.project(A.getNorthEast(),c),y=this.unproject(p.add(U).divideBy(2).add(h),c);return{center:y,zoom:c}},fitBounds:function(A,t){if(A=SA(A),!A.isValid())throw new Error("Bounds are not valid.");var r=this._getBoundsCenterZoom(A,t);return this.setView(r.center,r.zoom,t)},fitWorld:function(A){return this.fitBounds([[-90,-180],[90,180]],A)},panTo:function(A,t){return this.setView(A,this._zoom,{pan:t})},panBy:function(A,t){if(A=eA(A).round(),t=t||{},!A.x&&!A.y)return this.fire("moveend");if(t.animate!==!0&&!this.getSize().contains(A))return this._resetView(this.unproject(this.project(this.getCenter()).add(A)),this.getZoom()),this;if(this._panAnim||(this._panAnim=new VA,this._panAnim.on({step:this._onPanTransitionStep,end:this._onPanTransitionEnd},this)),t.noMoveStart||this.fire("movestart"),t.animate!==!1){iA(this._mapPane,"leaflet-pan-anim");var r=this._getMapPanePos().subtract(A).round();this._panAnim.run(this._mapPane,r,t.duration||.25,t.easeLinearity)}else this._rawPanBy(A),this.fire("move").fire("moveend");return this},flyTo:function(A,t,r){if(r=r||{},r.animate===!1||!z.any3d)return this.setView(A,t,r);this._stop();var s=this.project(this.getCenter()),c=this.project(A),h=this.getSize(),p=this._zoom;A=dA(A),t=t===void 0?p:t;var U=Math.max(h.x,h.y),y=U*this.getZoomScale(p,t),H=c.distanceTo(s)||1,P=1.42,q=P*P;function aA(TA){var qi=TA?-1:1,es=TA?y:U,Xn=y*y-U*U+qi*q*q*H*H,$i=2*es*q*H,Jn=Xn/$i,Wr=Math.sqrt(Jn*Jn+1)-Jn,is=Wr<1e-9?-18:Math.log(Wr);return is}function jA(TA){return(Math.exp(TA)-Math.exp(-TA))/2}function NA(TA){return(Math.exp(TA)+Math.exp(-TA))/2}function gt(TA){return jA(TA)/NA(TA)}var st=aA(0);function Ke(TA){return U*(NA(st)/NA(st+P*TA))}function $o(TA){return U*(NA(st)*gt(st+P*TA)-jA(st))/q}function jo(TA){return 1-Math.pow(1-TA,1.5)}var As=Date.now(),Yi=(aA(1)-st)/P,ts=r.duration?1e3*r.duration:1e3*Yi*.8;function zr(){var TA=(Date.now()-As)/ts,qi=jo(TA)*Yi;TA<=1?(this._flyToFrame=et(zr,this),this._move(this.unproject(s.add(c.subtract(s).multiplyBy($o(qi)/H)),p),this.getScaleZoom(U/Ke(qi),p),{flyTo:!0})):this._move(A,t)._moveEnd(!0)}return this._moveStart(!0,r.noMoveStart),zr.call(this),this},flyToBounds:function(A,t){var r=this._getBoundsCenterZoom(A,t);return this.flyTo(r.center,r.zoom,t)},setMaxBounds:function(A){return A=SA(A),this.listens("moveend",this._panInsideMaxBounds)&&this.off("moveend",this._panInsideMaxBounds),A.isValid()?(this.options.maxBounds=A,this._loaded&&this._panInsideMaxBounds(),this.on("moveend",this._panInsideMaxBounds)):(this.options.maxBounds=null,this)},setMinZoom:function(A){var t=this.options.minZoom;return this.options.minZoom=A,this._loaded&&t!==A&&(this.fire("zoomlevelschange"),this.getZoom()<this.options.minZoom)?this.setZoom(A):this},setMaxZoom:function(A){var t=this.options.maxZoom;return this.options.maxZoom=A,this._loaded&&t!==A&&(this.fire("zoomlevelschange"),this.getZoom()>this.options.maxZoom)?this.setZoom(A):this},panInsideBounds:function(A,t){this._enforcingBounds=!0;var r=this.getCenter(),s=this._limitCenter(r,this._zoom,SA(A));return r.equals(s)||this.panTo(s,t),this._enforcingBounds=!1,this},panInside:function(A,t){t=t||{};var r=eA(t.paddingTopLeft||t.padding||[0,0]),s=eA(t.paddingBottomRight||t.padding||[0,0]),c=this.project(this.getCenter()),h=this.project(A),p=this.getPixelBounds(),U=nt([p.min.add(r),p.max.subtract(s)]),y=U.getSize();if(!U.contains(h)){this._enforcingBounds=!0;var H=h.subtract(U.getCenter()),P=U.extend(h).getSize().subtract(y);c.x+=H.x<0?-P.x:P.x,c.y+=H.y<0?-P.y:P.y,this.panTo(this.unproject(c),t),this._enforcingBounds=!1}return this},invalidateSize:function(A){if(!this._loaded)return this;A=I({animate:!1,pan:!0},A===!0?{animate:!0}:A);var t=this.getSize();this._sizeChanged=!0,this._lastCenter=null;var r=this.getSize(),s=t.divideBy(2).round(),c=r.divideBy(2).round(),h=s.subtract(c);return!h.x&&!h.y?this:(A.animate&&A.pan?this.panBy(h):(A.pan&&this._rawPanBy(h),this.fire("move"),A.debounceMoveend?(clearTimeout(this._sizeTimer),this._sizeTimer=setTimeout(g(this.fire,this,"moveend"),200)):this.fire("moveend")),this.fire("resize",{oldSize:t,newSize:r}))},stop:function(){return this.setZoom(this._limitZoom(this._zoom)),this.options.zoomSnap||this.fire("viewreset"),this._stop()},locate:function(A){if(A=this._locateOptions=I({timeout:1e4,watch:!1},A),!("geolocation"in navigator))return this._handleGeolocationError({code:0,message:"Geolocation not supported."}),this;var t=g(this._handleGeolocationResponse,this),r=g(this._handleGeolocationError,this);return A.watch?this._locationWatchId=navigator.geolocation.watchPosition(t,r,A):navigator.geolocation.getCurrentPosition(t,r,A),this},stopLocate:function(){return navigator.geolocation&&navigator.geolocation.clearWatch&&navigator.geolocation.clearWatch(this._locationWatchId),this._locateOptions&&(this._locateOptions.setView=!1),this},_handleGeolocationError:function(A){if(this._container._leaflet_id){var t=A.code,r=A.message||(t===1?"permission denied":t===2?"position unavailable":"timeout");this._locateOptions.setView&&!this._loaded&&this.fitWorld(),this.fire("locationerror",{code:t,message:"Geolocation error: "+r+"."})}},_handleGeolocationResponse:function(A){if(this._container._leaflet_id){var t=A.coords.latitude,r=A.coords.longitude,s=new CA(t,r),c=s.toBounds(A.coords.accuracy*2),h=this._locateOptions;if(h.setView){var p=this.getBoundsZoom(c);this.setView(s,h.maxZoom?Math.min(p,h.maxZoom):p)}var U={latlng:s,bounds:c,timestamp:A.timestamp};for(var y in A.coords)typeof A.coords[y]=="number"&&(U[y]=A.coords[y]);this.fire("locationfound",U)}},addHandler:function(A,t){if(!t)return this;var r=this[A]=new t(this);return this._handlers.push(r),this.options[A]&&r.enable(),this},remove:function(){if(this._initEvents(!0),this.options.maxBounds&&this.off("moveend",this._panInsideMaxBounds),this._containerId!==this._container._leaflet_id)throw new Error("Map container is being reused by another instance");try{delete this._container._leaflet_id,delete this._containerId}catch{this._container._leaflet_id=void 0,this._containerId=void 0}this._locationWatchId!==void 0&&this.stopLocate(),this._stop(),xA(this._mapPane),this._clearControlPos&&this._clearControlPos(),this._resizeRequest&&(it(this._resizeRequest),this._resizeRequest=null),this._clearHandlers(),this._loaded&&this.fire("unload");var A;for(A in this._layers)this._layers[A].remove();for(A in this._panes)xA(this._panes[A]);return this._layers=[],this._panes=[],delete this._mapPane,delete this._renderer,this},createPane:function(A,t){var r="leaflet-pane"+(A?" leaflet-"+A.replace("Pane","")+"-pane":""),s=lA("div",r,t||this._mapPane);return A&&(this._panes[A]=s),s},getCenter:function(){return this._checkIfLoaded(),this._lastCenter&&!this._moved()?this._lastCenter.clone():this.layerPointToLatLng(this._getCenterLayerPoint())},getZoom:function(){return this._zoom},getBounds:function(){var A=this.getPixelBounds(),t=this.unproject(A.getBottomLeft()),r=this.unproject(A.getTopRight());return new rt(t,r)},getMinZoom:function(){return this.options.minZoom===void 0?this._layersMinZoom||0:this.options.minZoom},getMaxZoom:function(){return this.options.maxZoom===void 0?this._layersMaxZoom===void 0?1/0:this._layersMaxZoom:this.options.maxZoom},getBoundsZoom:function(A,t,r){A=SA(A),r=eA(r||[0,0]);var s=this.getZoom()||0,c=this.getMinZoom(),h=this.getMaxZoom(),p=A.getNorthWest(),U=A.getSouthEast(),y=this.getSize().subtract(r),H=nt(this.project(U,s),this.project(p,s)).getSize(),P=z.any3d?this.options.zoomSnap:1,q=y.x/H.x,aA=y.y/H.y,jA=t?Math.max(q,aA):Math.min(q,aA);return s=this.getScaleZoom(jA,s),P&&(s=Math.round(s/(P/100))*(P/100),s=t?Math.ceil(s/P)*P:Math.floor(s/P)*P),Math.max(c,Math.min(h,s))},getSize:function(){return(!this._size||this._sizeChanged)&&(this._size=new rA(this._container.clientWidth||0,this._container.clientHeight||0),this._sizeChanged=!1),this._size.clone()},getPixelBounds:function(A,t){var r=this._getTopLeftPoint(A,t);return new EA(r,r.add(this.getSize()))},getPixelOrigin:function(){return this._checkIfLoaded(),this._pixelOrigin},getPixelWorldBounds:function(A){return this.options.crs.getProjectedBounds(A===void 0?this.getZoom():A)},getPane:function(A){return typeof A=="string"?this._panes[A]:A},getPanes:function(){return this._panes},getContainer:function(){return this._container},getZoomScale:function(A,t){var r=this.options.crs;return t=t===void 0?this._zoom:t,r.scale(A)/r.scale(t)},getScaleZoom:function(A,t){var r=this.options.crs;t=t===void 0?this._zoom:t;var s=r.zoom(A*r.scale(t));return isNaN(s)?1/0:s},project:function(A,t){return t=t===void 0?this._zoom:t,this.options.crs.latLngToPoint(dA(A),t)},unproject:function(A,t){return t=t===void 0?this._zoom:t,this.options.crs.pointToLatLng(eA(A),t)},layerPointToLatLng:function(A){var t=eA(A).add(this.getPixelOrigin());return this.unproject(t)},latLngToLayerPoint:function(A){var t=this.project(dA(A))._round();return t._subtract(this.getPixelOrigin())},wrapLatLng:function(A){return this.options.crs.wrapLatLng(dA(A))},wrapLatLngBounds:function(A){return this.options.crs.wrapLatLngBounds(SA(A))},distance:function(A,t){return this.options.crs.distance(dA(A),dA(t))},containerPointToLayerPoint:function(A){return eA(A).subtract(this._getMapPanePos())},layerPointToContainerPoint:function(A){return eA(A).add(this._getMapPanePos())},containerPointToLatLng:function(A){var t=this.containerPointToLayerPoint(eA(A));return this.layerPointToLatLng(t)},latLngToContainerPoint:function(A){return this.layerPointToContainerPoint(this.latLngToLayerPoint(dA(A)))},mouseEventToContainerPoint:function(A){return Dn(A,this._container)},mouseEventToLayerPoint:function(A){return this.containerPointToLayerPoint(this.mouseEventToContainerPoint(A))},mouseEventToLatLng:function(A){return this.layerPointToLatLng(this.mouseEventToLayerPoint(A))},_initContainer:function(A){var t=this._container=fr(A);if(t){if(t._leaflet_id)throw new Error("Map container is already initialized.")}else throw new Error("Map container not found.");oA(t,"scroll",this._onScroll,this),this._containerId=C(t)},_initLayout:function(){var A=this._container;this._fadeAnimated=this.options.fadeAnimation&&z.any3d,iA(A,"leaflet-container"+(z.touch?" leaflet-touch":"")+(z.retina?" leaflet-retina":"")+(z.ielt9?" leaflet-oldie":"")+(z.safari?" leaflet-safari":"")+(this._fadeAnimated?" leaflet-fade-anim":""));var t=Vt(A,"position");t!=="absolute"&&t!=="relative"&&t!=="fixed"&&t!=="sticky"&&(A.style.position="relative"),this._initPanes(),this._initControlPos&&this._initControlPos()},_initPanes:function(){var A=this._panes={};this._paneRenderers={},this._mapPane=this.createPane("mapPane",this._container),OA(this._mapPane,new rA(0,0)),this.createPane("tilePane"),this.createPane("overlayPane"),this.createPane("shadowPane"),this.createPane("markerPane"),this.createPane("tooltipPane"),this.createPane("popupPane"),this.options.markerZoomAnimation||(iA(A.markerPane,"leaflet-zoom-hide"),iA(A.shadowPane,"leaflet-zoom-hide"))},_resetView:function(A,t,r){OA(this._mapPane,new rA(0,0));var s=!this._loaded;this._loaded=!0,t=this._limitZoom(t),this.fire("viewprereset");var c=this._zoom!==t;this._moveStart(c,r)._move(A,t)._moveEnd(c),this.fire("viewreset"),s&&this.fire("load")},_moveStart:function(A,t){return A&&this.fire("zoomstart"),t||this.fire("movestart"),this},_move:function(A,t,r,s){t===void 0&&(t=this._zoom);var c=this._zoom!==t;return this._zoom=t,this._lastCenter=A,this._pixelOrigin=this._getNewPixelOrigin(A),s?r&&r.pinch&&this.fire("zoom",r):((c||r&&r.pinch)&&this.fire("zoom",r),this.fire("move",r)),this},_moveEnd:function(A){return A&&this.fire("zoomend"),this.fire("moveend")},_stop:function(){return it(this._flyToFrame),this._panAnim&&this._panAnim.stop(),this},_rawPanBy:function(A){OA(this._mapPane,this._getMapPanePos().subtract(A))},_getZoomSpan:function(){return this.getMaxZoom()-this.getMinZoom()},_panInsideMaxBounds:function(){this._enforcingBounds||this.panInsideBounds(this.options.maxBounds)},_checkIfLoaded:function(){if(!this._loaded)throw new Error("Set map center and zoom first.")},_initEvents:function(A){this._targets={},this._targets[C(this._container)]=this;var t=A?_A:oA;t(this._container,"click dblclick mousedown mouseup mouseover mouseout mousemove contextmenu keypress keydown keyup",this._handleDOMEvent,this),this.options.trackResize&&t(window,"resize",this._onResize,this),z.any3d&&this.options.transform3DLimit&&(A?this.off:this.on).call(this,"moveend",this._onMoveEnd)},_onResize:function(){it(this._resizeRequest),this._resizeRequest=et(function(){this.invalidateSize({debounceMoveend:!0})},this)},_onScroll:function(){this._container.scrollTop=0,this._container.scrollLeft=0},_onMoveEnd:function(){var A=this._getMapPanePos();Math.max(Math.abs(A.x),Math.abs(A.y))>=this.options.transform3DLimit&&this._resetView(this.getCenter(),this.getZoom())},_findEventTargets:function(A,t){for(var r=[],s,c=t==="mouseout"||t==="mouseover",h=A.target||A.srcElement,p=!1;h;){if(s=this._targets[C(h)],s&&(t==="click"||t==="preclick")&&this._draggableMoved(s)){p=!0;break}if(s&&s.listens(t,!0)&&(c&&!kn(h,A)||(r.push(s),c))||h===this._container)break;h=h.parentNode}return!r.length&&!p&&!c&&this.listens(t,!0)&&(r=[this]),r},_isClickDisabled:function(A){for(;A&&A!==this._container;){if(A._leaflet_disable_click)return!0;A=A.parentNode}},_handleDOMEvent:function(A){var t=A.target||A.srcElement;if(!(!this._loaded||t._leaflet_disable_events||A.type==="click"&&this._isClickDisabled(t))){var r=A.type;r==="mousedown"&&Kt(t),this._fireDOMEvent(A,r)}},_mouseEvents:["click","dblclick","mouseover","mouseout","contextmenu"],_fireDOMEvent:function(A,t,r){if(A.type==="click"){var s=I({},A);s.type="preclick",this._fireDOMEvent(s,s.type,r)}var c=this._findEventTargets(A,t);if(r){for(var h=[],p=0;p<r.length;p++)r[p].listens(t,!0)&&h.push(r[p]);c=h.concat(c)}if(c.length){t==="contextmenu"&&GA(A);var U=c[0],y={originalEvent:A};if(A.type!=="keypress"&&A.type!=="keydown"&&A.type!=="keyup"){var H=U.getLatLng&&(!U._radius||U._radius<=10);y.containerPoint=H?this.latLngToContainerPoint(U.getLatLng()):this.mouseEventToContainerPoint(A),y.layerPoint=this.containerPointToLayerPoint(y.containerPoint),y.latlng=H?U.getLatLng():this.layerPointToLatLng(y.layerPoint)}for(p=0;p<c.length;p++)if(c[p].fire(t,y,!0),y.originalEvent._stopped||c[p].options.bubblingMouseEvents===!1&&ve(this._mouseEvents,t)!==-1)return}},_draggableMoved:function(A){return A=A.dragging&&A.dragging.enabled()?A:this,A.dragging&&A.dragging.moved()||this.boxZoom&&this.boxZoom.moved()},_clearHandlers:function(){for(var A=0,t=this._handlers.length;A<t;A++)this._handlers[A].disable()},whenReady:function(A,t){return this._loaded?A.call(t||this,{target:this}):this.on("load",A,t),this},_getMapPanePos:function(){return se(this._mapPane)||new rA(0,0)},_moved:function(){var A=this._getMapPanePos();return A&&!A.equals([0,0])},_getTopLeftPoint:function(A,t){var r=A&&t!==void 0?this._getNewPixelOrigin(A,t):this.getPixelOrigin();return r.subtract(this._getMapPanePos())},_getNewPixelOrigin:function(A,t){var r=this.getSize()._divideBy(2);return this.project(A,t)._subtract(r)._add(this._getMapPanePos())._round()},_latLngToNewLayerPoint:function(A,t,r){var s=this._getNewPixelOrigin(r,t);return this.project(A,t)._subtract(s)},_latLngBoundsToNewLayerBounds:function(A,t,r){var s=this._getNewPixelOrigin(r,t);return nt([this.project(A.getSouthWest(),t)._subtract(s),this.project(A.getNorthWest(),t)._subtract(s),this.project(A.getSouthEast(),t)._subtract(s),this.project(A.getNorthEast(),t)._subtract(s)])},_getCenterLayerPoint:function(){return this.containerPointToLayerPoint(this.getSize()._divideBy(2))},_getCenterOffset:function(A){return this.latLngToLayerPoint(A).subtract(this._getCenterLayerPoint())},_limitCenter:function(A,t,r){if(!r)return A;var s=this.project(A,t),c=this.getSize().divideBy(2),h=new EA(s.subtract(c),s.add(c)),p=this._getBoundsOffset(h,r,t);return Math.abs(p.x)<=1&&Math.abs(p.y)<=1?A:this.unproject(s.add(p),t)},_limitOffset:function(A,t){if(!t)return A;var r=this.getPixelBounds(),s=new EA(r.min.add(A),r.max.add(A));return A.add(this._getBoundsOffset(s,t))},_getBoundsOffset:function(A,t,r){var s=nt(this.project(t.getNorthEast(),r),this.project(t.getSouthWest(),r)),c=s.min.subtract(A.min),h=s.max.subtract(A.max),p=this._rebound(c.x,-h.x),U=this._rebound(c.y,-h.y);return new rA(p,U)},_rebound:function(A,t){return A+t>0?Math.round(A-t)/2:Math.max(0,Math.ceil(A))-Math.max(0,Math.floor(t))},_limitZoom:function(A){var t=this.getMinZoom(),r=this.getMaxZoom(),s=z.any3d?this.options.zoomSnap:1;return s&&(A=Math.round(A/s)*s),Math.max(t,Math.min(r,A))},_onPanTransitionStep:function(){this.fire("move")},_onPanTransitionEnd:function(){bA(this._mapPane,"leaflet-pan-anim"),this.fire("moveend")},_tryAnimatedPan:function(A,t){var r=this._getCenterOffset(A)._trunc();return(t&&t.animate)!==!0&&!this.getSize().contains(r)?!1:(this.panBy(r,t),!0)},_createAnimProxy:function(){var A=this._proxy=lA("div","leaflet-proxy leaflet-zoom-animated");this._panes.mapPane.appendChild(A),this.on("zoomanim",function(t){var r=Ti,s=this._proxy.style[r];oe(this._proxy,this.project(t.center,t.zoom),this.getZoomScale(t.zoom,1)),s===this._proxy.style[r]&&this._animatingZoom&&this._onZoomTransitionEnd()},this),this.on("load moveend",this._animMoveEnd,this),this._on("unload",this._destroyAnimProxy,this)},_destroyAnimProxy:function(){xA(this._proxy),this.off("load moveend",this._animMoveEnd,this),delete this._proxy},_animMoveEnd:function(){var A=this.getCenter(),t=this.getZoom();oe(this._proxy,this.project(A,t),this.getZoomScale(t,1))},_catchTransitionEnd:function(A){this._animatingZoom&&A.propertyName.indexOf("transform")>=0&&this._onZoomTransitionEnd()},_nothingToAnimate:function(){return!this._container.getElementsByClassName("leaflet-zoom-animated").length},_tryAnimatedZoom:function(A,t,r){if(this._animatingZoom)return!0;if(r=r||{},!this._zoomAnimated||r.animate===!1||this._nothingToAnimate()||Math.abs(t-this._zoom)>this.options.zoomAnimationThreshold)return!1;var s=this.getZoomScale(t),c=this._getCenterOffset(A)._divideBy(1-1/s);return r.animate!==!0&&!this.getSize().contains(c)?!1:(et(function(){this._moveStart(!0,r.noMoveStart||!1)._animateZoom(A,t,!0)},this),!0)},_animateZoom:function(A,t,r,s){this._mapPane&&(r&&(this._animatingZoom=!0,this._animateToCenter=A,this._animateToZoom=t,iA(this._mapPane,"leaflet-zoom-anim")),this.fire("zoomanim",{center:A,zoom:t,noUpdate:s}),this._tempFireZoomEvent||(this._tempFireZoomEvent=this._zoom!==this._animateToZoom),this._move(this._animateToCenter,this._animateToZoom,void 0,!0),setTimeout(g(this._onZoomTransitionEnd,this),250))},_onZoomTransitionEnd:function(){this._animatingZoom&&(this._mapPane&&bA(this._mapPane,"leaflet-zoom-anim"),this._animatingZoom=!1,this._move(this._animateToCenter,this._animateToZoom,void 0,!0),this._tempFireZoomEvent&&this.fire("zoom"),delete this._tempFireZoomEvent,this.fire("move"),this._moveEnd(!0))}});function be(A,t){return new BA(A,t)}var Qt=St.extend({options:{position:"topright"},initialize:function(A){X(this,A)},getPosition:function(){return this.options.position},setPosition:function(A){var t=this._map;return t&&t.removeControl(this),this.options.position=A,t&&t.addControl(this),this},getContainer:function(){return this._container},addTo:function(A){this.remove(),this._map=A;var t=this._container=this.onAdd(A),r=this.getPosition(),s=A._controlCorners[r];return iA(t,"leaflet-control"),r.indexOf("bottom")!==-1?s.insertBefore(t,s.firstChild):s.appendChild(t),this._map.on("unload",this.remove,this),this},remove:function(){return this._map?(xA(this._container),this.onRemove&&this.onRemove(this._map),this._map.off("unload",this.remove,this),this._map=null,this):this},_refocusOnMap:function(A){this._map&&A&&A.screenX>0&&A.screenY>0&&this._map.getContainer().focus()}}),ni=function(A){return new Qt(A)};BA.include({addControl:function(A){return A.addTo(this),this},removeControl:function(A){return A.remove(),this},_initControlPos:function(){var A=this._controlCorners={},t="leaflet-",r=this._controlContainer=lA("div",t+"control-container",this._container);function s(c,h){var p=t+c+" "+t+h;A[c+h]=lA("div",p,r)}s("top","left"),s("top","right"),s("bottom","left"),s("bottom","right")},_clearControlPos:function(){for(var A in this._controlCorners)xA(this._controlCorners[A]);xA(this._controlContainer),delete this._controlCorners,delete this._controlContainer}});var wr=Qt.extend({options:{collapsed:!0,position:"topright",autoZIndex:!0,hideSingleBase:!1,sortLayers:!1,sortFunction:function(A,t,r,s){return r<s?-1:s<r?1:0}},initialize:function(A,t,r){X(this,r),this._layerControlInputs=[],this._layers=[],this._lastZIndex=0,this._handlingClick=!1,this._preventClick=!1;for(var s in A)this._addLayer(A[s],s);for(s in t)this._addLayer(t[s],s,!0)},onAdd:function(A){this._initLayout(),this._update(),this._map=A,A.on("zoomend",this._checkDisabledLayers,this);for(var t=0;t<this._layers.length;t++)this._layers[t].layer.on("add remove",this._onLayerChange,this);return this._container},addTo:function(A){return Qt.prototype.addTo.call(this,A),this._expandIfNotCollapsed()},onRemove:function(){this._map.off("zoomend",this._checkDisabledLayers,this);for(var A=0;A<this._layers.length;A++)this._layers[A].layer.off("add remove",this._onLayerChange,this)},addBaseLayer:function(A,t){return this._addLayer(A,t),this._map?this._update():this},addOverlay:function(A,t){return this._addLayer(A,t,!0),this._map?this._update():this},removeLayer:function(A){A.off("add remove",this._onLayerChange,this);var t=this._getLayer(C(A));return t&&this._layers.splice(this._layers.indexOf(t),1),this._map?this._update():this},expand:function(){iA(this._container,"leaflet-control-layers-expanded"),this._section.style.height=null;var A=this._map.getSize().y-(this._container.offsetTop+50);return A<this._section.clientHeight?(iA(this._section,"leaflet-control-layers-scrollbar"),this._section.style.height=A+"px"):bA(this._section,"leaflet-control-layers-scrollbar"),this._checkDisabledLayers(),this},collapse:function(){return bA(this._container,"leaflet-control-layers-expanded"),this},_initLayout:function(){var A="leaflet-control-layers",t=this._container=lA("div",A),r=this.options.collapsed;t.setAttribute("aria-haspopup",!0),Le(t),Ki(t);var s=this._section=lA("section",A+"-list");r&&(this._map.on("click",this.collapse,this),oA(t,{mouseenter:this._expandSafely,mouseleave:this.collapse},this));var c=this._layersLink=lA("a",A+"-toggle",t);c.href="#",c.title="Layers",c.setAttribute("role","button"),oA(c,{keydown:function(h){h.keyCode===13&&this._expandSafely()},click:function(h){GA(h),this._expandSafely()}},this),r||this.expand(),this._baseLayersList=lA("div",A+"-base",s),this._separator=lA("div",A+"-separator",s),this._overlaysList=lA("div",A+"-overlays",s),t.appendChild(s)},_getLayer:function(A){for(var t=0;t<this._layers.length;t++)if(this._layers[t]&&C(this._layers[t].layer)===A)return this._layers[t]},_addLayer:function(A,t,r){this._map&&A.on("add remove",this._onLayerChange,this),this._layers.push({layer:A,name:t,overlay:r}),this.options.sortLayers&&this._layers.sort(g(function(s,c){return this.options.sortFunction(s.layer,c.layer,s.name,c.name)},this)),this.options.autoZIndex&&A.setZIndex&&(this._lastZIndex++,A.setZIndex(this._lastZIndex)),this._expandIfNotCollapsed()},_update:function(){if(!this._container)return this;Pi(this._baseLayersList),Pi(this._overlaysList),this._layerControlInputs=[];var A,t,r,s,c=0;for(r=0;r<this._layers.length;r++)s=this._layers[r],this._addItem(s),t=t||s.overlay,A=A||!s.overlay,c+=s.overlay?0:1;return this.options.hideSingleBase&&(A=A&&c>1,this._baseLayersList.style.display=A?"":"none"),this._separator.style.display=t&&A?"":"none",this},_onLayerChange:function(A){this._handlingClick||this._update();var t=this._getLayer(C(A.target)),r=t.overlay?A.type==="add"?"overlayadd":"overlayremove":A.type==="add"?"baselayerchange":null;r&&this._map.fire(r,t)},_createRadioElement:function(A,t){var r='<input type="radio" class="leaflet-control-layers-selector" name="'+A+'"'+(t?' checked="checked"':"")+"/>",s=document.createElement("div");return s.innerHTML=r,s.firstChild},_addItem:function(A){var t=document.createElement("label"),r=this._map.hasLayer(A.layer),s;A.overlay?(s=document.createElement("input"),s.type="checkbox",s.className="leaflet-control-layers-selector",s.defaultChecked=r):s=this._createRadioElement("leaflet-base-layers_"+C(this),r),this._layerControlInputs.push(s),s.layerId=C(A.layer),oA(s,"click",this._onInputClick,this);var c=document.createElement("span");c.innerHTML=" "+A.name;var h=document.createElement("span");t.appendChild(h),h.appendChild(s),h.appendChild(c);var p=A.overlay?this._overlaysList:this._baseLayersList;return p.appendChild(t),this._checkDisabledLayers(),t},_onInputClick:function(){if(!this._preventClick){var A=this._layerControlInputs,t,r,s=[],c=[];this._handlingClick=!0;for(var h=A.length-1;h>=0;h--)t=A[h],r=this._getLayer(t.layerId).layer,t.checked?s.push(r):t.checked||c.push(r);for(h=0;h<c.length;h++)this._map.hasLayer(c[h])&&this._map.removeLayer(c[h]);for(h=0;h<s.length;h++)this._map.hasLayer(s[h])||this._map.addLayer(s[h]);this._handlingClick=!1,this._refocusOnMap()}},_checkDisabledLayers:function(){for(var A=this._layerControlInputs,t,r,s=this._map.getZoom(),c=A.length-1;c>=0;c--)t=A[c],r=this._getLayer(t.layerId).layer,t.disabled=r.options.minZoom!==void 0&&s<r.options.minZoom||r.options.maxZoom!==void 0&&s>r.options.maxZoom},_expandIfNotCollapsed:function(){return this._map&&!this.options.collapsed&&this.expand(),this},_expandSafely:function(){var A=this._section;this._preventClick=!0,oA(A,"click",GA),this.expand();var t=this;setTimeout(function(){_A(A,"click",GA),t._preventClick=!1})}}),Po=function(A,t,r){return new wr(A,t,r)},Me=Qt.extend({options:{position:"topleft",zoomInText:'<span aria-hidden="true">+</span>',zoomInTitle:"Zoom in",zoomOutText:'<span aria-hidden="true">&#x2212;</span>',zoomOutTitle:"Zoom out"},onAdd:function(A){var t="leaflet-control-zoom",r=lA("div",t+" leaflet-bar"),s=this.options;return this._zoomInButton=this._createButton(s.zoomInText,s.zoomInTitle,t+"-in",r,this._zoomIn),this._zoomOutButton=this._createButton(s.zoomOutText,s.zoomOutTitle,t+"-out",r,this._zoomOut),this._updateDisabled(),A.on("zoomend zoomlevelschange",this._updateDisabled,this),r},onRemove:function(A){A.off("zoomend zoomlevelschange",this._updateDisabled,this)},disable:function(){return this._disabled=!0,this._updateDisabled(),this},enable:function(){return this._disabled=!1,this._updateDisabled(),this},_zoomIn:function(A){!this._disabled&&this._map._zoom<this._map.getMaxZoom()&&this._map.zoomIn(this._map.options.zoomDelta*(A.shiftKey?3:1))},_zoomOut:function(A){!this._disabled&&this._map._zoom>this._map.getMinZoom()&&this._map.zoomOut(this._map.options.zoomDelta*(A.shiftKey?3:1))},_createButton:function(A,t,r,s,c){var h=lA("a",r,s);return h.innerHTML=A,h.href="#",h.title=t,h.setAttribute("role","button"),h.setAttribute("aria-label",t),Le(h),oA(h,"click",ae),oA(h,"click",c,this),oA(h,"click",this._refocusOnMap,this),h},_updateDisabled:function(){var A=this._map,t="leaflet-disabled";bA(this._zoomInButton,t),bA(this._zoomOutButton,t),this._zoomInButton.setAttribute("aria-disabled","false"),this._zoomOutButton.setAttribute("aria-disabled","false"),(this._disabled||A._zoom===A.getMinZoom())&&(iA(this._zoomOutButton,t),this._zoomOutButton.setAttribute("aria-disabled","true")),(this._disabled||A._zoom===A.getMaxZoom())&&(iA(this._zoomInButton,t),this._zoomInButton.setAttribute("aria-disabled","true"))}});BA.mergeOptions({zoomControl:!0}),BA.addInitHook(function(){this.options.zoomControl&&(this.zoomControl=new Me,this.addControl(this.zoomControl))});var ki=function(A){return new Me(A)},Nn=Qt.extend({options:{position:"bottomleft",maxWidth:100,metric:!0,imperial:!0},onAdd:function(A){var t="leaflet-control-scale",r=lA("div",t),s=this.options;return this._addScales(s,t+"-line",r),A.on(s.updateWhenIdle?"moveend":"move",this._update,this),A.whenReady(this._update,this),r},onRemove:function(A){A.off(this.options.updateWhenIdle?"moveend":"move",this._update,this)},_addScales:function(A,t,r){A.metric&&(this._mScale=lA("div",t,r)),A.imperial&&(this._iScale=lA("div",t,r))},_update:function(){var A=this._map,t=A.getSize().y/2,r=A.distance(A.containerPointToLatLng([0,t]),A.containerPointToLatLng([this.options.maxWidth,t]));this._updateScales(r)},_updateScales:function(A){this.options.metric&&A&&this._updateMetric(A),this.options.imperial&&A&&this._updateImperial(A)},_updateMetric:function(A){var t=this._getRoundNum(A),r=t<1e3?t+" m":t/1e3+" km";this._updateScale(this._mScale,r,t/A)},_updateImperial:function(A){var t=A*3.2808399,r,s,c;t>5280?(r=t/5280,s=this._getRoundNum(r),this._updateScale(this._iScale,s+" mi",s/r)):(c=this._getRoundNum(t),this._updateScale(this._iScale,c+" ft",c/t))},_updateScale:function(A,t,r){A.style.width=Math.round(this.options.maxWidth*r)+"px",A.innerHTML=t},_getRoundNum:function(A){var t=Math.pow(10,(Math.floor(A)+"").length-1),r=A/t;return r=r>=10?10:r>=5?5:r>=3?3:r>=2?2:1,t*r}}),So=function(A){return new Nn(A)},Wt='<svg aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="12" height="8" viewBox="0 0 12 8" class="leaflet-attribution-flag"><path fill="#4C7BE1" d="M0 0h12v4H0z"/><path fill="#FFD500" d="M0 4h12v3H0z"/><path fill="#E0BC00" d="M0 7h12v1H0z"/></svg>',Te=Qt.extend({options:{position:"bottomright",prefix:'<a href="https://leafletjs.com" title="A JavaScript library for interactive maps">'+(z.inlineSvg?Wt+" ":"")+"Leaflet</a>"},initialize:function(A){X(this,A),this._attributions={}},onAdd:function(A){A.attributionControl=this,this._container=lA("div","leaflet-control-attribution"),Le(this._container);for(var t in A._layers)A._layers[t].getAttribution&&this.addAttribution(A._layers[t].getAttribution());return this._update(),A.on("layeradd",this._addAttribution,this),this._container},onRemove:function(A){A.off("layeradd",this._addAttribution,this)},_addAttribution:function(A){A.layer.getAttribution&&(this.addAttribution(A.layer.getAttribution()),A.layer.once("remove",function(){this.removeAttribution(A.layer.getAttribution())},this))},setPrefix:function(A){return this.options.prefix=A,this._update(),this},addAttribution:function(A){return A?(this._attributions[A]||(this._attributions[A]=0),this._attributions[A]++,this._update(),this):this},removeAttribution:function(A){return A?(this._attributions[A]&&(this._attributions[A]--,this._update()),this):this},_update:function(){if(this._map){var A=[];for(var t in this._attributions)this._attributions[t]&&A.push(t);var r=[];this.options.prefix&&r.push(this.options.prefix),A.length&&r.push(A.join(", ")),this._container.innerHTML=r.join(' <span aria-hidden="true">|</span> ')}}});BA.mergeOptions({attributionControl:!0}),BA.addInitHook(function(){this.options.attributionControl&&new Te().addTo(this)});var Rn=function(A){return new Te(A)};Qt.Layers=wr,Qt.Zoom=Me,Qt.Scale=Nn,Qt.Attribution=Te,ni.layers=Po,ni.zoom=ki,ni.scale=So,ni.attribution=Rn;var Et=St.extend({initialize:function(A){this._map=A},enable:function(){return this._enabled?this:(this._enabled=!0,this.addHooks(),this)},disable:function(){return this._enabled?(this._enabled=!1,this.removeHooks(),this):this},enabled:function(){return!!this._enabled}});Et.addTo=function(A,t){return A.addHandler(t,this),this};var Oo={Events:ct},mr=z.touch?"touchstart mousedown":"mousedown",Xt=ze.extend({options:{clickTolerance:3},initialize:function(A,t,r,s){X(this,s),this._element=A,this._dragStartTarget=t||A,this._preventOutline=r},enable:function(){this._enabled||(oA(this._dragStartTarget,mr,this._onDown,this),this._enabled=!0)},disable:function(){this._enabled&&(Xt._dragging===this&&this.finishDrag(!0),_A(this._dragStartTarget,mr,this._onDown,this),this._enabled=!1,this._moved=!1)},_onDown:function(A){if(this._enabled&&(this._moved=!1,!xe(this._element,"leaflet-zoom-anim"))){if(A.touches&&A.touches.length!==1){Xt._dragging===this&&this.finishDrag();return}if(!(Xt._dragging||A.shiftKey||A.which!==1&&A.button!==1&&!A.touches)&&(Xt._dragging=this,this._preventOutline&&Kt(this._element),Mn(),Ai(),!this._moving)){this.fire("down");var t=A.touches?A.touches[0]:A,r=dr(this._element);this._startPoint=new rA(t.clientX,t.clientY),this._startPos=se(this._element),this._parentScale=Pn(r);var s=A.type==="mousedown";oA(document,s?"mousemove":"touchmove",this._onMove,this),oA(document,s?"mouseup":"touchend touchcancel",this._onUp,this)}}},_onMove:function(A){if(this._enabled){if(A.touches&&A.touches.length>1){this._moved=!0;return}var t=A.touches&&A.touches.length===1?A.touches[0]:A,r=new rA(t.clientX,t.clientY)._subtract(this._startPoint);!r.x&&!r.y||Math.abs(r.x)+Math.abs(r.y)<this.options.clickTolerance||(r.x/=this._parentScale.x,r.y/=this._parentScale.y,GA(A),this._moved||(this.fire("dragstart"),this._moved=!0,iA(document.body,"leaflet-dragging"),this._lastTarget=A.target||A.srcElement,window.SVGElementInstance&&this._lastTarget instanceof window.SVGElementInstance&&(this._lastTarget=this._lastTarget.correspondingUseElement),iA(this._lastTarget,"leaflet-drag-target")),this._newPos=this._startPos.add(r),this._moving=!0,this._lastEvent=A,this._updatePosition())}},_updatePosition:function(){var A={originalEvent:this._lastEvent};this.fire("predrag",A),OA(this._element,this._newPos),this.fire("drag",A)},_onUp:function(){this._enabled&&this.finishDrag()},finishDrag:function(A){bA(document.body,"leaflet-dragging"),this._lastTarget&&(bA(this._lastTarget,"leaflet-drag-target"),this._lastTarget=null),_A(document,"mousemove touchmove",this._onMove,this),_A(document,"mouseup touchend touchcancel",this._onUp,this),Tn(),Zt();var t=this._moved&&this._moving;this._moving=!1,Xt._dragging=!1,t&&this.fire("dragend",{noInertia:A,distance:this._newPos.distanceTo(this._startPos)})}});function Cr(A,t,r){var s,c=[1,4,2,8],h,p,U,y,H,P,q,aA;for(h=0,P=A.length;h<P;h++)A[h]._code=ce(A[h],t);for(U=0;U<4;U++){for(q=c[U],s=[],h=0,P=A.length,p=P-1;h<P;p=h++)y=A[h],H=A[p],y._code&q?H._code&q||(aA=Ni(H,y,q,t,r),aA._code=ce(aA,t),s.push(aA)):(H._code&q&&(aA=Ni(H,y,q,t,r),aA._code=ce(aA,t),s.push(aA)),s.push(y));A=s}return A}function vr(A,t){var r,s,c,h,p,U,y,H,P;if(!A||A.length===0)throw new Error("latlngs not passed");ft(A)||(console.warn("latlngs are not flat! Only the first ring will be used"),A=A[0]);var q=dA([0,0]),aA=SA(A),jA=aA.getNorthWest().distanceTo(aA.getSouthWest())*aA.getNorthEast().distanceTo(aA.getNorthWest());jA<1700&&(q=Gn(A));var NA=A.length,gt=[];for(r=0;r<NA;r++){var st=dA(A[r]);gt.push(t.project(dA([st.lat-q.lat,st.lng-q.lng])))}for(U=y=H=0,r=0,s=NA-1;r<NA;s=r++)c=gt[r],h=gt[s],p=c.y*h.x-h.y*c.x,y+=(c.x+h.x)*p,H+=(c.y+h.y)*p,U+=p*3;U===0?P=gt[0]:P=[y/U,H/U];var Ke=t.unproject(eA(P));return dA([Ke.lat+q.lat,Ke.lng+q.lng])}function Gn(A){for(var t=0,r=0,s=0,c=0;c<A.length;c++){var h=dA(A[c]);t+=h.lat,r+=h.lng,s++}return dA([t/s,r/s])}var Do={__proto__:null,clipPolygon:Cr,polygonCenter:vr,centroid:Gn};function Qr(A,t){if(!t||!A.length)return A.slice();var r=t*t;return A=No(A,r),A=ko(A,r),A}function _r(A,t,r){return Math.sqrt(ri(A,t,r,!0))}function Ko(A,t,r){return ri(A,t,r)}function ko(A,t){var r=A.length,s=typeof Uint8Array<"u"?Uint8Array:Array,c=new s(r);c[0]=c[r-1]=1,Pe(A,c,t,0,r-1);var h,p=[];for(h=0;h<r;h++)c[h]&&p.push(A[h]);return p}function Pe(A,t,r,s,c){var h=0,p,U,y;for(U=s+1;U<=c-1;U++)y=ri(A[U],A[s],A[c],!0),y>h&&(p=U,h=y);h>r&&(t[p]=1,Pe(A,t,r,s,p),Pe(A,t,r,p,c))}function No(A,t){for(var r=[A[0]],s=1,c=0,h=A.length;s<h;s++)Ro(A[s],A[c])>t&&(r.push(A[s]),c=s);return c<h-1&&r.push(A[h-1]),r}var Ur;function Fr(A,t,r,s,c){var h=s?Ur:ce(A,r),p=ce(t,r),U,y,H;for(Ur=p;;){if(!(h|p))return[A,t];if(h&p)return!1;U=h||p,y=Ni(A,t,U,r,c),H=ce(y,r),U===h?(A=y,h=H):(t=y,p=H)}}function Ni(A,t,r,s,c){var h=t.x-A.x,p=t.y-A.y,U=s.min,y=s.max,H,P;return r&8?(H=A.x+h*(y.y-A.y)/p,P=y.y):r&4?(H=A.x+h*(U.y-A.y)/p,P=U.y):r&2?(H=y.x,P=A.y+p*(y.x-A.x)/h):r&1&&(H=U.x,P=A.y+p*(U.x-A.x)/h),new rA(H,P,c)}function ce(A,t){var r=0;return A.x<t.min.x?r|=1:A.x>t.max.x&&(r|=2),A.y<t.min.y?r|=4:A.y>t.max.y&&(r|=8),r}function Ro(A,t){var r=t.x-A.x,s=t.y-A.y;return r*r+s*s}function ri(A,t,r,s){var c=t.x,h=t.y,p=r.x-c,U=r.y-h,y=p*p+U*U,H;return y>0&&(H=((A.x-c)*p+(A.y-h)*U)/y,H>1?(c=r.x,h=r.y):H>0&&(c+=p*H,h+=U*H)),p=A.x-c,U=A.y-h,s?p*p+U*U:new rA(c,h)}function ft(A){return!fA(A[0])||typeof A[0][0]!="object"&&typeof A[0][0]<"u"}function Ri(A){return console.warn("Deprecated use of _flat, please use L.LineUtil.isFlat instead."),ft(A)}function Vn(A,t){var r,s,c,h,p,U,y,H;if(!A||A.length===0)throw new Error("latlngs not passed");ft(A)||(console.warn("latlngs are not flat! Only the first ring will be used"),A=A[0]);var P=dA([0,0]),q=SA(A),aA=q.getNorthWest().distanceTo(q.getSouthWest())*q.getNorthEast().distanceTo(q.getNorthWest());aA<1700&&(P=Gn(A));var jA=A.length,NA=[];for(r=0;r<jA;r++){var gt=dA(A[r]);NA.push(t.project(dA([gt.lat-P.lat,gt.lng-P.lng])))}for(r=0,s=0;r<jA-1;r++)s+=NA[r].distanceTo(NA[r+1])/2;if(s===0)H=NA[0];else for(r=0,h=0;r<jA-1;r++)if(p=NA[r],U=NA[r+1],c=p.distanceTo(U),h+=c,h>s){y=(h-s)/c,H=[U.x-y*(U.x-p.x),U.y-y*(U.y-p.y)];break}var st=t.unproject(eA(H));return dA([st.lat+P.lat,st.lng+P.lng])}var yr={__proto__:null,simplify:Qr,pointToSegmentDistance:_r,closestPointOnSegment:Ko,clipSegment:Fr,_getEdgeIntersection:Ni,_getBitCode:ce,_sqClosestPointOnSegment:ri,isFlat:ft,_flat:Ri,polylineCenter:Vn},le={project:function(A){return new rA(A.lng,A.lat)},unproject:function(A){return new CA(A.y,A.x)},bounds:new EA([-180,-90],[180,90])},Jt={R:6378137,R_MINOR:6356752314245179e-9,bounds:new EA([-2003750834279e-5,-1549657073972e-5],[2003750834279e-5,1876465623138e-5]),project:function(A){var t=Math.PI/180,r=this.R,s=A.lat*t,c=this.R_MINOR/r,h=Math.sqrt(1-c*c),p=h*Math.sin(s),U=Math.tan(Math.PI/4-s/2)/Math.pow((1-p)/(1+p),h/2);return s=-r*Math.log(Math.max(U,1e-10)),new rA(A.lng*t*r,s)},unproject:function(A){for(var t=180/Math.PI,r=this.R,s=this.R_MINOR/r,c=Math.sqrt(1-s*s),h=Math.exp(-A.y/r),p=Math.PI/2-2*Math.atan(h),U=0,y=.1,H;U<15&&Math.abs(y)>1e-7;U++)H=c*Math.sin(p),H=Math.pow((1-H)/(1+H),c/2),y=Math.PI/2-2*Math.atan(h*H)-p,p+=y;return new CA(p*t,A.x*t/r)}},vA={__proto__:null,LonLat:le,Mercator:Jt,SphericalMercator:wn},Go=I({},Gt,{code:"EPSG:3395",projection:Jt,transformation:function(){var A=.5/(Math.PI*Jt.R);return Qe(A,.5,-A,.5)}()}),Gi=I({},Gt,{code:"EPSG:4326",projection:le,transformation:Qe(1/180,1,-1/180,.5)}),Er=I({},lt,{projection:le,transformation:Qe(1,0,-1,0),scale:function(A){return Math.pow(2,A)},zoom:function(A){return Math.log(A)/Math.LN2},distance:function(A,t){var r=t.lng-A.lng,s=t.lat-A.lat;return Math.sqrt(r*r+s*s)},infinite:!0});lt.Earth=Gt,lt.EPSG3395=Go,lt.EPSG3857=mn,lt.EPSG900913=Uo,lt.EPSG4326=Gi,lt.Simple=Er;var JA=ze.extend({options:{pane:"overlayPane",attribution:null,bubblingMouseEvents:!0},addTo:function(A){return A.addLayer(this),this},remove:function(){return this.removeFrom(this._map||this._mapToAdd)},removeFrom:function(A){return A&&A.removeLayer(this),this},getPane:function(A){return this._map.getPane(A?this.options[A]||A:this.options.pane)},addInteractiveTarget:function(A){return this._map._targets[C(A)]=this,this},removeInteractiveTarget:function(A){return delete this._map._targets[C(A)],this},getAttribution:function(){return this.options.attribution},_layerAdd:function(A){var t=A.target;if(t.hasLayer(this)){if(this._map=t,this._zoomAnimated=t._zoomAnimated,this.getEvents){var r=this.getEvents();t.on(r,this),this.once("remove",function(){t.off(r,this)},this)}this.onAdd(t),this.fire("add"),t.fire("layeradd",{layer:this})}}});BA.include({addLayer:function(A){if(!A._layerAdd)throw new Error("The provided object is not a Layer.");var t=C(A);return this._layers[t]?this:(this._layers[t]=A,A._mapToAdd=this,A.beforeAdd&&A.beforeAdd(this),this.whenReady(A._layerAdd,A),this)},removeLayer:function(A){var t=C(A);return this._layers[t]?(this._loaded&&A.onRemove(this),delete this._layers[t],this._loaded&&(this.fire("layerremove",{layer:A}),A.fire("remove")),A._map=A._mapToAdd=null,this):this},hasLayer:function(A){return C(A)in this._layers},eachLayer:function(A,t){for(var r in this._layers)A.call(t,this._layers[r]);return this},_addLayers:function(A){A=A?fA(A)?A:[A]:[];for(var t=0,r=A.length;t<r;t++)this.addLayer(A[t])},_addZoomLimit:function(A){(!isNaN(A.options.maxZoom)||!isNaN(A.options.minZoom))&&(this._zoomBoundLayers[C(A)]=A,this._updateZoomLevels())},_removeZoomLimit:function(A){var t=C(A);this._zoomBoundLayers[t]&&(delete this._zoomBoundLayers[t],this._updateZoomLevels())},_updateZoomLevels:function(){var A=1/0,t=-1/0,r=this._getZoomSpan();for(var s in this._zoomBoundLayers){var c=this._zoomBoundLayers[s].options;A=c.minZoom===void 0?A:Math.min(A,c.minZoom),t=c.maxZoom===void 0?t:Math.max(t,c.maxZoom)}this._layersMaxZoom=t===-1/0?void 0:t,this._layersMinZoom=A===1/0?void 0:A,r!==this._getZoomSpan()&&this.fire("zoomlevelschange"),this.options.maxZoom===void 0&&this._layersMaxZoom&&this.getZoom()>this._layersMaxZoom&&this.setZoom(this._layersMaxZoom),this.options.minZoom===void 0&&this._layersMinZoom&&this.getZoom()<this._layersMinZoom&&this.setZoom(this._layersMinZoom)}});var $A=JA.extend({initialize:function(A,t){X(this,t),this._layers={};var r,s;if(A)for(r=0,s=A.length;r<s;r++)this.addLayer(A[r])},addLayer:function(A){var t=this.getLayerId(A);return this._layers[t]=A,this._map&&this._map.addLayer(A),this},removeLayer:function(A){var t=A in this._layers?A:this.getLayerId(A);return this._map&&this._layers[t]&&this._map.removeLayer(this._layers[t]),delete this._layers[t],this},hasLayer:function(A){var t=typeof A=="number"?A:this.getLayerId(A);return t in this._layers},clearLayers:function(){return this.eachLayer(this.removeLayer,this)},invoke:function(A){var t=Array.prototype.slice.call(arguments,1),r,s;for(r in this._layers)s=this._layers[r],s[A]&&s[A].apply(s,t);return this},onAdd:function(A){this.eachLayer(A.addLayer,A)},onRemove:function(A){this.eachLayer(A.removeLayer,A)},eachLayer:function(A,t){for(var r in this._layers)A.call(t,this._layers[r]);return this},getLayer:function(A){return this._layers[A]},getLayers:function(){var A=[];return this.eachLayer(A.push,A),A},setZIndex:function(A){return this.invoke("setZIndex",A)},getLayerId:function(A){return C(A)}}),Vo=function(A,t){return new $A(A,t)},YA=$A.extend({addLayer:function(A){return this.hasLayer(A)?this:(A.addEventParent(this),$A.prototype.addLayer.call(this,A),this.fire("layeradd",{layer:A}))},removeLayer:function(A){return this.hasLayer(A)?(A in this._layers&&(A=this._layers[A]),A.removeEventParent(this),$A.prototype.removeLayer.call(this,A),this.fire("layerremove",{layer:A})):this},setStyle:function(A){return this.invoke("setStyle",A)},bringToFront:function(){return this.invoke("bringToFront")},bringToBack:function(){return this.invoke("bringToBack")},getBounds:function(){var A=new rt;for(var t in this._layers){var r=this._layers[t];A.extend(r.getBounds?r.getBounds():r.getLatLng())}return A}}),DA=function(A,t){return new YA(A,t)},ue=St.extend({options:{popupAnchor:[0,0],tooltipAnchor:[0,0],crossOrigin:!1},initialize:function(A){X(this,A)},createIcon:function(A){return this._createIcon("icon",A)},createShadow:function(A){return this._createIcon("shadow",A)},_createIcon:function(A,t){var r=this._getIconUrl(A);if(!r){if(A==="icon")throw new Error("iconUrl not set in Icon options (see the docs).");return null}var s=this._createImg(r,t&&t.tagName==="IMG"?t:null);return this._setIconStyles(s,A),(this.options.crossOrigin||this.options.crossOrigin==="")&&(s.crossOrigin=this.options.crossOrigin===!0?"":this.options.crossOrigin),s},_setIconStyles:function(A,t){var r=this.options,s=r[t+"Size"];typeof s=="number"&&(s=[s,s]);var c=eA(s),h=eA(t==="shadow"&&r.shadowAnchor||r.iconAnchor||c&&c.divideBy(2,!0));A.className="leaflet-marker-"+t+" "+(r.className||""),h&&(A.style.marginLeft=-h.x+"px",A.style.marginTop=-h.y+"px"),c&&(A.style.width=c.x+"px",A.style.height=c.y+"px")},_createImg:function(A,t){return t=t||document.createElement("img"),t.src=A,t},_getIconUrl:function(A){return z.retina&&this.options[A+"RetinaUrl"]||this.options[A+"Url"]}});function ZA(A){return new ue(A)}var he=ue.extend({options:{iconUrl:"marker-icon.png",iconRetinaUrl:"marker-icon-2x.png",shadowUrl:"marker-shadow.png",iconSize:[25,41],iconAnchor:[12,41],popupAnchor:[1,-34],tooltipAnchor:[16,-28],shadowSize:[41,41]},_getIconUrl:function(A){return typeof he.imagePath!="string"&&(he.imagePath=this._detectIconPath()),(this.options.imagePath||he.imagePath)+ue.prototype._getIconUrl.call(this,A)},_stripUrl:function(A){var t=function(r,s,c){var h=s.exec(r);return h&&h[c]};return A=t(A,/^url\((['"])?(.+)\1\)$/,2),A&&t(A,/^(.*)marker-icon\.png$/,1)},_detectIconPath:function(){var A=lA("div","leaflet-default-icon-path",document.body),t=Vt(A,"background-image")||Vt(A,"backgroundImage");if(document.body.removeChild(A),t=this._stripUrl(t),t)return t;var r=document.querySelector('link[href$="leaflet.css"]');return r?r.href.substring(0,r.href.length-11-1):""}}),kt=Et.extend({initialize:function(A){this._marker=A},addHooks:function(){var A=this._marker._icon;this._draggable||(this._draggable=new Xt(A,A,!0)),this._draggable.on({dragstart:this._onDragStart,predrag:this._onPreDrag,drag:this._onDrag,dragend:this._onDragEnd},this).enable(),iA(A,"leaflet-marker-draggable")},removeHooks:function(){this._draggable.off({dragstart:this._onDragStart,predrag:this._onPreDrag,drag:this._onDrag,dragend:this._onDragEnd},this).disable(),this._marker._icon&&bA(this._marker._icon,"leaflet-marker-draggable")},moved:function(){return this._draggable&&this._draggable._moved},_adjustPan:function(A){var t=this._marker,r=t._map,s=this._marker.options.autoPanSpeed,c=this._marker.options.autoPanPadding,h=se(t._icon),p=r.getPixelBounds(),U=r.getPixelOrigin(),y=nt(p.min._subtract(U).add(c),p.max._subtract(U).subtract(c));if(!y.contains(h)){var H=eA((Math.max(y.max.x,h.x)-y.max.x)/(p.max.x-y.max.x)-(Math.min(y.min.x,h.x)-y.min.x)/(p.min.x-y.min.x),(Math.max(y.max.y,h.y)-y.max.y)/(p.max.y-y.max.y)-(Math.min(y.min.y,h.y)-y.min.y)/(p.min.y-y.min.y)).multiplyBy(s);r.panBy(H,{animate:!1}),this._draggable._newPos._add(H),this._draggable._startPos._add(H),OA(t._icon,this._draggable._newPos),this._onDrag(A),this._panRequest=et(this._adjustPan.bind(this,A))}},_onDragStart:function(){this._oldLatLng=this._marker.getLatLng(),this._marker.closePopup&&this._marker.closePopup(),this._marker.fire("movestart").fire("dragstart")},_onPreDrag:function(A){this._marker.options.autoPan&&(it(this._panRequest),this._panRequest=et(this._adjustPan.bind(this,A)))},_onDrag:function(A){var t=this._marker,r=t._shadow,s=se(t._icon),c=t._map.layerPointToLatLng(s);r&&OA(r,s),t._latlng=c,A.latlng=c,A.oldLatLng=this._oldLatLng,t.fire("move",A).fire("drag",A)},_onDragEnd:function(A){it(this._panRequest),delete this._oldLatLng,this._marker.fire("moveend").fire("dragend",A)}}),Yt=JA.extend({options:{icon:new he,interactive:!0,keyboard:!0,title:"",alt:"Marker",zIndexOffset:0,opacity:1,riseOnHover:!1,riseOffset:250,pane:"markerPane",shadowPane:"shadowPane",bubblingMouseEvents:!1,autoPanOnFocus:!0,draggable:!1,autoPan:!1,autoPanPadding:[50,50],autoPanSpeed:10},initialize:function(A,t){X(this,t),this._latlng=dA(A)},onAdd:function(A){this._zoomAnimated=this._zoomAnimated&&A.options.markerZoomAnimation,this._zoomAnimated&&A.on("zoomanim",this._animateZoom,this),this._initIcon(),this.update()},onRemove:function(A){this.dragging&&this.dragging.enabled()&&(this.options.draggable=!0,this.dragging.removeHooks()),delete this.dragging,this._zoomAnimated&&A.off("zoomanim",this._animateZoom,this),this._removeIcon(),this._removeShadow()},getEvents:function(){return{zoom:this.update,viewreset:this.update}},getLatLng:function(){return this._latlng},setLatLng:function(A){var t=this._latlng;return this._latlng=dA(A),this.update(),this.fire("move",{oldLatLng:t,latlng:this._latlng})},setZIndexOffset:function(A){return this.options.zIndexOffset=A,this.update()},getIcon:function(){return this.options.icon},setIcon:function(A){return this.options.icon=A,this._map&&(this._initIcon(),this.update()),this._popup&&this.bindPopup(this._popup,this._popup.options),this},getElement:function(){return this._icon},update:function(){if(this._icon&&this._map){var A=this._map.latLngToLayerPoint(this._latlng).round();this._setPos(A)}return this},_initIcon:function(){var A=this.options,t="leaflet-zoom-"+(this._zoomAnimated?"animated":"hide"),r=A.icon.createIcon(this._icon),s=!1;r!==this._icon&&(this._icon&&this._removeIcon(),s=!0,A.title&&(r.title=A.title),r.tagName==="IMG"&&(r.alt=A.alt||"")),iA(r,t),A.keyboard&&(r.tabIndex="0",r.setAttribute("role","button")),this._icon=r,A.riseOnHover&&this.on({mouseover:this._bringToFront,mouseout:this._resetZIndex}),this.options.autoPanOnFocus&&oA(r,"focus",this._panOnFocus,this);var c=A.icon.createShadow(this._shadow),h=!1;c!==this._shadow&&(this._removeShadow(),h=!0),c&&(iA(c,t),c.alt=""),this._shadow=c,A.opacity<1&&this._updateOpacity(),s&&this.getPane().appendChild(this._icon),this._initInteraction(),c&&h&&this.getPane(A.shadowPane).appendChild(this._shadow)},_removeIcon:function(){this.options.riseOnHover&&this.off({mouseover:this._bringToFront,mouseout:this._resetZIndex}),this.options.autoPanOnFocus&&_A(this._icon,"focus",this._panOnFocus,this),xA(this._icon),this.removeInteractiveTarget(this._icon),this._icon=null},_removeShadow:function(){this._shadow&&xA(this._shadow),this._shadow=null},_setPos:function(A){this._icon&&OA(this._icon,A),this._shadow&&OA(this._shadow,A),this._zIndex=A.y+this.options.zIndexOffset,this._resetZIndex()},_updateZIndex:function(A){this._icon&&(this._icon.style.zIndex=this._zIndex+A)},_animateZoom:function(A){var t=this._map._latLngToNewLayerPoint(this._latlng,A.zoom,A.center).round();this._setPos(t)},_initInteraction:function(){if(this.options.interactive&&(iA(this._icon,"leaflet-interactive"),this.addInteractiveTarget(this._icon),kt)){var A=this.options.draggable;this.dragging&&(A=this.dragging.enabled(),this.dragging.disable()),this.dragging=new kt(this),A&&this.dragging.enable()}},setOpacity:function(A){return this.options.opacity=A,this._map&&this._updateOpacity(),this},_updateOpacity:function(){var A=this.options.opacity;this._icon&&ut(this._icon,A),this._shadow&&ut(this._shadow,A)},_bringToFront:function(){this._updateZIndex(this.options.riseOffset)},_resetZIndex:function(){this._updateZIndex(0)},_panOnFocus:function(){var A=this._map;if(A){var t=this.options.icon.options,r=t.iconSize?eA(t.iconSize):eA(0,0),s=t.iconAnchor?eA(t.iconAnchor):eA(0,0);A.panInside(this._latlng,{paddingTopLeft:s,paddingBottomRight:r.subtract(s)})}},_getPopupAnchor:function(){return this.options.icon.options.popupAnchor},_getTooltipAnchor:function(){return this.options.icon.options.tooltipAnchor}});function UA(A,t){return new Yt(A,t)}var Nt=JA.extend({options:{stroke:!0,color:"#3388ff",weight:3,opacity:1,lineCap:"round",lineJoin:"round",dashArray:null,dashOffset:null,fill:!1,fillColor:null,fillOpacity:.2,fillRule:"evenodd",interactive:!0,bubblingMouseEvents:!0},beforeAdd:function(A){this._renderer=A.getRenderer(this)},onAdd:function(){this._renderer._initPath(this),this._reset(),this._renderer._addPath(this)},onRemove:function(){this._renderer._removePath(this)},redraw:function(){return this._map&&this._renderer._updatePath(this),this},setStyle:function(A){return X(this,A),this._renderer&&(this._renderer._updateStyle(this),this.options.stroke&&A&&Object.prototype.hasOwnProperty.call(A,"weight")&&this._updateBounds()),this},bringToFront:function(){return this._renderer&&this._renderer._bringToFront(this),this},bringToBack:function(){return this._renderer&&this._renderer._bringToBack(this),this},getElement:function(){return this._path},_reset:function(){this._project(),this._update()},_clickTolerance:function(){return(this.options.stroke?this.options.weight/2:0)+(this._renderer.options.tolerance||0)}}),oi=Nt.extend({options:{fill:!0,radius:10},initialize:function(A,t){X(this,t),this._latlng=dA(A),this._radius=this.options.radius},setLatLng:function(A){var t=this._latlng;return this._latlng=dA(A),this.redraw(),this.fire("move",{oldLatLng:t,latlng:this._latlng})},getLatLng:function(){return this._latlng},setRadius:function(A){return this.options.radius=this._radius=A,this.redraw()},getRadius:function(){return this._radius},setStyle:function(A){var t=A&&A.radius||this._radius;return Nt.prototype.setStyle.call(this,A),this.setRadius(t),this},_project:function(){this._point=this._map.latLngToLayerPoint(this._latlng),this._updateBounds()},_updateBounds:function(){var A=this._radius,t=this._radiusY||A,r=this._clickTolerance(),s=[A+r,t+r];this._pxBounds=new EA(this._point.subtract(s),this._point.add(s))},_update:function(){this._map&&this._updatePath()},_updatePath:function(){this._renderer._updateCircle(this)},_empty:function(){return this._radius&&!this._renderer._bounds.intersects(this._pxBounds)},_containsPoint:function(A){return A.distanceTo(this._point)<=this._radius+this._clickTolerance()}});function Ir(A,t){return new oi(A,t)}var Vi=oi.extend({initialize:function(A,t,r){if(typeof t=="number"&&(t=I({},r,{radius:t})),X(this,t),this._latlng=dA(A),isNaN(this.options.radius))throw new Error("Circle radius cannot be NaN");this._mRadius=this.options.radius},setRadius:function(A){return this._mRadius=A,this.redraw()},getRadius:function(){return this._mRadius},getBounds:function(){var A=[this._radius,this._radiusY||this._radius];return new rt(this._map.layerPointToLatLng(this._point.subtract(A)),this._map.layerPointToLatLng(this._point.add(A)))},setStyle:Nt.prototype.setStyle,_project:function(){var A=this._latlng.lng,t=this._latlng.lat,r=this._map,s=r.options.crs;if(s.distance===Gt.distance){var c=Math.PI/180,h=this._mRadius/Gt.R/c,p=r.project([t+h,A]),U=r.project([t-h,A]),y=p.add(U).divideBy(2),H=r.unproject(y).lat,P=Math.acos((Math.cos(h*c)-Math.sin(t*c)*Math.sin(H*c))/(Math.cos(t*c)*Math.cos(H*c)))/c;(isNaN(P)||P===0)&&(P=h/Math.cos(Math.PI/180*t)),this._point=y.subtract(r.getPixelOrigin()),this._radius=isNaN(P)?0:y.x-r.project([H,A-P]).x,this._radiusY=y.y-p.y}else{var q=s.unproject(s.project(this._latlng).subtract([this._mRadius,0]));this._point=r.latLngToLayerPoint(this._latlng),this._radius=this._point.x-r.latLngToLayerPoint(q).x}this._updateBounds()}});function Zi(A,t,r){return new Vi(A,t,r)}var It=Nt.extend({options:{smoothFactor:1,noClip:!1},initialize:function(A,t){X(this,t),this._setLatLngs(A)},getLatLngs:function(){return this._latlngs},setLatLngs:function(A){return this._setLatLngs(A),this.redraw()},isEmpty:function(){return!this._latlngs.length},closestLayerPoint:function(A){for(var t=1/0,r=null,s=ri,c,h,p=0,U=this._parts.length;p<U;p++)for(var y=this._parts[p],H=1,P=y.length;H<P;H++){c=y[H-1],h=y[H];var q=s(A,c,h,!0);q<t&&(t=q,r=s(A,c,h))}return r&&(r.distance=Math.sqrt(t)),r},getCenter:function(){if(!this._map)throw new Error("Must add layer to map before using getCenter()");return Vn(this._defaultShape(),this._map.options.crs)},getBounds:function(){return this._bounds},addLatLng:function(A,t){return t=t||this._defaultShape(),A=dA(A),t.push(A),this._bounds.extend(A),this.redraw()},_setLatLngs:function(A){this._bounds=new rt,this._latlngs=this._convertLatLngs(A)},_defaultShape:function(){return ft(this._latlngs)?this._latlngs:this._latlngs[0]},_convertLatLngs:function(A){for(var t=[],r=ft(A),s=0,c=A.length;s<c;s++)r?(t[s]=dA(A[s]),this._bounds.extend(t[s])):t[s]=this._convertLatLngs(A[s]);return t},_project:function(){var A=new EA;this._rings=[],this._projectLatlngs(this._latlngs,this._rings,A),this._bounds.isValid()&&A.isValid()&&(this._rawPxBounds=A,this._updateBounds())},_updateBounds:function(){var A=this._clickTolerance(),t=new rA(A,A);this._rawPxBounds&&(this._pxBounds=new EA([this._rawPxBounds.min.subtract(t),this._rawPxBounds.max.add(t)]))},_projectLatlngs:function(A,t,r){var s=A[0]instanceof CA,c=A.length,h,p;if(s){for(p=[],h=0;h<c;h++)p[h]=this._map.latLngToLayerPoint(A[h]),r.extend(p[h]);t.push(p)}else for(h=0;h<c;h++)this._projectLatlngs(A[h],t,r)},_clipPoints:function(){var A=this._renderer._bounds;if(this._parts=[],!(!this._pxBounds||!this._pxBounds.intersects(A))){if(this.options.noClip){this._parts=this._rings;return}var t=this._parts,r,s,c,h,p,U,y;for(r=0,c=0,h=this._rings.length;r<h;r++)for(y=this._rings[r],s=0,p=y.length;s<p-1;s++)U=Fr(y[s],y[s+1],A,s,!0),U&&(t[c]=t[c]||[],t[c].push(U[0]),(U[1]!==y[s+1]||s===p-2)&&(t[c].push(U[1]),c++))}},_simplifyPoints:function(){for(var A=this._parts,t=this.options.smoothFactor,r=0,s=A.length;r<s;r++)A[r]=Qr(A[r],t)},_update:function(){this._map&&(this._clipPoints(),this._simplifyPoints(),this._updatePath())},_updatePath:function(){this._renderer._updatePoly(this)},_containsPoint:function(A,t){var r,s,c,h,p,U,y=this._clickTolerance();if(!this._pxBounds||!this._pxBounds.contains(A))return!1;for(r=0,h=this._parts.length;r<h;r++)for(U=this._parts[r],s=0,p=U.length,c=p-1;s<p;c=s++)if(!(!t&&s===0)&&_r(A,U[c],U[s])<=y)return!0;return!1}});function xr(A,t){return new It(A,t)}It._flat=Ri;var zA=It.extend({options:{fill:!0},isEmpty:function(){return!this._latlngs.length||!this._latlngs[0].length},getCenter:function(){if(!this._map)throw new Error("Must add layer to map before using getCenter()");return vr(this._defaultShape(),this._map.options.crs)},_convertLatLngs:function(A){var t=It.prototype._convertLatLngs.call(this,A),r=t.length;return r>=2&&t[0]instanceof CA&&t[0].equals(t[r-1])&&t.pop(),t},_setLatLngs:function(A){It.prototype._setLatLngs.call(this,A),ft(this._latlngs)&&(this._latlngs=[this._latlngs])},_defaultShape:function(){return ft(this._latlngs[0])?this._latlngs[0]:this._latlngs[0][0]},_clipPoints:function(){var A=this._renderer._bounds,t=this.options.weight,r=new rA(t,t);if(A=new EA(A.min.subtract(r),A.max.add(r)),this._parts=[],!(!this._pxBounds||!this._pxBounds.intersects(A))){if(this.options.noClip){this._parts=this._rings;return}for(var s=0,c=this._rings.length,h;s<c;s++)h=Cr(this._rings[s],A,!0),h.length&&this._parts.push(h)}},_updatePath:function(){this._renderer._updatePoly(this,!0)},_containsPoint:function(A){var t=!1,r,s,c,h,p,U,y,H;if(!this._pxBounds||!this._pxBounds.contains(A))return!1;for(h=0,y=this._parts.length;h<y;h++)for(r=this._parts[h],p=0,H=r.length,U=H-1;p<H;U=p++)s=r[p],c=r[U],s.y>A.y!=c.y>A.y&&A.x<(c.x-s.x)*(A.y-s.y)/(c.y-s.y)+s.x&&(t=!t);return t||It.prototype._containsPoint.call(this,A,!0)}});function qt(A,t){return new zA(A,t)}var qA=YA.extend({initialize:function(A,t){X(this,t),this._layers={},A&&this.addData(A)},addData:function(A){var t=fA(A)?A:A.features,r,s,c;if(t){for(r=0,s=t.length;r<s;r++)c=t[r],(c.geometries||c.geometry||c.features||c.coordinates)&&this.addData(c);return this}var h=this.options;if(h.filter&&!h.filter(A))return this;var p=MA(A,h);return p?(p.feature=$t(A),p.defaultOptions=p.options,this.resetStyle(p),h.onEachFeature&&h.onEachFeature(A,p),this.addLayer(p)):this},resetStyle:function(A){return A===void 0?this.eachLayer(this.resetStyle,this):(A.options=I({},A.defaultOptions),this._setLayerStyle(A,this.options.style),this)},setStyle:function(A){return this.eachLayer(function(t){this._setLayerStyle(t,A)},this)},_setLayerStyle:function(A,t){A.setStyle&&(typeof t=="function"&&(t=t(A.feature)),A.setStyle(t))}});function MA(A,t){var r=A.type==="Feature"?A.geometry:A,s=r?r.coordinates:null,c=[],h=t&&t.pointToLayer,p=t&&t.coordsToLatLng||zi,U,y,H,P;if(!s&&!r)return null;switch(r.type){case"Point":return U=p(s),Rt(h,A,U,t);case"MultiPoint":for(H=0,P=s.length;H<P;H++)U=p(s[H]),c.push(Rt(h,A,U,t));return new YA(c);case"LineString":case"MultiLineString":return y=si(s,r.type==="LineString"?0:1,p),new It(y,t);case"Polygon":case"MultiPolygon":return y=si(s,r.type==="Polygon"?1:2,p),new zA(y,t);case"GeometryCollection":for(H=0,P=r.geometries.length;H<P;H++){var q=MA({geometry:r.geometries[H],type:"Feature",properties:A.properties},t);q&&c.push(q)}return new YA(c);case"FeatureCollection":for(H=0,P=r.features.length;H<P;H++){var aA=MA(r.features[H],t);aA&&c.push(aA)}return new YA(c);default:throw new Error("Invalid GeoJSON object.")}}function Rt(A,t,r,s){return A?A(t,r):new Yt(r,s&&s.markersInheritOptions&&s)}function zi(A){return new CA(A[1],A[0],A[2])}function si(A,t,r){for(var s=[],c=0,h=A.length,p;c<h;c++)p=t?si(A[c],t-1,r):(r||zi)(A[c]),s.push(p);return s}function ai(A,t){return A=dA(A),A.alt!==void 0?[F(A.lng,t),F(A.lat,t),F(A.alt,t)]:[F(A.lng,t),F(A.lat,t)]}function ci(A,t,r,s){for(var c=[],h=0,p=A.length;h<p;h++)c.push(t?ci(A[h],ft(A[h])?0:t-1,r,s):ai(A[h],s));return!t&&r&&c.length>0&&c.push(c[0].slice()),c}function Se(A,t){return A.feature?I({},A.feature,{geometry:t}):$t(t)}function $t(A){return A.type==="Feature"||A.type==="FeatureCollection"?A:{type:"Feature",properties:{},geometry:A}}var _t={toGeoJSON:function(A){return Se(this,{type:"Point",coordinates:ai(this.getLatLng(),A)})}};Yt.include(_t),Vi.include(_t),oi.include(_t),It.include({toGeoJSON:function(A){var t=!ft(this._latlngs),r=ci(this._latlngs,t?1:0,!1,A);return Se(this,{type:(t?"Multi":"")+"LineString",coordinates:r})}}),zA.include({toGeoJSON:function(A){var t=!ft(this._latlngs),r=t&&!ft(this._latlngs[0]),s=ci(this._latlngs,r?2:t?1:0,!0,A);return t||(s=[s]),Se(this,{type:(r?"Multi":"")+"Polygon",coordinates:s})}}),$A.include({toMultiPoint:function(A){var t=[];return this.eachLayer(function(r){t.push(r.toGeoJSON(A).geometry.coordinates)}),Se(this,{type:"MultiPoint",coordinates:t})},toGeoJSON:function(A){var t=this.feature&&this.feature.geometry&&this.feature.geometry.type;if(t==="MultiPoint")return this.toMultiPoint(A);var r=t==="GeometryCollection",s=[];return this.eachLayer(function(c){if(c.toGeoJSON){var h=c.toGeoJSON(A);if(r)s.push(h.geometry);else{var p=$t(h);p.type==="FeatureCollection"?s.push.apply(s,p.features):s.push(p)}}}),r?Se(this,{geometries:s,type:"GeometryCollection"}):{type:"FeatureCollection",features:s}}});function Hr(A,t){return new qA(A,t)}var Zo=Hr,fe=JA.extend({options:{opacity:1,alt:"",interactive:!1,crossOrigin:!1,errorOverlayUrl:"",zIndex:1,className:""},initialize:function(A,t,r){this._url=A,this._bounds=SA(t),X(this,r)},onAdd:function(){this._image||(this._initImage(),this.options.opacity<1&&this._updateOpacity()),this.options.interactive&&(iA(this._image,"leaflet-interactive"),this.addInteractiveTarget(this._image)),this.getPane().appendChild(this._image),this._reset()},onRemove:function(){xA(this._image),this.options.interactive&&this.removeInteractiveTarget(this._image)},setOpacity:function(A){return this.options.opacity=A,this._image&&this._updateOpacity(),this},setStyle:function(A){return A.opacity&&this.setOpacity(A.opacity),this},bringToFront:function(){return this._map&&Ie(this._image),this},bringToBack:function(){return this._map&&Dt(this._image),this},setUrl:function(A){return this._url=A,this._image&&(this._image.src=A),this},setBounds:function(A){return this._bounds=SA(A),this._map&&this._reset(),this},getEvents:function(){var A={zoom:this._reset,viewreset:this._reset};return this._zoomAnimated&&(A.zoomanim=this._animateZoom),A},setZIndex:function(A){return this.options.zIndex=A,this._updateZIndex(),this},getBounds:function(){return this._bounds},getElement:function(){return this._image},_initImage:function(){var A=this._url.tagName==="IMG",t=this._image=A?this._url:lA("img");if(iA(t,"leaflet-image-layer"),this._zoomAnimated&&iA(t,"leaflet-zoom-animated"),this.options.className&&iA(t,this.options.className),t.onselectstart=b,t.onmousemove=b,t.onload=g(this.fire,this,"load"),t.onerror=g(this._overlayOnError,this,"error"),(this.options.crossOrigin||this.options.crossOrigin==="")&&(t.crossOrigin=this.options.crossOrigin===!0?"":this.options.crossOrigin),this.options.zIndex&&this._updateZIndex(),A){this._url=t.src;return}t.src=this._url,t.alt=this.options.alt},_animateZoom:function(A){var t=this._map.getZoomScale(A.zoom),r=this._map._latLngBoundsToNewLayerBounds(this._bounds,A.zoom,A.center).min;oe(this._image,r,t)},_reset:function(){var A=this._image,t=new EA(this._map.latLngToLayerPoint(this._bounds.getNorthWest()),this._map.latLngToLayerPoint(this._bounds.getSouthEast())),r=t.getSize();OA(A,t.min),A.style.width=r.x+"px",A.style.height=r.y+"px"},_updateOpacity:function(){ut(this._image,this.options.opacity)},_updateZIndex:function(){this._image&&this.options.zIndex!==void 0&&this.options.zIndex!==null&&(this._image.style.zIndex=this.options.zIndex)},_overlayOnError:function(){this.fire("error");var A=this.options.errorOverlayUrl;A&&this._url!==A&&(this._url=A,this._image.src=A)},getCenter:function(){return this._bounds.getCenter()}}),Lr=function(A,t,r){return new fe(A,t,r)},br=fe.extend({options:{autoplay:!0,loop:!0,keepAspectRatio:!0,muted:!1,playsInline:!0},_initImage:function(){var A=this._url.tagName==="VIDEO",t=this._image=A?this._url:lA("video");if(iA(t,"leaflet-image-layer"),this._zoomAnimated&&iA(t,"leaflet-zoom-animated"),this.options.className&&iA(t,this.options.className),t.onselectstart=b,t.onmousemove=b,t.onloadeddata=g(this.fire,this,"load"),A){for(var r=t.getElementsByTagName("source"),s=[],c=0;c<r.length;c++)s.push(r[c].src);this._url=r.length>0?s:[t.src];return}fA(this._url)||(this._url=[this._url]),!this.options.keepAspectRatio&&Object.prototype.hasOwnProperty.call(t.style,"objectFit")&&(t.style.objectFit="fill"),t.autoplay=!!this.options.autoplay,t.loop=!!this.options.loop,t.muted=!!this.options.muted,t.playsInline=!!this.options.playsInline;for(var h=0;h<this._url.length;h++){var p=lA("source");p.src=this._url[h],t.appendChild(p)}}});function zo(A,t,r){return new br(A,t,r)}var dt=fe.extend({_initImage:function(){var A=this._image=this._url;iA(A,"leaflet-image-layer"),this._zoomAnimated&&iA(A,"leaflet-zoom-animated"),this.options.className&&iA(A,this.options.className),A.onselectstart=b,A.onmousemove=b}});function Mr(A,t,r){return new dt(A,t,r)}var xt=JA.extend({options:{interactive:!1,offset:[0,0],className:"",pane:void 0,content:""},initialize:function(A,t){A&&(A instanceof CA||fA(A))?(this._latlng=dA(A),X(this,t)):(X(this,A),this._source=t),this.options.content&&(this._content=this.options.content)},openOn:function(A){return A=arguments.length?A:this._source._map,A.hasLayer(this)||A.addLayer(this),this},close:function(){return this._map&&this._map.removeLayer(this),this},toggle:function(A){return this._map?this.close():(arguments.length?this._source=A:A=this._source,this._prepareOpen(),this.openOn(A._map)),this},onAdd:function(A){this._zoomAnimated=A._zoomAnimated,this._container||this._initLayout(),A._fadeAnimated&&ut(this._container,0),clearTimeout(this._removeTimeout),this.getPane().appendChild(this._container),this.update(),A._fadeAnimated&&ut(this._container,1),this.bringToFront(),this.options.interactive&&(iA(this._container,"leaflet-interactive"),this.addInteractiveTarget(this._container))},onRemove:function(A){A._fadeAnimated?(ut(this._container,0),this._removeTimeout=setTimeout(g(xA,void 0,this._container),200)):xA(this._container),this.options.interactive&&(bA(this._container,"leaflet-interactive"),this.removeInteractiveTarget(this._container))},getLatLng:function(){return this._latlng},setLatLng:function(A){return this._latlng=dA(A),this._map&&(this._updatePosition(),this._adjustPan()),this},getContent:function(){return this._content},setContent:function(A){return this._content=A,this.update(),this},getElement:function(){return this._container},update:function(){this._map&&(this._container.style.visibility="hidden",this._updateContent(),this._updateLayout(),this._updatePosition(),this._container.style.visibility="",this._adjustPan())},getEvents:function(){var A={zoom:this._updatePosition,viewreset:this._updatePosition};return this._zoomAnimated&&(A.zoomanim=this._animateZoom),A},isOpen:function(){return!!this._map&&this._map.hasLayer(this)},bringToFront:function(){return this._map&&Ie(this._container),this},bringToBack:function(){return this._map&&Dt(this._container),this},_prepareOpen:function(A){var t=this._source;if(!t._map)return!1;if(t instanceof YA){t=null;var r=this._source._layers;for(var s in r)if(r[s]._map){t=r[s];break}if(!t)return!1;this._source=t}if(!A)if(t.getCenter)A=t.getCenter();else if(t.getLatLng)A=t.getLatLng();else if(t.getBounds)A=t.getBounds().getCenter();else throw new Error("Unable to get source layer LatLng.");return this.setLatLng(A),this._map&&this.update(),!0},_updateContent:function(){if(this._content){var A=this._contentNode,t=typeof this._content=="function"?this._content(this._source||this):this._content;if(typeof t=="string")A.innerHTML=t;else{for(;A.hasChildNodes();)A.removeChild(A.firstChild);A.appendChild(t)}this.fire("contentupdate")}},_updatePosition:function(){if(this._map){var A=this._map.latLngToLayerPoint(this._latlng),t=eA(this.options.offset),r=this._getAnchor();this._zoomAnimated?OA(this._container,A.add(r)):t=t.add(A).add(r);var s=this._containerBottom=-t.y,c=this._containerLeft=-Math.round(this._containerWidth/2)+t.x;this._container.style.bottom=s+"px",this._container.style.left=c+"px"}},_getAnchor:function(){return[0,0]}});BA.include({_initOverlay:function(A,t,r,s){var c=t;return c instanceof A||(c=new A(s).setContent(t)),r&&c.setLatLng(r),c}}),JA.include({_initOverlay:function(A,t,r,s){var c=r;return c instanceof A?(X(c,s),c._source=this):(c=t&&!s?t:new A(s,this),c.setContent(r)),c}});var Wi=xt.extend({options:{pane:"popupPane",offset:[0,7],maxWidth:300,minWidth:50,maxHeight:null,autoPan:!0,autoPanPaddingTopLeft:null,autoPanPaddingBottomRight:null,autoPanPadding:[5,5],keepInView:!1,closeButton:!0,autoClose:!0,closeOnEscapeKey:!0,className:""},openOn:function(A){return A=arguments.length?A:this._source._map,!A.hasLayer(this)&&A._popup&&A._popup.options.autoClose&&A.removeLayer(A._popup),A._popup=this,xt.prototype.openOn.call(this,A)},onAdd:function(A){xt.prototype.onAdd.call(this,A),A.fire("popupopen",{popup:this}),this._source&&(this._source.fire("popupopen",{popup:this},!0),this._source instanceof Nt||this._source.on("preclick",zt))},onRemove:function(A){xt.prototype.onRemove.call(this,A),A.fire("popupclose",{popup:this}),this._source&&(this._source.fire("popupclose",{popup:this},!0),this._source instanceof Nt||this._source.off("preclick",zt))},getEvents:function(){var A=xt.prototype.getEvents.call(this);return(this.options.closeOnClick!==void 0?this.options.closeOnClick:this._map.options.closePopupOnClick)&&(A.preclick=this.close),this.options.keepInView&&(A.moveend=this._adjustPan),A},_initLayout:function(){var A="leaflet-popup",t=this._container=lA("div",A+" "+(this.options.className||"")+" leaflet-zoom-animated"),r=this._wrapper=lA("div",A+"-content-wrapper",t);if(this._contentNode=lA("div",A+"-content",r),Le(t),Ki(this._contentNode),oA(t,"contextmenu",zt),this._tipContainer=lA("div",A+"-tip-container",t),this._tip=lA("div",A+"-tip",this._tipContainer),this.options.closeButton){var s=this._closeButton=lA("a",A+"-close-button",t);s.setAttribute("role","button"),s.setAttribute("aria-label","Close popup"),s.href="#close",s.innerHTML='<span aria-hidden="true">&#215;</span>',oA(s,"click",function(c){GA(c),this.close()},this)}},_updateLayout:function(){var A=this._contentNode,t=A.style;t.width="",t.whiteSpace="nowrap";var r=A.offsetWidth;r=Math.min(r,this.options.maxWidth),r=Math.max(r,this.options.minWidth),t.width=r+1+"px",t.whiteSpace="",t.height="";var s=A.offsetHeight,c=this.options.maxHeight,h="leaflet-popup-scrolled";c&&s>c?(t.height=c+"px",iA(A,h)):bA(A,h),this._containerWidth=this._container.offsetWidth},_animateZoom:function(A){var t=this._map._latLngToNewLayerPoint(this._latlng,A.zoom,A.center),r=this._getAnchor();OA(this._container,t.add(r))},_adjustPan:function(){if(this.options.autoPan){if(this._map._panAnim&&this._map._panAnim.stop(),this._autopanning){this._autopanning=!1;return}var A=this._map,t=parseInt(Vt(this._container,"marginBottom"),10)||0,r=this._container.offsetHeight+t,s=this._containerWidth,c=new rA(this._containerLeft,-r-this._containerBottom);c._add(se(this._container));var h=A.layerPointToContainerPoint(c),p=eA(this.options.autoPanPadding),U=eA(this.options.autoPanPaddingTopLeft||p),y=eA(this.options.autoPanPaddingBottomRight||p),H=A.getSize(),P=0,q=0;h.x+s+y.x>H.x&&(P=h.x+s-H.x+y.x),h.x-P-U.x<0&&(P=h.x-U.x),h.y+r+y.y>H.y&&(q=h.y+r-H.y+y.y),h.y-q-U.y<0&&(q=h.y-U.y),(P||q)&&(this.options.keepInView&&(this._autopanning=!0),A.fire("autopanstart").panBy([P,q]))}},_getAnchor:function(){return eA(this._source&&this._source._getPopupAnchor?this._source._getPopupAnchor():[0,0])}}),Xi=function(A,t){return new Wi(A,t)};BA.mergeOptions({closePopupOnClick:!0}),BA.include({openPopup:function(A,t,r){return this._initOverlay(Wi,A,t,r).openOn(this),this},closePopup:function(A){return A=arguments.length?A:this._popup,A&&A.close(),this}}),JA.include({bindPopup:function(A,t){return this._popup=this._initOverlay(Wi,this._popup,A,t),this._popupHandlersAdded||(this.on({click:this._openPopup,keypress:this._onKeyPress,remove:this.closePopup,move:this._movePopup}),this._popupHandlersAdded=!0),this},unbindPopup:function(){return this._popup&&(this.off({click:this._openPopup,keypress:this._onKeyPress,remove:this.closePopup,move:this._movePopup}),this._popupHandlersAdded=!1,this._popup=null),this},openPopup:function(A){return this._popup&&(this instanceof YA||(this._popup._source=this),this._popup._prepareOpen(A||this._latlng)&&this._popup.openOn(this._map)),this},closePopup:function(){return this._popup&&this._popup.close(),this},togglePopup:function(){return this._popup&&this._popup.toggle(this),this},isPopupOpen:function(){return this._popup?this._popup.isOpen():!1},setPopupContent:function(A){return this._popup&&this._popup.setContent(A),this},getPopup:function(){return this._popup},_openPopup:function(A){if(!(!this._popup||!this._map)){ae(A);var t=A.layer||A.target;if(this._popup._source===t&&!(t instanceof Nt)){this._map.hasLayer(this._popup)?this.closePopup():this.openPopup(A.latlng);return}this._popup._source=t,this.openPopup(A.latlng)}},_movePopup:function(A){this._popup.setLatLng(A.latlng)},_onKeyPress:function(A){A.originalEvent.keyCode===13&&this._openPopup(A)}});var Ji=xt.extend({options:{pane:"tooltipPane",offset:[0,0],direction:"auto",permanent:!1,sticky:!1,opacity:.9},onAdd:function(A){xt.prototype.onAdd.call(this,A),this.setOpacity(this.options.opacity),A.fire("tooltipopen",{tooltip:this}),this._source&&(this.addEventParent(this._source),this._source.fire("tooltipopen",{tooltip:this},!0))},onRemove:function(A){xt.prototype.onRemove.call(this,A),A.fire("tooltipclose",{tooltip:this}),this._source&&(this.removeEventParent(this._source),this._source.fire("tooltipclose",{tooltip:this},!0))},getEvents:function(){var A=xt.prototype.getEvents.call(this);return this.options.permanent||(A.preclick=this.close),A},_initLayout:function(){var A="leaflet-tooltip",t=A+" "+(this.options.className||"")+" leaflet-zoom-"+(this._zoomAnimated?"animated":"hide");this._contentNode=this._container=lA("div",t),this._container.setAttribute("role","tooltip"),this._container.setAttribute("id","leaflet-tooltip-"+C(this))},_updateLayout:function(){},_adjustPan:function(){},_setPosition:function(A){var t,r,s=this._map,c=this._container,h=s.latLngToContainerPoint(s.getCenter()),p=s.layerPointToContainerPoint(A),U=this.options.direction,y=c.offsetWidth,H=c.offsetHeight,P=eA(this.options.offset),q=this._getAnchor();U==="top"?(t=y/2,r=H):U==="bottom"?(t=y/2,r=0):U==="center"?(t=y/2,r=H/2):U==="right"?(t=0,r=H/2):U==="left"?(t=y,r=H/2):p.x<h.x?(U="right",t=0,r=H/2):(U="left",t=y+(P.x+q.x)*2,r=H/2),A=A.subtract(eA(t,r,!0)).add(P).add(q),bA(c,"leaflet-tooltip-right"),bA(c,"leaflet-tooltip-left"),bA(c,"leaflet-tooltip-top"),bA(c,"leaflet-tooltip-bottom"),iA(c,"leaflet-tooltip-"+U),OA(c,A)},_updatePosition:function(){var A=this._map.latLngToLayerPoint(this._latlng);this._setPosition(A)},setOpacity:function(A){this.options.opacity=A,this._container&&ut(this._container,A)},_animateZoom:function(A){var t=this._map._latLngToNewLayerPoint(this._latlng,A.zoom,A.center);this._setPosition(t)},_getAnchor:function(){return eA(this._source&&this._source._getTooltipAnchor&&!this.options.sticky?this._source._getTooltipAnchor():[0,0])}}),Tr=function(A,t){return new Ji(A,t)};BA.include({openTooltip:function(A,t,r){return this._initOverlay(Ji,A,t,r).openOn(this),this},closeTooltip:function(A){return A.close(),this}}),JA.include({bindTooltip:function(A,t){return this._tooltip&&this.isTooltipOpen()&&this.unbindTooltip(),this._tooltip=this._initOverlay(Ji,this._tooltip,A,t),this._initTooltipInteractions(),this._tooltip.options.permanent&&this._map&&this._map.hasLayer(this)&&this.openTooltip(),this},unbindTooltip:function(){return this._tooltip&&(this._initTooltipInteractions(!0),this.closeTooltip(),this._tooltip=null),this},_initTooltipInteractions:function(A){if(!(!A&&this._tooltipHandlersAdded)){var t=A?"off":"on",r={remove:this.closeTooltip,move:this._moveTooltip};this._tooltip.options.permanent?r.add=this._openTooltip:(r.mouseover=this._openTooltip,r.mouseout=this.closeTooltip,r.click=this._openTooltip,this._map?this._addFocusListeners():r.add=this._addFocusListeners),this._tooltip.options.sticky&&(r.mousemove=this._moveTooltip),this[t](r),this._tooltipHandlersAdded=!A}},openTooltip:function(A){return this._tooltip&&(this instanceof YA||(this._tooltip._source=this),this._tooltip._prepareOpen(A)&&(this._tooltip.openOn(this._map),this.getElement?this._setAriaDescribedByOnLayer(this):this.eachLayer&&this.eachLayer(this._setAriaDescribedByOnLayer,this))),this},closeTooltip:function(){if(this._tooltip)return this._tooltip.close()},toggleTooltip:function(){return this._tooltip&&this._tooltip.toggle(this),this},isTooltipOpen:function(){return this._tooltip.isOpen()},setTooltipContent:function(A){return this._tooltip&&this._tooltip.setContent(A),this},getTooltip:function(){return this._tooltip},_addFocusListeners:function(){this.getElement?this._addFocusListenersOnLayer(this):this.eachLayer&&this.eachLayer(this._addFocusListenersOnLayer,this)},_addFocusListenersOnLayer:function(A){var t=typeof A.getElement=="function"&&A.getElement();t&&(oA(t,"focus",function(){this._tooltip._source=A,this.openTooltip()},this),oA(t,"blur",this.closeTooltip,this))},_setAriaDescribedByOnLayer:function(A){var t=typeof A.getElement=="function"&&A.getElement();t&&t.setAttribute("aria-describedby",this._tooltip._container.id)},_openTooltip:function(A){if(!(!this._tooltip||!this._map)){if(this._map.dragging&&this._map.dragging.moving()&&!this._openOnceFlag){this._openOnceFlag=!0;var t=this;this._map.once("moveend",function(){t._openOnceFlag=!1,t._openTooltip(A)});return}this._tooltip._source=A.layer||A.target,this.openTooltip(this._tooltip.options.sticky?A.latlng:void 0)}},_moveTooltip:function(A){var t=A.latlng,r,s;this._tooltip.options.sticky&&A.originalEvent&&(r=this._map.mouseEventToContainerPoint(A.originalEvent),s=this._map.containerPointToLayerPoint(r),t=this._map.layerPointToLatLng(s)),this._tooltip.setLatLng(t)}});var Zn=ue.extend({options:{iconSize:[12,12],html:!1,bgPos:null,className:"leaflet-div-icon"},createIcon:function(A){var t=A&&A.tagName==="DIV"?A:document.createElement("div"),r=this.options;if(r.html instanceof Element?(Pi(t),t.appendChild(r.html)):t.innerHTML=r.html!==!1?r.html:"",r.bgPos){var s=eA(r.bgPos);t.style.backgroundPosition=-s.x+"px "+-s.y+"px"}return this._setIconStyles(t,"icon"),t},createShadow:function(){return null}});function Pr(A){return new Zn(A)}ue.Default=he;var Oe=JA.extend({options:{tileSize:256,opacity:1,updateWhenIdle:z.mobile,updateWhenZooming:!0,updateInterval:200,zIndex:1,bounds:null,minZoom:0,maxZoom:void 0,maxNativeZoom:void 0,minNativeZoom:void 0,noWrap:!1,pane:"tilePane",className:"",keepBuffer:2},initialize:function(A){X(this,A)},onAdd:function(){this._initContainer(),this._levels={},this._tiles={},this._resetView()},beforeAdd:function(A){A._addZoomLimit(this)},onRemove:function(A){this._removeAllTiles(),xA(this._container),A._removeZoomLimit(this),this._container=null,this._tileZoom=void 0},bringToFront:function(){return this._map&&(Ie(this._container),this._setAutoZIndex(Math.max)),this},bringToBack:function(){return this._map&&(Dt(this._container),this._setAutoZIndex(Math.min)),this},getContainer:function(){return this._container},setOpacity:function(A){return this.options.opacity=A,this._updateOpacity(),this},setZIndex:function(A){return this.options.zIndex=A,this._updateZIndex(),this},isLoading:function(){return this._loading},redraw:function(){if(this._map){this._removeAllTiles();var A=this._clampZoom(this._map.getZoom());A!==this._tileZoom&&(this._tileZoom=A,this._updateLevels()),this._update()}return this},getEvents:function(){var A={viewprereset:this._invalidateAll,viewreset:this._resetView,zoom:this._resetView,moveend:this._onMoveEnd};return this.options.updateWhenIdle||(this._onMove||(this._onMove=T(this._onMoveEnd,this.options.updateInterval,this)),A.move=this._onMove),this._zoomAnimated&&(A.zoomanim=this._animateZoom),A},createTile:function(){return document.createElement("div")},getTileSize:function(){var A=this.options.tileSize;return A instanceof rA?A:new rA(A,A)},_updateZIndex:function(){this._container&&this.options.zIndex!==void 0&&this.options.zIndex!==null&&(this._container.style.zIndex=this.options.zIndex)},_setAutoZIndex:function(A){for(var t=this.getPane().children,r=-A(-1/0,1/0),s=0,c=t.length,h;s<c;s++)h=t[s].style.zIndex,t[s]!==this._container&&h&&(r=A(r,+h));isFinite(r)&&(this.options.zIndex=r+A(-1,1),this._updateZIndex())},_updateOpacity:function(){if(this._map&&!z.ielt9){ut(this._container,this.options.opacity);var A=+new Date,t=!1,r=!1;for(var s in this._tiles){var c=this._tiles[s];if(!(!c.current||!c.loaded)){var h=Math.min(1,(A-c.loaded)/200);ut(c.el,h),h<1?t=!0:(c.active?r=!0:this._onOpaqueTile(c),c.active=!0)}}r&&!this._noPrune&&this._pruneTiles(),t&&(it(this._fadeFrame),this._fadeFrame=et(this._updateOpacity,this))}},_onOpaqueTile:b,_initContainer:function(){this._container||(this._container=lA("div","leaflet-layer "+(this.options.className||"")),this._updateZIndex(),this.options.opacity<1&&this._updateOpacity(),this.getPane().appendChild(this._container))},_updateLevels:function(){var A=this._tileZoom,t=this.options.maxZoom;if(A!==void 0){for(var r in this._levels)r=Number(r),this._levels[r].el.children.length||r===A?(this._levels[r].el.style.zIndex=t-Math.abs(A-r),this._onUpdateLevel(r)):(xA(this._levels[r].el),this._removeTilesAtZoom(r),this._onRemoveLevel(r),delete this._levels[r]);var s=this._levels[A],c=this._map;return s||(s=this._levels[A]={},s.el=lA("div","leaflet-tile-container leaflet-zoom-animated",this._container),s.el.style.zIndex=t,s.origin=c.project(c.unproject(c.getPixelOrigin()),A).round(),s.zoom=A,this._setZoomTransform(s,c.getCenter(),c.getZoom()),b(s.el.offsetWidth),this._onCreateLevel(s)),this._level=s,s}},_onUpdateLevel:b,_onRemoveLevel:b,_onCreateLevel:b,_pruneTiles:function(){if(this._map){var A,t,r=this._map.getZoom();if(r>this.options.maxZoom||r<this.options.minZoom){this._removeAllTiles();return}for(A in this._tiles)t=this._tiles[A],t.retain=t.current;for(A in this._tiles)if(t=this._tiles[A],t.current&&!t.active){var s=t.coords;this._retainParent(s.x,s.y,s.z,s.z-5)||this._retainChildren(s.x,s.y,s.z,s.z+2)}for(A in this._tiles)this._tiles[A].retain||this._removeTile(A)}},_removeTilesAtZoom:function(A){for(var t in this._tiles)this._tiles[t].coords.z===A&&this._removeTile(t)},_removeAllTiles:function(){for(var A in this._tiles)this._removeTile(A)},_invalidateAll:function(){for(var A in this._levels)xA(this._levels[A].el),this._onRemoveLevel(Number(A)),delete this._levels[A];this._removeAllTiles(),this._tileZoom=void 0},_retainParent:function(A,t,r,s){var c=Math.floor(A/2),h=Math.floor(t/2),p=r-1,U=new rA(+c,+h);U.z=+p;var y=this._tileCoordsToKey(U),H=this._tiles[y];return H&&H.active?(H.retain=!0,!0):(H&&H.loaded&&(H.retain=!0),p>s?this._retainParent(c,h,p,s):!1)},_retainChildren:function(A,t,r,s){for(var c=2*A;c<2*A+2;c++)for(var h=2*t;h<2*t+2;h++){var p=new rA(c,h);p.z=r+1;var U=this._tileCoordsToKey(p),y=this._tiles[U];if(y&&y.active){y.retain=!0;continue}else y&&y.loaded&&(y.retain=!0);r+1<s&&this._retainChildren(c,h,r+1,s)}},_resetView:function(A){var t=A&&(A.pinch||A.flyTo);this._setView(this._map.getCenter(),this._map.getZoom(),t,t)},_animateZoom:function(A){this._setView(A.center,A.zoom,!0,A.noUpdate)},_clampZoom:function(A){var t=this.options;return t.minNativeZoom!==void 0&&A<t.minNativeZoom?t.minNativeZoom:t.maxNativeZoom!==void 0&&t.maxNativeZoom<A?t.maxNativeZoom:A},_setView:function(A,t,r,s){var c=Math.round(t);this.options.maxZoom!==void 0&&c>this.options.maxZoom||this.options.minZoom!==void 0&&c<this.options.minZoom?c=void 0:c=this._clampZoom(c);var h=this.options.updateWhenZooming&&c!==this._tileZoom;(!s||h)&&(this._tileZoom=c,this._abortLoading&&this._abortLoading(),this._updateLevels(),this._resetGrid(),c!==void 0&&this._update(A),r||this._pruneTiles(),this._noPrune=!!r),this._setZoomTransforms(A,t)},_setZoomTransforms:function(A,t){for(var r in this._levels)this._setZoomTransform(this._levels[r],A,t)},_setZoomTransform:function(A,t,r){var s=this._map.getZoomScale(r,A.zoom),c=A.origin.multiplyBy(s).subtract(this._map._getNewPixelOrigin(t,r)).round();z.any3d?oe(A.el,c,s):OA(A.el,c)},_resetGrid:function(){var A=this._map,t=A.options.crs,r=this._tileSize=this.getTileSize(),s=this._tileZoom,c=this._map.getPixelWorldBounds(this._tileZoom);c&&(this._globalTileRange=this._pxBoundsToTileRange(c)),this._wrapX=t.wrapLng&&!this.options.noWrap&&[Math.floor(A.project([0,t.wrapLng[0]],s).x/r.x),Math.ceil(A.project([0,t.wrapLng[1]],s).x/r.y)],this._wrapY=t.wrapLat&&!this.options.noWrap&&[Math.floor(A.project([t.wrapLat[0],0],s).y/r.x),Math.ceil(A.project([t.wrapLat[1],0],s).y/r.y)]},_onMoveEnd:function(){!this._map||this._map._animatingZoom||this._update()},_getTiledPixelBounds:function(A){var t=this._map,r=t._animatingZoom?Math.max(t._animateToZoom,t.getZoom()):t.getZoom(),s=t.getZoomScale(r,this._tileZoom),c=t.project(A,this._tileZoom).floor(),h=t.getSize().divideBy(s*2);return new EA(c.subtract(h),c.add(h))},_update:function(A){var t=this._map;if(t){var r=this._clampZoom(t.getZoom());if(A===void 0&&(A=t.getCenter()),this._tileZoom!==void 0){var s=this._getTiledPixelBounds(A),c=this._pxBoundsToTileRange(s),h=c.getCenter(),p=[],U=this.options.keepBuffer,y=new EA(c.getBottomLeft().subtract([U,-U]),c.getTopRight().add([U,-U]));if(!(isFinite(c.min.x)&&isFinite(c.min.y)&&isFinite(c.max.x)&&isFinite(c.max.y)))throw new Error("Attempted to load an infinite number of tiles");for(var H in this._tiles){var P=this._tiles[H].coords;(P.z!==this._tileZoom||!y.contains(new rA(P.x,P.y)))&&(this._tiles[H].current=!1)}if(Math.abs(r-this._tileZoom)>1){this._setView(A,r);return}for(var q=c.min.y;q<=c.max.y;q++)for(var aA=c.min.x;aA<=c.max.x;aA++){var jA=new rA(aA,q);if(jA.z=this._tileZoom,!!this._isValidTile(jA)){var NA=this._tiles[this._tileCoordsToKey(jA)];NA?NA.current=!0:p.push(jA)}}if(p.sort(function(st,Ke){return st.distanceTo(h)-Ke.distanceTo(h)}),p.length!==0){this._loading||(this._loading=!0,this.fire("loading"));var gt=document.createDocumentFragment();for(aA=0;aA<p.length;aA++)this._addTile(p[aA],gt);this._level.el.appendChild(gt)}}}},_isValidTile:function(A){var t=this._map.options.crs;if(!t.infinite){var r=this._globalTileRange;if(!t.wrapLng&&(A.x<r.min.x||A.x>r.max.x)||!t.wrapLat&&(A.y<r.min.y||A.y>r.max.y))return!1}if(!this.options.bounds)return!0;var s=this._tileCoordsToBounds(A);return SA(this.options.bounds).overlaps(s)},_keyToBounds:function(A){return this._tileCoordsToBounds(this._keyToTileCoords(A))},_tileCoordsToNwSe:function(A){var t=this._map,r=this.getTileSize(),s=A.scaleBy(r),c=s.add(r),h=t.unproject(s,A.z),p=t.unproject(c,A.z);return[h,p]},_tileCoordsToBounds:function(A){var t=this._tileCoordsToNwSe(A),r=new rt(t[0],t[1]);return this.options.noWrap||(r=this._map.wrapLatLngBounds(r)),r},_tileCoordsToKey:function(A){return A.x+":"+A.y+":"+A.z},_keyToTileCoords:function(A){var t=A.split(":"),r=new rA(+t[0],+t[1]);return r.z=+t[2],r},_removeTile:function(A){var t=this._tiles[A];t&&(xA(t.el),delete this._tiles[A],this.fire("tileunload",{tile:t.el,coords:this._keyToTileCoords(A)}))},_initTile:function(A){iA(A,"leaflet-tile");var t=this.getTileSize();A.style.width=t.x+"px",A.style.height=t.y+"px",A.onselectstart=b,A.onmousemove=b,z.ielt9&&this.options.opacity<1&&ut(A,this.options.opacity)},_addTile:function(A,t){var r=this._getTilePos(A),s=this._tileCoordsToKey(A),c=this.createTile(this._wrapCoords(A),g(this._tileReady,this,A));this._initTile(c),this.createTile.length<2&&et(g(this._tileReady,this,A,null,c)),OA(c,r),this._tiles[s]={el:c,coords:A,current:!0},t.appendChild(c),this.fire("tileloadstart",{tile:c,coords:A})},_tileReady:function(A,t,r){t&&this.fire("tileerror",{error:t,tile:r,coords:A});var s=this._tileCoordsToKey(A);r=this._tiles[s],r&&(r.loaded=+new Date,this._map._fadeAnimated?(ut(r.el,0),it(this._fadeFrame),this._fadeFrame=et(this._updateOpacity,this)):(r.active=!0,this._pruneTiles()),t||(iA(r.el,"leaflet-tile-loaded"),this.fire("tileload",{tile:r.el,coords:A})),this._noTilesToLoad()&&(this._loading=!1,this.fire("load"),z.ielt9||!this._map._fadeAnimated?et(this._pruneTiles,this):setTimeout(g(this._pruneTiles,this),250)))},_getTilePos:function(A){return A.scaleBy(this.getTileSize()).subtract(this._level.origin)},_wrapCoords:function(A){var t=new rA(this._wrapX?x(A.x,this._wrapX):A.x,this._wrapY?x(A.y,this._wrapY):A.y);return t.z=A.z,t},_pxBoundsToTileRange:function(A){var t=this.getTileSize();return new EA(A.min.unscaleBy(t).floor(),A.max.unscaleBy(t).ceil().subtract([1,1]))},_noTilesToLoad:function(){for(var A in this._tiles)if(!this._tiles[A].loaded)return!1;return!0}});function Sr(A){return new Oe(A)}var de=Oe.extend({options:{minZoom:0,maxZoom:18,subdomains:"abc",errorTileUrl:"",zoomOffset:0,tms:!1,zoomReverse:!1,detectRetina:!1,crossOrigin:!1,referrerPolicy:!1},initialize:function(A,t){this._url=A,t=X(this,t),t.detectRetina&&z.retina&&t.maxZoom>0?(t.tileSize=Math.floor(t.tileSize/2),t.zoomReverse?(t.zoomOffset--,t.minZoom=Math.min(t.maxZoom,t.minZoom+1)):(t.zoomOffset++,t.maxZoom=Math.max(t.minZoom,t.maxZoom-1)),t.minZoom=Math.max(0,t.minZoom)):t.zoomReverse?t.minZoom=Math.min(t.maxZoom,t.minZoom):t.maxZoom=Math.max(t.minZoom,t.maxZoom),typeof t.subdomains=="string"&&(t.subdomains=t.subdomains.split("")),this.on("tileunload",this._onTileRemove)},setUrl:function(A,t){return this._url===A&&t===void 0&&(t=!0),this._url=A,t||this.redraw(),this},createTile:function(A,t){var r=document.createElement("img");return oA(r,"load",g(this._tileOnLoad,this,t,r)),oA(r,"error",g(this._tileOnError,this,t,r)),(this.options.crossOrigin||this.options.crossOrigin==="")&&(r.crossOrigin=this.options.crossOrigin===!0?"":this.options.crossOrigin),typeof this.options.referrerPolicy=="string"&&(r.referrerPolicy=this.options.referrerPolicy),r.alt="",r.src=this.getTileUrl(A),r},getTileUrl:function(A){var t={r:z.retina?"@2x":"",s:this._getSubdomain(A),x:A.x,y:A.y,z:this._getZoomForUrl()};if(this._map&&!this._map.options.crs.infinite){var r=this._globalTileRange.max.y-A.y;this.options.tms&&(t.y=r),t["-y"]=r}return PA(this._url,I(t,this.options))},_tileOnLoad:function(A,t){z.ielt9?setTimeout(g(A,this,null,t),0):A(null,t)},_tileOnError:function(A,t,r){var s=this.options.errorTileUrl;s&&t.getAttribute("src")!==s&&(t.src=s),A(r,t)},_onTileRemove:function(A){A.tile.onload=null},_getZoomForUrl:function(){var A=this._tileZoom,t=this.options.maxZoom,r=this.options.zoomReverse,s=this.options.zoomOffset;return r&&(A=t-A),A+s},_getSubdomain:function(A){var t=Math.abs(A.x+A.y)%this.options.subdomains.length;return this.options.subdomains[t]},_abortLoading:function(){var A,t;for(A in this._tiles)if(this._tiles[A].coords.z!==this._tileZoom&&(t=this._tiles[A].el,t.onload=b,t.onerror=b,!t.complete)){t.src=KA;var r=this._tiles[A].coords;xA(t),delete this._tiles[A],this.fire("tileabort",{tile:t,coords:r})}},_removeTile:function(A){var t=this._tiles[A];if(t)return t.el.setAttribute("src",KA),Oe.prototype._removeTile.call(this,A)},_tileReady:function(A,t,r){if(!(!this._map||r&&r.getAttribute("src")===KA))return Oe.prototype._tileReady.call(this,A,t,r)}});function zn(A,t){return new de(A,t)}var Wn=de.extend({defaultWmsParams:{service:"WMS",request:"GetMap",layers:"",styles:"",format:"image/jpeg",transparent:!1,version:"1.1.1"},options:{crs:null,uppercase:!1},initialize:function(A,t){this._url=A;var r=I({},this.defaultWmsParams);for(var s in t)s in this.options||(r[s]=t[s]);t=X(this,t);var c=t.detectRetina&&z.retina?2:1,h=this.getTileSize();r.width=h.x*c,r.height=h.y*c,this.wmsParams=r},onAdd:function(A){this._crs=this.options.crs||A.options.crs,this._wmsVersion=parseFloat(this.wmsParams.version);var t=this._wmsVersion>=1.3?"crs":"srs";this.wmsParams[t]=this._crs.code,de.prototype.onAdd.call(this,A)},getTileUrl:function(A){var t=this._tileCoordsToNwSe(A),r=this._crs,s=nt(r.project(t[0]),r.project(t[1])),c=s.min,h=s.max,p=(this._wmsVersion>=1.3&&this._crs===Gi?[c.y,c.x,h.y,h.x]:[c.x,c.y,h.x,h.y]).join(","),U=de.prototype.getTileUrl.call(this,A);return U+IA(this.wmsParams,U,this.options.uppercase)+(this.options.uppercase?"&BBOX=":"&bbox=")+p},setParams:function(A,t){return I(this.wmsParams,A),t||this.redraw(),this}});function Wo(A,t){return new Wn(A,t)}de.WMS=Wn,zn.wms=Wo;var Bt=JA.extend({options:{padding:.1},initialize:function(A){X(this,A),C(this),this._layers=this._layers||{}},onAdd:function(){this._container||(this._initContainer(),iA(this._container,"leaflet-zoom-animated")),this.getPane().appendChild(this._container),this._update(),this.on("update",this._updatePaths,this)},onRemove:function(){this.off("update",this._updatePaths,this),this._destroyContainer()},getEvents:function(){var A={viewreset:this._reset,zoom:this._onZoom,moveend:this._update,zoomend:this._onZoomEnd};return this._zoomAnimated&&(A.zoomanim=this._onAnimZoom),A},_onAnimZoom:function(A){this._updateTransform(A.center,A.zoom)},_onZoom:function(){this._updateTransform(this._map.getCenter(),this._map.getZoom())},_updateTransform:function(A,t){var r=this._map.getZoomScale(t,this._zoom),s=this._map.getSize().multiplyBy(.5+this.options.padding),c=this._map.project(this._center,t),h=s.multiplyBy(-r).add(c).subtract(this._map._getNewPixelOrigin(A,t));z.any3d?oe(this._container,h,r):OA(this._container,h)},_reset:function(){this._update(),this._updateTransform(this._center,this._zoom);for(var A in this._layers)this._layers[A]._reset()},_onZoomEnd:function(){for(var A in this._layers)this._layers[A]._project()},_updatePaths:function(){for(var A in this._layers)this._layers[A]._update()},_update:function(){var A=this.options.padding,t=this._map.getSize(),r=this._map.containerPointToLayerPoint(t.multiplyBy(-A)).round();this._bounds=new EA(r,r.add(t.multiplyBy(1+A*2)).round()),this._center=this._map.getCenter(),this._zoom=this._map.getZoom()}}),Or=Bt.extend({options:{tolerance:0},getEvents:function(){var A=Bt.prototype.getEvents.call(this);return A.viewprereset=this._onViewPreReset,A},_onViewPreReset:function(){this._postponeUpdatePaths=!0},onAdd:function(){Bt.prototype.onAdd.call(this),this._draw()},_initContainer:function(){var A=this._container=document.createElement("canvas");oA(A,"mousemove",this._onMouseMove,this),oA(A,"click dblclick mousedown mouseup contextmenu",this._onClick,this),oA(A,"mouseout",this._handleMouseOut,this),A._leaflet_disable_events=!0,this._ctx=A.getContext("2d")},_destroyContainer:function(){it(this._redrawRequest),delete this._ctx,xA(this._container),_A(this._container),delete this._container},_updatePaths:function(){if(!this._postponeUpdatePaths){var A;this._redrawBounds=null;for(var t in this._layers)A=this._layers[t],A._update();this._redraw()}},_update:function(){if(!(this._map._animatingZoom&&this._bounds)){Bt.prototype._update.call(this);var A=this._bounds,t=this._container,r=A.getSize(),s=z.retina?2:1;OA(t,A.min),t.width=s*r.x,t.height=s*r.y,t.style.width=r.x+"px",t.style.height=r.y+"px",z.retina&&this._ctx.scale(2,2),this._ctx.translate(-A.min.x,-A.min.y),this.fire("update")}},_reset:function(){Bt.prototype._reset.call(this),this._postponeUpdatePaths&&(this._postponeUpdatePaths=!1,this._updatePaths())},_initPath:function(A){this._updateDashArray(A),this._layers[C(A)]=A;var t=A._order={layer:A,prev:this._drawLast,next:null};this._drawLast&&(this._drawLast.next=t),this._drawLast=t,this._drawFirst=this._drawFirst||this._drawLast},_addPath:function(A){this._requestRedraw(A)},_removePath:function(A){var t=A._order,r=t.next,s=t.prev;r?r.prev=s:this._drawLast=s,s?s.next=r:this._drawFirst=r,delete A._order,delete this._layers[C(A)],this._requestRedraw(A)},_updatePath:function(A){this._extendRedrawBounds(A),A._project(),A._update(),this._requestRedraw(A)},_updateStyle:function(A){this._updateDashArray(A),this._requestRedraw(A)},_updateDashArray:function(A){if(typeof A.options.dashArray=="string"){var t=A.options.dashArray.split(/[, ]+/),r=[],s,c;for(c=0;c<t.length;c++){if(s=Number(t[c]),isNaN(s))return;r.push(s)}A.options._dashArray=r}else A.options._dashArray=A.options.dashArray},_requestRedraw:function(A){this._map&&(this._extendRedrawBounds(A),this._redrawRequest=this._redrawRequest||et(this._redraw,this))},_extendRedrawBounds:function(A){if(A._pxBounds){var t=(A.options.weight||0)+1;this._redrawBounds=this._redrawBounds||new EA,this._redrawBounds.extend(A._pxBounds.min.subtract([t,t])),this._redrawBounds.extend(A._pxBounds.max.add([t,t]))}},_redraw:function(){this._redrawRequest=null,this._redrawBounds&&(this._redrawBounds.min._floor(),this._redrawBounds.max._ceil()),this._clear(),this._draw(),this._redrawBounds=null},_clear:function(){var A=this._redrawBounds;if(A){var t=A.getSize();this._ctx.clearRect(A.min.x,A.min.y,t.x,t.y)}else this._ctx.save(),this._ctx.setTransform(1,0,0,1,0,0),this._ctx.clearRect(0,0,this._container.width,this._container.height),this._ctx.restore()},_draw:function(){var A,t=this._redrawBounds;if(this._ctx.save(),t){var r=t.getSize();this._ctx.beginPath(),this._ctx.rect(t.min.x,t.min.y,r.x,r.y),this._ctx.clip()}this._drawing=!0;for(var s=this._drawFirst;s;s=s.next)A=s.layer,(!t||A._pxBounds&&A._pxBounds.intersects(t))&&A._updatePath();this._drawing=!1,this._ctx.restore()},_updatePoly:function(A,t){if(this._drawing){var r,s,c,h,p=A._parts,U=p.length,y=this._ctx;if(U){for(y.beginPath(),r=0;r<U;r++){for(s=0,c=p[r].length;s<c;s++)h=p[r][s],y[s?"lineTo":"moveTo"](h.x,h.y);t&&y.closePath()}this._fillStroke(y,A)}}},_updateCircle:function(A){if(!(!this._drawing||A._empty())){var t=A._point,r=this._ctx,s=Math.max(Math.round(A._radius),1),c=(Math.max(Math.round(A._radiusY),1)||s)/s;c!==1&&(r.save(),r.scale(1,c)),r.beginPath(),r.arc(t.x,t.y/c,s,0,Math.PI*2,!1),c!==1&&r.restore(),this._fillStroke(r,A)}},_fillStroke:function(A,t){var r=t.options;r.fill&&(A.globalAlpha=r.fillOpacity,A.fillStyle=r.fillColor||r.color,A.fill(r.fillRule||"evenodd")),r.stroke&&r.weight!==0&&(A.setLineDash&&A.setLineDash(t.options&&t.options._dashArray||[]),A.globalAlpha=r.opacity,A.lineWidth=r.weight,A.strokeStyle=r.color,A.lineCap=r.lineCap,A.lineJoin=r.lineJoin,A.stroke())},_onClick:function(A){for(var t=this._map.mouseEventToLayerPoint(A),r,s,c=this._drawFirst;c;c=c.next)r=c.layer,r.options.interactive&&r._containsPoint(t)&&(!(A.type==="click"||A.type==="preclick")||!this._map._draggableMoved(r))&&(s=r);this._fireEvent(s?[s]:!1,A)},_onMouseMove:function(A){if(!(!this._map||this._map.dragging.moving()||this._map._animatingZoom)){var t=this._map.mouseEventToLayerPoint(A);this._handleMouseHover(A,t)}},_handleMouseOut:function(A){var t=this._hoveredLayer;t&&(bA(this._container,"leaflet-interactive"),this._fireEvent([t],A,"mouseout"),this._hoveredLayer=null,this._mouseHoverThrottled=!1)},_handleMouseHover:function(A,t){if(!this._mouseHoverThrottled){for(var r,s,c=this._drawFirst;c;c=c.next)r=c.layer,r.options.interactive&&r._containsPoint(t)&&(s=r);s!==this._hoveredLayer&&(this._handleMouseOut(A),s&&(iA(this._container,"leaflet-interactive"),this._fireEvent([s],A,"mouseover"),this._hoveredLayer=s)),this._fireEvent(this._hoveredLayer?[this._hoveredLayer]:!1,A),this._mouseHoverThrottled=!0,setTimeout(g(function(){this._mouseHoverThrottled=!1},this),32)}},_fireEvent:function(A,t,r){this._map._fireDOMEvent(t,r||t.type,A)},_bringToFront:function(A){var t=A._order;if(t){var r=t.next,s=t.prev;if(r)r.prev=s;else return;s?s.next=r:r&&(this._drawFirst=r),t.prev=this._drawLast,this._drawLast.next=t,t.next=null,this._drawLast=t,this._requestRedraw(A)}},_bringToBack:function(A){var t=A._order;if(t){var r=t.next,s=t.prev;if(s)s.next=r;else return;r?r.prev=s:s&&(this._drawLast=s),t.prev=null,t.next=this._drawFirst,this._drawFirst.prev=t,this._drawFirst=t,this._requestRedraw(A)}}});function Dr(A){return z.canvas?new Or(A):null}var Be=function(){try{return document.namespaces.add("lvml","urn:schemas-microsoft-com:vml"),function(A){return document.createElement("<lvml:"+A+' class="lvml">')}}catch{}return function(A){return document.createElement("<"+A+' xmlns="urn:schemas-microsoft.com:vml" class="lvml">')}}(),Xo={_initContainer:function(){this._container=lA("div","leaflet-vml-container")},_update:function(){this._map._animatingZoom||(Bt.prototype._update.call(this),this.fire("update"))},_initPath:function(A){var t=A._container=Be("shape");iA(t,"leaflet-vml-shape "+(this.options.className||"")),t.coordsize="1 1",A._path=Be("path"),t.appendChild(A._path),this._updateStyle(A),this._layers[C(A)]=A},_addPath:function(A){var t=A._container;this._container.appendChild(t),A.options.interactive&&A.addInteractiveTarget(t)},_removePath:function(A){var t=A._container;xA(t),A.removeInteractiveTarget(t),delete this._layers[C(A)]},_updateStyle:function(A){var t=A._stroke,r=A._fill,s=A.options,c=A._container;c.stroked=!!s.stroke,c.filled=!!s.fill,s.stroke?(t||(t=A._stroke=Be("stroke")),c.appendChild(t),t.weight=s.weight+"px",t.color=s.color,t.opacity=s.opacity,s.dashArray?t.dashStyle=fA(s.dashArray)?s.dashArray.join(" "):s.dashArray.replace(/( *, *)/g," "):t.dashStyle="",t.endcap=s.lineCap.replace("butt","flat"),t.joinstyle=s.lineJoin):t&&(c.removeChild(t),A._stroke=null),s.fill?(r||(r=A._fill=Be("fill")),c.appendChild(r),r.color=s.fillColor||s.color,r.opacity=s.fillOpacity):r&&(c.removeChild(r),A._fill=null)},_updateCircle:function(A){var t=A._point.round(),r=Math.round(A._radius),s=Math.round(A._radiusY||r);this._setPath(A,A._empty()?"M0 0":"AL "+t.x+","+t.y+" "+r+","+s+" 0,"+65535*360)},_setPath:function(A,t){A._path.v=t},_bringToFront:function(A){Ie(A._container)},_bringToBack:function(A){Dt(A._container)}},li=z.vml?Be:Cn,ui=Bt.extend({_initContainer:function(){this._container=li("svg"),this._container.setAttribute("pointer-events","none"),this._rootGroup=li("g"),this._container.appendChild(this._rootGroup)},_destroyContainer:function(){xA(this._container),_A(this._container),delete this._container,delete this._rootGroup,delete this._svgSize},_update:function(){if(!(this._map._animatingZoom&&this._bounds)){Bt.prototype._update.call(this);var A=this._bounds,t=A.getSize(),r=this._container;(!this._svgSize||!this._svgSize.equals(t))&&(this._svgSize=t,r.setAttribute("width",t.x),r.setAttribute("height",t.y)),OA(r,A.min),r.setAttribute("viewBox",[A.min.x,A.min.y,t.x,t.y].join(" ")),this.fire("update")}},_initPath:function(A){var t=A._path=li("path");A.options.className&&iA(t,A.options.className),A.options.interactive&&iA(t,"leaflet-interactive"),this._updateStyle(A),this._layers[C(A)]=A},_addPath:function(A){this._rootGroup||this._initContainer(),this._rootGroup.appendChild(A._path),A.addInteractiveTarget(A._path)},_removePath:function(A){xA(A._path),A.removeInteractiveTarget(A._path),delete this._layers[C(A)]},_updatePath:function(A){A._project(),A._update()},_updateStyle:function(A){var t=A._path,r=A.options;t&&(r.stroke?(t.setAttribute("stroke",r.color),t.setAttribute("stroke-opacity",r.opacity),t.setAttribute("stroke-width",r.weight),t.setAttribute("stroke-linecap",r.lineCap),t.setAttribute("stroke-linejoin",r.lineJoin),r.dashArray?t.setAttribute("stroke-dasharray",r.dashArray):t.removeAttribute("stroke-dasharray"),r.dashOffset?t.setAttribute("stroke-dashoffset",r.dashOffset):t.removeAttribute("stroke-dashoffset")):t.setAttribute("stroke","none"),r.fill?(t.setAttribute("fill",r.fillColor||r.color),t.setAttribute("fill-opacity",r.fillOpacity),t.setAttribute("fill-rule",r.fillRule||"evenodd")):t.setAttribute("fill","none"))},_updatePoly:function(A,t){this._setPath(A,vn(A._parts,t))},_updateCircle:function(A){var t=A._point,r=Math.max(Math.round(A._radius),1),s=Math.max(Math.round(A._radiusY),1)||r,c="a"+r+","+s+" 0 1,0 ",h=A._empty()?"M0 0":"M"+(t.x-r)+","+t.y+c+r*2+",0 "+c+-r*2+",0 ";this._setPath(A,h)},_setPath:function(A,t){A._path.setAttribute("d",t)},_bringToFront:function(A){Ie(A._path)},_bringToBack:function(A){Dt(A._path)}});z.vml&&ui.include(Xo);function Kr(A){return z.svg||z.vml?new ui(A):null}BA.include({getRenderer:function(A){var t=A.options.renderer||this._getPaneRenderer(A.options.pane)||this.options.renderer||this._renderer;return t||(t=this._renderer=this._createRenderer()),this.hasLayer(t)||this.addLayer(t),t},_getPaneRenderer:function(A){if(A==="overlayPane"||A===void 0)return!1;var t=this._paneRenderers[A];return t===void 0&&(t=this._createRenderer({pane:A}),this._paneRenderers[A]=t),t},_createRenderer:function(A){return this.options.preferCanvas&&Dr(A)||Kr(A)}});var kr=zA.extend({initialize:function(A,t){zA.prototype.initialize.call(this,this._boundsToLatLngs(A),t)},setBounds:function(A){return this.setLatLngs(this._boundsToLatLngs(A))},_boundsToLatLngs:function(A){return A=SA(A),[A.getSouthWest(),A.getNorthWest(),A.getNorthEast(),A.getSouthEast()]}});function Jo(A,t){return new kr(A,t)}ui.create=li,ui.pointsToPath=vn,qA.geometryToLayer=MA,qA.coordsToLatLng=zi,qA.coordsToLatLngs=si,qA.latLngToCoords=ai,qA.latLngsToCoords=ci,qA.getFeature=Se,qA.asFeature=$t,BA.mergeOptions({boxZoom:!0});var Nr=Et.extend({initialize:function(A){this._map=A,this._container=A._container,this._pane=A._panes.overlayPane,this._resetStateTimeout=0,A.on("unload",this._destroy,this)},addHooks:function(){oA(this._container,"mousedown",this._onMouseDown,this)},removeHooks:function(){_A(this._container,"mousedown",this._onMouseDown,this)},moved:function(){return this._moved},_destroy:function(){xA(this._pane),delete this._pane},_resetState:function(){this._resetStateTimeout=0,this._moved=!1},_clearDeferredResetState:function(){this._resetStateTimeout!==0&&(clearTimeout(this._resetStateTimeout),this._resetStateTimeout=0)},_onMouseDown:function(A){if(!A.shiftKey||A.which!==1&&A.button!==1)return!1;this._clearDeferredResetState(),this._resetState(),Ai(),Mn(),this._startPoint=this._map.mouseEventToContainerPoint(A),oA(document,{contextmenu:ae,mousemove:this._onMouseMove,mouseup:this._onMouseUp,keydown:this._onKeyDown},this)},_onMouseMove:function(A){this._moved||(this._moved=!0,this._box=lA("div","leaflet-zoom-box",this._container),iA(this._container,"leaflet-crosshair"),this._map.fire("boxzoomstart")),this._point=this._map.mouseEventToContainerPoint(A);var t=new EA(this._point,this._startPoint),r=t.getSize();OA(this._box,t.min),this._box.style.width=r.x+"px",this._box.style.height=r.y+"px"},_finish:function(){this._moved&&(xA(this._box),bA(this._container,"leaflet-crosshair")),Zt(),Tn(),_A(document,{contextmenu:ae,mousemove:this._onMouseMove,mouseup:this._onMouseUp,keydown:this._onKeyDown},this)},_onMouseUp:function(A){if(!(A.which!==1&&A.button!==1)&&(this._finish(),!!this._moved)){this._clearDeferredResetState(),this._resetStateTimeout=setTimeout(g(this._resetState,this),0);var t=new rt(this._map.containerPointToLatLng(this._startPoint),this._map.containerPointToLatLng(this._point));this._map.fitBounds(t).fire("boxzoomend",{boxZoomBounds:t})}},_onKeyDown:function(A){A.keyCode===27&&(this._finish(),this._clearDeferredResetState(),this._resetState())}});BA.addInitHook("addHandler","boxZoom",Nr),BA.mergeOptions({doubleClickZoom:!0});var De=Et.extend({addHooks:function(){this._map.on("dblclick",this._onDoubleClick,this)},removeHooks:function(){this._map.off("dblclick",this._onDoubleClick,this)},_onDoubleClick:function(A){var t=this._map,r=t.getZoom(),s=t.options.zoomDelta,c=A.originalEvent.shiftKey?r-s:r+s;t.options.doubleClickZoom==="center"?t.setZoom(c):t.setZoomAround(A.containerPoint,c)}});BA.addInitHook("addHandler","doubleClickZoom",De),BA.mergeOptions({dragging:!0,inertia:!0,inertiaDeceleration:3400,inertiaMaxSpeed:1/0,easeLinearity:.2,worldCopyJump:!1,maxBoundsViscosity:0});var Rr=Et.extend({addHooks:function(){if(!this._draggable){var A=this._map;this._draggable=new Xt(A._mapPane,A._container),this._draggable.on({dragstart:this._onDragStart,drag:this._onDrag,dragend:this._onDragEnd},this),this._draggable.on("predrag",this._onPreDragLimit,this),A.options.worldCopyJump&&(this._draggable.on("predrag",this._onPreDragWrap,this),A.on("zoomend",this._onZoomEnd,this),A.whenReady(this._onZoomEnd,this))}iA(this._map._container,"leaflet-grab leaflet-touch-drag"),this._draggable.enable(),this._positions=[],this._times=[]},removeHooks:function(){bA(this._map._container,"leaflet-grab"),bA(this._map._container,"leaflet-touch-drag"),this._draggable.disable()},moved:function(){return this._draggable&&this._draggable._moved},moving:function(){return this._draggable&&this._draggable._moving},_onDragStart:function(){var A=this._map;if(A._stop(),this._map.options.maxBounds&&this._map.options.maxBoundsViscosity){var t=SA(this._map.options.maxBounds);this._offsetLimit=nt(this._map.latLngToContainerPoint(t.getNorthWest()).multiplyBy(-1),this._map.latLngToContainerPoint(t.getSouthEast()).multiplyBy(-1).add(this._map.getSize())),this._viscosity=Math.min(1,Math.max(0,this._map.options.maxBoundsViscosity))}else this._offsetLimit=null;A.fire("movestart").fire("dragstart"),A.options.inertia&&(this._positions=[],this._times=[])},_onDrag:function(A){if(this._map.options.inertia){var t=this._lastTime=+new Date,r=this._lastPos=this._draggable._absPos||this._draggable._newPos;this._positions.push(r),this._times.push(t),this._prunePositions(t)}this._map.fire("move",A).fire("drag",A)},_prunePositions:function(A){for(;this._positions.length>1&&A-this._times[0]>50;)this._positions.shift(),this._times.shift()},_onZoomEnd:function(){var A=this._map.getSize().divideBy(2),t=this._map.latLngToLayerPoint([0,0]);this._initialWorldOffset=t.subtract(A).x,this._worldWidth=this._map.getPixelWorldBounds().getSize().x},_viscousLimit:function(A,t){return A-(A-t)*this._viscosity},_onPreDragLimit:function(){if(!(!this._viscosity||!this._offsetLimit)){var A=this._draggable._newPos.subtract(this._draggable._startPos),t=this._offsetLimit;A.x<t.min.x&&(A.x=this._viscousLimit(A.x,t.min.x)),A.y<t.min.y&&(A.y=this._viscousLimit(A.y,t.min.y)),A.x>t.max.x&&(A.x=this._viscousLimit(A.x,t.max.x)),A.y>t.max.y&&(A.y=this._viscousLimit(A.y,t.max.y)),this._draggable._newPos=this._draggable._startPos.add(A)}},_onPreDragWrap:function(){var A=this._worldWidth,t=Math.round(A/2),r=this._initialWorldOffset,s=this._draggable._newPos.x,c=(s-t+r)%A+t-r,h=(s+t+r)%A-t-r,p=Math.abs(c+r)<Math.abs(h+r)?c:h;this._draggable._absPos=this._draggable._newPos.clone(),this._draggable._newPos.x=p},_onDragEnd:function(A){var t=this._map,r=t.options,s=!r.inertia||A.noInertia||this._times.length<2;if(t.fire("dragend",A),s)t.fire("moveend");else{this._prunePositions(+new Date);var c=this._lastPos.subtract(this._positions[0]),h=(this._lastTime-this._times[0])/1e3,p=r.easeLinearity,U=c.multiplyBy(p/h),y=U.distanceTo([0,0]),H=Math.min(r.inertiaMaxSpeed,y),P=U.multiplyBy(H/y),q=H/(r.inertiaDeceleration*p),aA=P.multiplyBy(-q/2).round();!aA.x&&!aA.y?t.fire("moveend"):(aA=t._limitOffset(aA,t.options.maxBounds),et(function(){t.panBy(aA,{duration:q,easeLinearity:p,noMoveStart:!0,animate:!0})}))}}});BA.addInitHook("addHandler","dragging",Rr),BA.mergeOptions({keyboard:!0,keyboardPanDelta:80});var Gr=Et.extend({keyCodes:{left:[37],right:[39],down:[40],up:[38],zoomIn:[187,107,61,171],zoomOut:[189,109,54,173]},initialize:function(A){this._map=A,this._setPanDelta(A.options.keyboardPanDelta),this._setZoomDelta(A.options.zoomDelta)},addHooks:function(){var A=this._map._container;A.tabIndex<=0&&(A.tabIndex="0"),oA(A,{focus:this._onFocus,blur:this._onBlur,mousedown:this._onMouseDown},this),this._map.on({focus:this._addHooks,blur:this._removeHooks},this)},removeHooks:function(){this._removeHooks(),_A(this._map._container,{focus:this._onFocus,blur:this._onBlur,mousedown:this._onMouseDown},this),this._map.off({focus:this._addHooks,blur:this._removeHooks},this)},_onMouseDown:function(){if(!this._focused){var A=document.body,t=document.documentElement,r=A.scrollTop||t.scrollTop,s=A.scrollLeft||t.scrollLeft;this._map._container.focus(),window.scrollTo(s,r)}},_onFocus:function(){this._focused=!0,this._map.fire("focus")},_onBlur:function(){this._focused=!1,this._map.fire("blur")},_setPanDelta:function(A){var t=this._panKeys={},r=this.keyCodes,s,c;for(s=0,c=r.left.length;s<c;s++)t[r.left[s]]=[-1*A,0];for(s=0,c=r.right.length;s<c;s++)t[r.right[s]]=[A,0];for(s=0,c=r.down.length;s<c;s++)t[r.down[s]]=[0,A];for(s=0,c=r.up.length;s<c;s++)t[r.up[s]]=[0,-1*A]},_setZoomDelta:function(A){var t=this._zoomKeys={},r=this.keyCodes,s,c;for(s=0,c=r.zoomIn.length;s<c;s++)t[r.zoomIn[s]]=A;for(s=0,c=r.zoomOut.length;s<c;s++)t[r.zoomOut[s]]=-A},_addHooks:function(){oA(document,"keydown",this._onKeyDown,this)},_removeHooks:function(){_A(document,"keydown",this._onKeyDown,this)},_onKeyDown:function(A){if(!(A.altKey||A.ctrlKey||A.metaKey)){var t=A.keyCode,r=this._map,s;if(t in this._panKeys){if(!r._panAnim||!r._panAnim._inProgress)if(s=this._panKeys[t],A.shiftKey&&(s=eA(s).multiplyBy(3)),r.options.maxBounds&&(s=r._limitOffset(eA(s),r.options.maxBounds)),r.options.worldCopyJump){var c=r.wrapLatLng(r.unproject(r.project(r.getCenter()).add(s)));r.panTo(c)}else r.panBy(s)}else if(t in this._zoomKeys)r.setZoom(r.getZoom()+(A.shiftKey?3:1)*this._zoomKeys[t]);else if(t===27&&r._popup&&r._popup.options.closeOnEscapeKey)r.closePopup();else return;ae(A)}}});BA.addInitHook("addHandler","keyboard",Gr),BA.mergeOptions({scrollWheelZoom:!0,wheelDebounceTime:40,wheelPxPerZoomLevel:60});var hi=Et.extend({addHooks:function(){oA(this._map._container,"wheel",this._onWheelScroll,this),this._delta=0},removeHooks:function(){_A(this._map._container,"wheel",this._onWheelScroll,this)},_onWheelScroll:function(A){var t=Kn(A),r=this._map.options.wheelDebounceTime;this._delta+=t,this._lastMousePos=this._map.mouseEventToContainerPoint(A),this._startTime||(this._startTime=+new Date);var s=Math.max(r-(+new Date-this._startTime),0);clearTimeout(this._timer),this._timer=setTimeout(g(this._performZoom,this),s),ae(A)},_performZoom:function(){var A=this._map,t=A.getZoom(),r=this._map.options.zoomSnap||0;A._stop();var s=this._delta/(this._map.options.wheelPxPerZoomLevel*4),c=4*Math.log(2/(1+Math.exp(-Math.abs(s))))/Math.LN2,h=r?Math.ceil(c/r)*r:c,p=A._limitZoom(t+(this._delta>0?h:-h))-t;this._delta=0,this._startTime=null,p&&(A.options.scrollWheelZoom==="center"?A.setZoom(t+p):A.setZoomAround(this._lastMousePos,t+p))}});BA.addInitHook("addHandler","scrollWheelZoom",hi);var Yo=600;BA.mergeOptions({tapHold:z.touchNative&&z.safari&&z.mobile,tapTolerance:15});var Vr=Et.extend({addHooks:function(){oA(this._map._container,"touchstart",this._onDown,this)},removeHooks:function(){_A(this._map._container,"touchstart",this._onDown,this)},_onDown:function(A){if(clearTimeout(this._holdTimeout),A.touches.length===1){var t=A.touches[0];this._startPos=this._newPos=new rA(t.clientX,t.clientY),this._holdTimeout=setTimeout(g(function(){this._cancel(),this._isTapValid()&&(oA(document,"touchend",GA),oA(document,"touchend touchcancel",this._cancelClickPrevent),this._simulateEvent("contextmenu",t))},this),Yo),oA(document,"touchend touchcancel contextmenu",this._cancel,this),oA(document,"touchmove",this._onMove,this)}},_cancelClickPrevent:function A(){_A(document,"touchend",GA),_A(document,"touchend touchcancel",A)},_cancel:function(){clearTimeout(this._holdTimeout),_A(document,"touchend touchcancel contextmenu",this._cancel,this),_A(document,"touchmove",this._onMove,this)},_onMove:function(A){var t=A.touches[0];this._newPos=new rA(t.clientX,t.clientY)},_isTapValid:function(){return this._newPos.distanceTo(this._startPos)<=this._map.options.tapTolerance},_simulateEvent:function(A,t){var r=new MouseEvent(A,{bubbles:!0,cancelable:!0,view:window,screenX:t.screenX,screenY:t.screenY,clientX:t.clientX,clientY:t.clientY});r._simulated=!0,t.target.dispatchEvent(r)}});BA.addInitHook("addHandler","tapHold",Vr),BA.mergeOptions({touchZoom:z.touch,bounceAtZoomLimits:!0});var Zr=Et.extend({addHooks:function(){iA(this._map._container,"leaflet-touch-zoom"),oA(this._map._container,"touchstart",this._onTouchStart,this)},removeHooks:function(){bA(this._map._container,"leaflet-touch-zoom"),_A(this._map._container,"touchstart",this._onTouchStart,this)},_onTouchStart:function(A){var t=this._map;if(!(!A.touches||A.touches.length!==2||t._animatingZoom||this._zooming)){var r=t.mouseEventToContainerPoint(A.touches[0]),s=t.mouseEventToContainerPoint(A.touches[1]);this._centerPoint=t.getSize()._divideBy(2),this._startLatLng=t.containerPointToLatLng(this._centerPoint),t.options.touchZoom!=="center"&&(this._pinchStartLatLng=t.containerPointToLatLng(r.add(s)._divideBy(2))),this._startDist=r.distanceTo(s),this._startZoom=t.getZoom(),this._moved=!1,this._zooming=!0,t._stop(),oA(document,"touchmove",this._onTouchMove,this),oA(document,"touchend touchcancel",this._onTouchEnd,this),GA(A)}},_onTouchMove:function(A){if(!(!A.touches||A.touches.length!==2||!this._zooming)){var t=this._map,r=t.mouseEventToContainerPoint(A.touches[0]),s=t.mouseEventToContainerPoint(A.touches[1]),c=r.distanceTo(s)/this._startDist;if(this._zoom=t.getScaleZoom(c,this._startZoom),!t.options.bounceAtZoomLimits&&(this._zoom<t.getMinZoom()&&c<1||this._zoom>t.getMaxZoom()&&c>1)&&(this._zoom=t._limitZoom(this._zoom)),t.options.touchZoom==="center"){if(this._center=this._startLatLng,c===1)return}else{var h=r._add(s)._divideBy(2)._subtract(this._centerPoint);if(c===1&&h.x===0&&h.y===0)return;this._center=t.unproject(t.project(this._pinchStartLatLng,this._zoom).subtract(h),this._zoom)}this._moved||(t._moveStart(!0,!1),this._moved=!0),it(this._animRequest);var p=g(t._move,t,this._center,this._zoom,{pinch:!0,round:!1},void 0);this._animRequest=et(p,this,!0),GA(A)}},_onTouchEnd:function(){if(!this._moved||!this._zooming){this._zooming=!1;return}this._zooming=!1,it(this._animRequest),_A(document,"touchmove",this._onTouchMove,this),_A(document,"touchend touchcancel",this._onTouchEnd,this),this._map.options.zoomAnimation?this._map._animateZoom(this._center,this._map._limitZoom(this._zoom),!0,this._map.options.zoomSnap):this._map._resetView(this._center,this._map._limitZoom(this._zoom))}});BA.addInitHook("addHandler","touchZoom",Zr),BA.BoxZoom=Nr,BA.DoubleClickZoom=De,BA.Drag=Rr,BA.Keyboard=Gr,BA.ScrollWheelZoom=hi,BA.TapHold=Vr,BA.TouchZoom=Zr,v.Bounds=EA,v.Browser=z,v.CRS=lt,v.Canvas=Or,v.Circle=Vi,v.CircleMarker=oi,v.Class=St,v.Control=Qt,v.DivIcon=Zn,v.DivOverlay=xt,v.DomEvent=To,v.DomUtil=ii,v.Draggable=Xt,v.Evented=ze,v.FeatureGroup=YA,v.GeoJSON=qA,v.GridLayer=Oe,v.Handler=Et,v.Icon=ue,v.ImageOverlay=fe,v.LatLng=CA,v.LatLngBounds=rt,v.Layer=JA,v.LayerGroup=$A,v.LineUtil=yr,v.Map=BA,v.Marker=Yt,v.Mixin=Oo,v.Path=Nt,v.Point=rA,v.PolyUtil=Do,v.Polygon=zA,v.Polyline=It,v.Popup=Wi,v.PosAnimation=VA,v.Projection=vA,v.Rectangle=kr,v.Renderer=Bt,v.SVG=ui,v.SVGOverlay=dt,v.TileLayer=de,v.Tooltip=Ji,v.Transformation=Qi,v.Util=Qo,v.VideoOverlay=br,v.bind=g,v.bounds=nt,v.canvas=Dr,v.circle=Zi,v.circleMarker=Ir,v.control=ni,v.divIcon=Pr,v.extend=I,v.featureGroup=DA,v.geoJSON=Hr,v.geoJson=Zo,v.gridLayer=Sr,v.icon=ZA,v.imageOverlay=Lr,v.latLng=dA,v.latLngBounds=SA,v.layerGroup=Vo,v.map=be,v.marker=UA,v.point=eA,v.polygon=qt,v.polyline=xr,v.popup=Xi,v.rectangle=Jo,v.setOptions=X,v.stamp=C,v.svg=Kr,v.svgOverlay=Mr,v.tileLayer=zn,v.tooltip=Tr,v.transformation=Qe,v.version=N,v.videoOverlay=zo;var qo=window.L;v.noConflict=function(){return window.L=qo,this},window.L=v})});var hc=ba((xs,Hs)=>{"use strict";(function(v,N){typeof xs=="object"&&typeof Hs<"u"?Hs.exports=N():typeof define=="function"&&define.amd?define(N):(v=typeof globalThis<"u"?globalThis:v||self,v.html2canvas=N())})(xs,function(){"use strict";var v=function(i,e){return v=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(o,n){o.__proto__=n}||function(o,n){for(var a in n)Object.prototype.hasOwnProperty.call(n,a)&&(o[a]=n[a])},v(i,e)};function N(i,e){if(typeof e!="function"&&e!==null)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");v(i,e);function o(){this.constructor=i}i.prototype=e===null?Object.create(e):(o.prototype=e.prototype,new o)}var I=function(){return I=Object.assign||function(e){for(var o,n=1,a=arguments.length;n<a;n++){o=arguments[n];for(var l in o)Object.prototype.hasOwnProperty.call(o,l)&&(e[l]=o[l])}return e},I.apply(this,arguments)};function B(i,e,o,n){function a(l){return l instanceof o?l:new o(function(u){u(l)})}return new(o||(o=Promise))(function(l,u){function d(m){try{w(n.next(m))}catch(_){u(_)}}function f(m){try{w(n.throw(m))}catch(_){u(_)}}function w(m){m.done?l(m.value):a(m.value).then(d,f)}w((n=n.apply(i,e||[])).next())})}function g(i,e){var o={label:0,sent:function(){if(l[0]&1)throw l[1];return l[1]},trys:[],ops:[]},n,a,l,u;return u={next:d(0),throw:d(1),return:d(2)},typeof Symbol=="function"&&(u[Symbol.iterator]=function(){return this}),u;function d(w){return function(m){return f([w,m])}}function f(w){if(n)throw new TypeError("Generator is already executing.");for(;o;)try{if(n=1,a&&(l=w[0]&2?a.return:w[0]?a.throw||((l=a.return)&&l.call(a),0):a.next)&&!(l=l.call(a,w[1])).done)return l;switch(a=0,l&&(w=[w[0]&2,l.value]),w[0]){case 0:case 1:l=w;break;case 4:return o.label++,{value:w[1],done:!1};case 5:o.label++,a=w[1],w=[0];continue;case 7:w=o.ops.pop(),o.trys.pop();continue;default:if(l=o.trys,!(l=l.length>0&&l[l.length-1])&&(w[0]===6||w[0]===2)){o=0;continue}if(w[0]===3&&(!l||w[1]>l[0]&&w[1]<l[3])){o.label=w[1];break}if(w[0]===6&&o.label<l[1]){o.label=l[1],l=w;break}if(l&&o.label<l[2]){o.label=l[2],o.ops.push(w);break}l[2]&&o.ops.pop(),o.trys.pop();continue}w=e.call(i,o)}catch(m){w=[6,m],a=0}finally{n=l=0}if(w[0]&5)throw w[1];return{value:w[0]?w[1]:void 0,done:!0}}}function Q(i,e,o){if(o||arguments.length===2)for(var n=0,a=e.length,l;n<a;n++)(l||!(n in e))&&(l||(l=Array.prototype.slice.call(e,0,n)),l[n]=e[n]);return i.concat(l||e)}for(var C=function(){function i(e,o,n,a){this.left=e,this.top=o,this.width=n,this.height=a}return i.prototype.add=function(e,o,n,a){return new i(this.left+e,this.top+o,this.width+n,this.height+a)},i.fromClientRect=function(e,o){return new i(o.left+e.windowBounds.left,o.top+e.windowBounds.top,o.width,o.height)},i.fromDOMRectList=function(e,o){var n=Array.from(o).find(function(a){return a.width!==0});return n?new i(n.left+e.windowBounds.left,n.top+e.windowBounds.top,n.width,n.height):i.EMPTY},i.EMPTY=new i(0,0,0,0),i}(),T=function(i,e){return C.fromClientRect(i,e.getBoundingClientRect())},x=function(i){var e=i.body,o=i.documentElement;if(!e||!o)throw new Error("Unable to get document size");var n=Math.max(Math.max(e.scrollWidth,o.scrollWidth),Math.max(e.offsetWidth,o.offsetWidth),Math.max(e.clientWidth,o.clientWidth)),a=Math.max(Math.max(e.scrollHeight,o.scrollHeight),Math.max(e.offsetHeight,o.offsetHeight),Math.max(e.clientHeight,o.clientHeight));return new C(0,0,n,a)},b=function(i){for(var e=[],o=0,n=i.length;o<n;){var a=i.charCodeAt(o++);if(a>=55296&&a<=56319&&o<n){var l=i.charCodeAt(o++);(l&64512)===56320?e.push(((a&1023)<<10)+(l&1023)+65536):(e.push(a),o--)}else e.push(a)}return e},F=function(){for(var i=[],e=0;e<arguments.length;e++)i[e]=arguments[e];if(String.fromCodePoint)return String.fromCodePoint.apply(String,i);var o=i.length;if(!o)return"";for(var n=[],a=-1,l="";++a<o;){var u=i[a];u<=65535?n.push(u):(u-=65536,n.push((u>>10)+55296,u%1024+56320)),(a+1===o||n.length>16384)&&(l+=String.fromCharCode.apply(String,n),n.length=0)}return l},Z="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",R=typeof Uint8Array>"u"?[]:new Uint8Array(256),X=0;X<Z.length;X++)R[Z.charCodeAt(X)]=X;for(var IA="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",j=typeof Uint8Array>"u"?[]:new Uint8Array(256),PA=0;PA<IA.length;PA++)j[IA.charCodeAt(PA)]=PA;for(var fA=function(i){var e=i.length*.75,o=i.length,n,a=0,l,u,d,f;i[i.length-1]==="="&&(e--,i[i.length-2]==="="&&e--);var w=typeof ArrayBuffer<"u"&&typeof Uint8Array<"u"&&typeof Uint8Array.prototype.slice<"u"?new ArrayBuffer(e):new Array(e),m=Array.isArray(w)?w:new Uint8Array(w);for(n=0;n<o;n+=4)l=j[i.charCodeAt(n)],u=j[i.charCodeAt(n+1)],d=j[i.charCodeAt(n+2)],f=j[i.charCodeAt(n+3)],m[a++]=l<<2|u>>4,m[a++]=(u&15)<<4|d>>2,m[a++]=(d&3)<<6|f&63;return w},ve=function(i){for(var e=i.length,o=[],n=0;n<e;n+=2)o.push(i[n+1]<<8|i[n]);return o},KA=function(i){for(var e=i.length,o=[],n=0;n<e;n+=4)o.push(i[n+3]<<24|i[n+2]<<16|i[n+1]<<8|i[n]);return o},Pt=5,Ci=11,vi=2,Bn=Ci-Pt,gn=65536>>Pt,et=1<<Pt,it=et-1,Qo=1024>>Pt,St=gn+Qo,_o=St,ct=32,ze=_o+ct,rA=65536>>Ci,er=1<<Bn,eA=er-1,EA=function(i,e,o){return i.slice?i.slice(e,o):new Uint16Array(Array.prototype.slice.call(i,e,o))},nt=function(i,e,o){return i.slice?i.slice(e,o):new Uint32Array(Array.prototype.slice.call(i,e,o))},rt=function(i,e){var o=fA(i),n=Array.isArray(o)?KA(o):new Uint32Array(o),a=Array.isArray(o)?ve(o):new Uint16Array(o),l=24,u=EA(a,l/2,n[4]/2),d=n[5]===2?EA(a,(l+n[4])/2):nt(n,Math.ceil((l+n[4])/4));return new SA(n[0],n[1],n[2],n[3],u,d)},SA=function(){function i(e,o,n,a,l,u){this.initialValue=e,this.errorValue=o,this.highStart=n,this.highValueIndex=a,this.index=l,this.data=u}return i.prototype.get=function(e){var o;if(e>=0){if(e<55296||e>56319&&e<=65535)return o=this.index[e>>Pt],o=(o<<vi)+(e&it),this.data[o];if(e<=65535)return o=this.index[gn+(e-55296>>Pt)],o=(o<<vi)+(e&it),this.data[o];if(e<this.highStart)return o=ze-rA+(e>>Ci),o=this.index[o],o+=e>>Pt&eA,o=this.index[o],o=(o<<vi)+(e&it),this.data[o];if(e<=1114111)return this.data[this.highValueIndex]}return this.errorValue},i}(),CA="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",dA=typeof Uint8Array>"u"?[]:new Uint8Array(256),lt=0;lt<CA.length;lt++)dA[CA.charCodeAt(lt)]=lt;var Gt="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",pn=50,wn=1,Qi=2,Qe=3,mn=4,Uo=5,Cn=7,vn=8,_i=9,vt=10,Qn=11,_n=12,We=13,ir=14,_e=15,Un=16,Ui=17,ee=18,nr=19,Fn=20,yn=21,Ue=22,Fi=23,Fe=24,ot=25,ie=26,ye=27,Ee=28,Xe=29,ne=30,Fo=31,Je=32,Ye=33,yi=34,En=35,In=36,qe=37,xn=38,Ei=39,Ii=40,$e=41,rr=42,yo=43,Eo=[9001,65288],or="!",AA="\xD7",z="\xF7",xi=rt(Gt),Ft=[ne,In],Hi=[wn,Qi,Qe,Uo],Hn=[vt,vn],Li=[ye,ie],sr=Hi.concat(Hn),re=[xn,Ei,Ii,yi,En],ar=[_e,We],Io=function(i,e){e===void 0&&(e="strict");var o=[],n=[],a=[];return i.forEach(function(l,u){var d=xi.get(l);if(d>pn?(a.push(!0),d-=pn):a.push(!1),["normal","auto","loose"].indexOf(e)!==-1&&[8208,8211,12316,12448].indexOf(l)!==-1)return n.push(u),o.push(Un);if(d===mn||d===Qn){if(u===0)return n.push(u),o.push(ne);var f=o[u-1];return sr.indexOf(f)===-1?(n.push(n[u-1]),o.push(f)):(n.push(u),o.push(ne))}if(n.push(u),d===Fo)return o.push(e==="strict"?yn:qe);if(d===rr||d===Xe)return o.push(ne);if(d===yo)return l>=131072&&l<=196605||l>=196608&&l<=262141?o.push(qe):o.push(ne);o.push(d)}),[n,o,a]},Ln=function(i,e,o,n){var a=n[o];if(Array.isArray(i)?i.indexOf(a)!==-1:i===a)for(var l=o;l<=n.length;){l++;var u=n[l];if(u===e)return!0;if(u!==vt)break}if(a===vt)for(var l=o;l>0;){l--;var d=n[l];if(Array.isArray(i)?i.indexOf(d)!==-1:i===d)for(var f=o;f<=n.length;){f++;var u=n[f];if(u===e)return!0;if(u!==vt)break}if(d!==vt)break}return!1},cr=function(i,e){for(var o=i;o>=0;){var n=e[o];if(n===vt)o--;else return n}return 0},xo=function(i,e,o,n,a){if(o[n]===0)return AA;var l=n-1;if(Array.isArray(a)&&a[l]===!0)return AA;var u=l-1,d=l+1,f=e[l],w=u>=0?e[u]:0,m=e[d];if(f===Qi&&m===Qe)return AA;if(Hi.indexOf(f)!==-1)return or;if(Hi.indexOf(m)!==-1||Hn.indexOf(m)!==-1)return AA;if(cr(l,e)===vn)return z;if(xi.get(i[l])===Qn||(f===Je||f===Ye)&&xi.get(i[d])===Qn||f===Cn||m===Cn||f===_i||[vt,We,_e].indexOf(f)===-1&&m===_i||[Ui,ee,nr,Fe,Ee].indexOf(m)!==-1||cr(l,e)===Ue||Ln(Fi,Ue,l,e)||Ln([Ui,ee],yn,l,e)||Ln(_n,_n,l,e))return AA;if(f===vt)return z;if(f===Fi||m===Fi)return AA;if(m===Un||f===Un)return z;if([We,_e,yn].indexOf(m)!==-1||f===ir||w===In&&ar.indexOf(f)!==-1||f===Ee&&m===In||m===Fn||Ft.indexOf(m)!==-1&&f===ot||Ft.indexOf(f)!==-1&&m===ot||f===ye&&[qe,Je,Ye].indexOf(m)!==-1||[qe,Je,Ye].indexOf(f)!==-1&&m===ie||Ft.indexOf(f)!==-1&&Li.indexOf(m)!==-1||Li.indexOf(f)!==-1&&Ft.indexOf(m)!==-1||[ye,ie].indexOf(f)!==-1&&(m===ot||[Ue,_e].indexOf(m)!==-1&&e[d+1]===ot)||[Ue,_e].indexOf(f)!==-1&&m===ot||f===ot&&[ot,Ee,Fe].indexOf(m)!==-1)return AA;if([ot,Ee,Fe,Ui,ee].indexOf(m)!==-1)for(var _=l;_>=0;){var E=e[_];if(E===ot)return AA;if([Ee,Fe].indexOf(E)!==-1)_--;else break}if([ye,ie].indexOf(m)!==-1)for(var _=[Ui,ee].indexOf(f)!==-1?u:l;_>=0;){var E=e[_];if(E===ot)return AA;if([Ee,Fe].indexOf(E)!==-1)_--;else break}if(xn===f&&[xn,Ei,yi,En].indexOf(m)!==-1||[Ei,yi].indexOf(f)!==-1&&[Ei,Ii].indexOf(m)!==-1||[Ii,En].indexOf(f)!==-1&&m===Ii||re.indexOf(f)!==-1&&[Fn,ie].indexOf(m)!==-1||re.indexOf(m)!==-1&&f===ye||Ft.indexOf(f)!==-1&&Ft.indexOf(m)!==-1||f===Fe&&Ft.indexOf(m)!==-1||Ft.concat(ot).indexOf(f)!==-1&&m===Ue&&Eo.indexOf(i[d])===-1||Ft.concat(ot).indexOf(m)!==-1&&f===ee)return AA;if(f===$e&&m===$e){for(var K=o[l],M=1;K>0&&(K--,e[K]===$e);)M++;if(M%2!==0)return AA}return f===Je&&m===Ye?AA:z},lr=function(i,e){e||(e={lineBreak:"normal",wordBreak:"normal"});var o=Io(i,e.lineBreak),n=o[0],a=o[1],l=o[2];(e.wordBreak==="break-all"||e.wordBreak==="break-word")&&(a=a.map(function(d){return[ot,ne,rr].indexOf(d)!==-1?qe:d}));var u=e.wordBreak==="keep-all"?l.map(function(d,f){return d&&i[f]>=19968&&i[f]<=40959}):void 0;return[n,a,u]},Ho=function(){function i(e,o,n,a){this.codePoints=e,this.required=o===or,this.start=n,this.end=a}return i.prototype.slice=function(){return F.apply(void 0,this.codePoints.slice(this.start,this.end))},i}(),bi=function(i,e){var o=b(i),n=lr(o,e),a=n[0],l=n[1],u=n[2],d=o.length,f=0,w=0;return{next:function(){if(w>=d)return{done:!0,value:null};for(var m=AA;w<d&&(m=xo(o,l,a,++w,u))===AA;);if(m!==AA||w===d){var _=new Ho(o,m,f,w);return f=w,{value:_,done:!1}}return{done:!0,value:null}}}},Lo=1,bo=2,je=4,ur=8,Mi=10,Ti=47,Ot=92,hr=9,fr=32,Vt=34,lA=61,xA=35,Pi=36,Ie=37,Dt=39,xe=40,iA=41,bA=95,XA=45,Si=33,ut=60,Mo=62,Oi=64,oe=91,OA=93,se=61,Ai=123,Zt=63,bn=125,He=124,Mn=126,Tn=128,ti=65533,ei=42,Kt=43,Di=44,dr=58,Pn=59,ii=46,oA=0,yt=8,_A=11,Br=14,Sn=31,On=127,ht=-1,zt=48,Ki=97,Le=101,GA=102,ae=117,gr=122,Dn=65,pr=69,Kn=70,kn=85,To=90,VA=function(i){return i>=zt&&i<=57},BA=function(i){return i>=55296&&i<=57343},be=function(i){return VA(i)||i>=Dn&&i<=Kn||i>=Ki&&i<=GA},Qt=function(i){return i>=Ki&&i<=gr},ni=function(i){return i>=Dn&&i<=To},wr=function(i){return Qt(i)||ni(i)},Po=function(i){return i>=Tn},Me=function(i){return i===Mi||i===hr||i===fr},ki=function(i){return wr(i)||Po(i)||i===bA},Nn=function(i){return ki(i)||VA(i)||i===XA},So=function(i){return i>=oA&&i<=yt||i===_A||i>=Br&&i<=Sn||i===On},Wt=function(i,e){return i!==Ot?!1:e!==Mi},Te=function(i,e,o){return i===XA?ki(e)||Wt(e,o):ki(i)?!0:!!(i===Ot&&Wt(i,e))},Rn=function(i,e,o){return i===Kt||i===XA?VA(e)?!0:e===ii&&VA(o):VA(i===ii?e:i)},Et=function(i){var e=0,o=1;(i[e]===Kt||i[e]===XA)&&(i[e]===XA&&(o=-1),e++);for(var n=[];VA(i[e]);)n.push(i[e++]);var a=n.length?parseInt(F.apply(void 0,n),10):0;i[e]===ii&&e++;for(var l=[];VA(i[e]);)l.push(i[e++]);var u=l.length,d=u?parseInt(F.apply(void 0,l),10):0;(i[e]===pr||i[e]===Le)&&e++;var f=1;(i[e]===Kt||i[e]===XA)&&(i[e]===XA&&(f=-1),e++);for(var w=[];VA(i[e]);)w.push(i[e++]);var m=w.length?parseInt(F.apply(void 0,w),10):0;return o*(a+d*Math.pow(10,-u))*Math.pow(10,f*m)},Oo={type:2},mr={type:3},Xt={type:4},Cr={type:13},vr={type:8},Gn={type:21},Do={type:9},Qr={type:10},_r={type:11},Ko={type:12},ko={type:14},Pe={type:23},No={type:1},Ur={type:25},Fr={type:24},Ni={type:26},ce={type:27},Ro={type:28},ri={type:29},ft={type:31},Ri={type:32},Vn=function(){function i(){this._value=[]}return i.prototype.write=function(e){this._value=this._value.concat(b(e))},i.prototype.read=function(){for(var e=[],o=this.consumeToken();o!==Ri;)e.push(o),o=this.consumeToken();return e},i.prototype.consumeToken=function(){var e=this.consumeCodePoint();switch(e){case Vt:return this.consumeStringToken(Vt);case xA:var o=this.peekCodePoint(0),n=this.peekCodePoint(1),a=this.peekCodePoint(2);if(Nn(o)||Wt(n,a)){var l=Te(o,n,a)?bo:Lo,u=this.consumeName();return{type:5,value:u,flags:l}}break;case Pi:if(this.peekCodePoint(0)===lA)return this.consumeCodePoint(),Cr;break;case Dt:return this.consumeStringToken(Dt);case xe:return Oo;case iA:return mr;case ei:if(this.peekCodePoint(0)===lA)return this.consumeCodePoint(),ko;break;case Kt:if(Rn(e,this.peekCodePoint(0),this.peekCodePoint(1)))return this.reconsumeCodePoint(e),this.consumeNumericToken();break;case Di:return Xt;case XA:var d=e,f=this.peekCodePoint(0),w=this.peekCodePoint(1);if(Rn(d,f,w))return this.reconsumeCodePoint(e),this.consumeNumericToken();if(Te(d,f,w))return this.reconsumeCodePoint(e),this.consumeIdentLikeToken();if(f===XA&&w===Mo)return this.consumeCodePoint(),this.consumeCodePoint(),Fr;break;case ii:if(Rn(e,this.peekCodePoint(0),this.peekCodePoint(1)))return this.reconsumeCodePoint(e),this.consumeNumericToken();break;case Ti:if(this.peekCodePoint(0)===ei)for(this.consumeCodePoint();;){var m=this.consumeCodePoint();if(m===ei&&(m=this.consumeCodePoint(),m===Ti))return this.consumeToken();if(m===ht)return this.consumeToken()}break;case dr:return Ni;case Pn:return ce;case ut:if(this.peekCodePoint(0)===Si&&this.peekCodePoint(1)===XA&&this.peekCodePoint(2)===XA)return this.consumeCodePoint(),this.consumeCodePoint(),Ur;break;case Oi:var _=this.peekCodePoint(0),E=this.peekCodePoint(1),K=this.peekCodePoint(2);if(Te(_,E,K)){var u=this.consumeName();return{type:7,value:u}}break;case oe:return Ro;case Ot:if(Wt(e,this.peekCodePoint(0)))return this.reconsumeCodePoint(e),this.consumeIdentLikeToken();break;case OA:return ri;case se:if(this.peekCodePoint(0)===lA)return this.consumeCodePoint(),vr;break;case Ai:return _r;case bn:return Ko;case ae:case kn:var M=this.peekCodePoint(0),S=this.peekCodePoint(1);return M===Kt&&(be(S)||S===Zt)&&(this.consumeCodePoint(),this.consumeUnicodeRangeToken()),this.reconsumeCodePoint(e),this.consumeIdentLikeToken();case He:if(this.peekCodePoint(0)===lA)return this.consumeCodePoint(),Do;if(this.peekCodePoint(0)===He)return this.consumeCodePoint(),Gn;break;case Mn:if(this.peekCodePoint(0)===lA)return this.consumeCodePoint(),Qr;break;case ht:return Ri}return Me(e)?(this.consumeWhiteSpace(),ft):VA(e)?(this.reconsumeCodePoint(e),this.consumeNumericToken()):ki(e)?(this.reconsumeCodePoint(e),this.consumeIdentLikeToken()):{type:6,value:F(e)}},i.prototype.consumeCodePoint=function(){var e=this._value.shift();return typeof e>"u"?-1:e},i.prototype.reconsumeCodePoint=function(e){this._value.unshift(e)},i.prototype.peekCodePoint=function(e){return e>=this._value.length?-1:this._value[e]},i.prototype.consumeUnicodeRangeToken=function(){for(var e=[],o=this.consumeCodePoint();be(o)&&e.length<6;)e.push(o),o=this.consumeCodePoint();for(var n=!1;o===Zt&&e.length<6;)e.push(o),o=this.consumeCodePoint(),n=!0;if(n){var a=parseInt(F.apply(void 0,e.map(function(f){return f===Zt?zt:f})),16),l=parseInt(F.apply(void 0,e.map(function(f){return f===Zt?Kn:f})),16);return{type:30,start:a,end:l}}var u=parseInt(F.apply(void 0,e),16);if(this.peekCodePoint(0)===XA&&be(this.peekCodePoint(1))){this.consumeCodePoint(),o=this.consumeCodePoint();for(var d=[];be(o)&&d.length<6;)d.push(o),o=this.consumeCodePoint();var l=parseInt(F.apply(void 0,d),16);return{type:30,start:u,end:l}}else return{type:30,start:u,end:u}},i.prototype.consumeIdentLikeToken=function(){var e=this.consumeName();return e.toLowerCase()==="url"&&this.peekCodePoint(0)===xe?(this.consumeCodePoint(),this.consumeUrlToken()):this.peekCodePoint(0)===xe?(this.consumeCodePoint(),{type:19,value:e}):{type:20,value:e}},i.prototype.consumeUrlToken=function(){var e=[];if(this.consumeWhiteSpace(),this.peekCodePoint(0)===ht)return{type:22,value:""};var o=this.peekCodePoint(0);if(o===Dt||o===Vt){var n=this.consumeStringToken(this.consumeCodePoint());return n.type===0&&(this.consumeWhiteSpace(),this.peekCodePoint(0)===ht||this.peekCodePoint(0)===iA)?(this.consumeCodePoint(),{type:22,value:n.value}):(this.consumeBadUrlRemnants(),Pe)}for(;;){var a=this.consumeCodePoint();if(a===ht||a===iA)return{type:22,value:F.apply(void 0,e)};if(Me(a))return this.consumeWhiteSpace(),this.peekCodePoint(0)===ht||this.peekCodePoint(0)===iA?(this.consumeCodePoint(),{type:22,value:F.apply(void 0,e)}):(this.consumeBadUrlRemnants(),Pe);if(a===Vt||a===Dt||a===xe||So(a))return this.consumeBadUrlRemnants(),Pe;if(a===Ot)if(Wt(a,this.peekCodePoint(0)))e.push(this.consumeEscapedCodePoint());else return this.consumeBadUrlRemnants(),Pe;else e.push(a)}},i.prototype.consumeWhiteSpace=function(){for(;Me(this.peekCodePoint(0));)this.consumeCodePoint()},i.prototype.consumeBadUrlRemnants=function(){for(;;){var e=this.consumeCodePoint();if(e===iA||e===ht)return;Wt(e,this.peekCodePoint(0))&&this.consumeEscapedCodePoint()}},i.prototype.consumeStringSlice=function(e){for(var o=5e4,n="";e>0;){var a=Math.min(o,e);n+=F.apply(void 0,this._value.splice(0,a)),e-=a}return this._value.shift(),n},i.prototype.consumeStringToken=function(e){var o="",n=0;do{var a=this._value[n];if(a===ht||a===void 0||a===e)return o+=this.consumeStringSlice(n),{type:0,value:o};if(a===Mi)return this._value.splice(0,n),No;if(a===Ot){var l=this._value[n+1];l!==ht&&l!==void 0&&(l===Mi?(o+=this.consumeStringSlice(n),n=-1,this._value.shift()):Wt(a,l)&&(o+=this.consumeStringSlice(n),o+=F(this.consumeEscapedCodePoint()),n=-1))}n++}while(!0)},i.prototype.consumeNumber=function(){var e=[],o=je,n=this.peekCodePoint(0);for((n===Kt||n===XA)&&e.push(this.consumeCodePoint());VA(this.peekCodePoint(0));)e.push(this.consumeCodePoint());n=this.peekCodePoint(0);var a=this.peekCodePoint(1);if(n===ii&&VA(a))for(e.push(this.consumeCodePoint(),this.consumeCodePoint()),o=ur;VA(this.peekCodePoint(0));)e.push(this.consumeCodePoint());n=this.peekCodePoint(0),a=this.peekCodePoint(1);var l=this.peekCodePoint(2);if((n===pr||n===Le)&&((a===Kt||a===XA)&&VA(l)||VA(a)))for(e.push(this.consumeCodePoint(),this.consumeCodePoint()),o=ur;VA(this.peekCodePoint(0));)e.push(this.consumeCodePoint());return[Et(e),o]},i.prototype.consumeNumericToken=function(){var e=this.consumeNumber(),o=e[0],n=e[1],a=this.peekCodePoint(0),l=this.peekCodePoint(1),u=this.peekCodePoint(2);if(Te(a,l,u)){var d=this.consumeName();return{type:15,number:o,flags:n,unit:d}}return a===Ie?(this.consumeCodePoint(),{type:16,number:o,flags:n}):{type:17,number:o,flags:n}},i.prototype.consumeEscapedCodePoint=function(){var e=this.consumeCodePoint();if(be(e)){for(var o=F(e);be(this.peekCodePoint(0))&&o.length<6;)o+=F(this.consumeCodePoint());Me(this.peekCodePoint(0))&&this.consumeCodePoint();var n=parseInt(o,16);return n===0||BA(n)||n>1114111?ti:n}return e===ht?ti:e},i.prototype.consumeName=function(){for(var e="";;){var o=this.consumeCodePoint();if(Nn(o))e+=F(o);else if(Wt(o,this.peekCodePoint(0)))e+=F(this.consumeEscapedCodePoint());else return this.reconsumeCodePoint(o),e}},i}(),yr=function(){function i(e){this._tokens=e}return i.create=function(e){var o=new Vn;return o.write(e),new i(o.read())},i.parseValue=function(e){return i.create(e).parseComponentValue()},i.parseValues=function(e){return i.create(e).parseComponentValues()},i.prototype.parseComponentValue=function(){for(var e=this.consumeToken();e.type===31;)e=this.consumeToken();if(e.type===32)throw new SyntaxError("Error parsing CSS component value, unexpected EOF");this.reconsumeToken(e);var o=this.consumeComponentValue();do e=this.consumeToken();while(e.type===31);if(e.type===32)return o;throw new SyntaxError("Error parsing CSS component value, multiple values found when expecting only one")},i.prototype.parseComponentValues=function(){for(var e=[];;){var o=this.consumeComponentValue();if(o.type===32)return e;e.push(o),e.push()}},i.prototype.consumeComponentValue=function(){var e=this.consumeToken();switch(e.type){case 11:case 28:case 2:return this.consumeSimpleBlock(e.type);case 19:return this.consumeFunction(e)}return e},i.prototype.consumeSimpleBlock=function(e){for(var o={type:e,values:[]},n=this.consumeToken();;){if(n.type===32||Vo(n,e))return o;this.reconsumeToken(n),o.values.push(this.consumeComponentValue()),n=this.consumeToken()}},i.prototype.consumeFunction=function(e){for(var o={name:e.value,values:[],type:18};;){var n=this.consumeToken();if(n.type===32||n.type===3)return o;this.reconsumeToken(n),o.values.push(this.consumeComponentValue())}},i.prototype.consumeToken=function(){var e=this._tokens.shift();return typeof e>"u"?Ri:e},i.prototype.reconsumeToken=function(e){this._tokens.unshift(e)},i}(),le=function(i){return i.type===15},Jt=function(i){return i.type===17},vA=function(i){return i.type===20},Go=function(i){return i.type===0},Gi=function(i,e){return vA(i)&&i.value===e},Er=function(i){return i.type!==31},JA=function(i){return i.type!==31&&i.type!==4},$A=function(i){var e=[],o=[];return i.forEach(function(n){if(n.type===4){if(o.length===0)throw new Error("Error parsing function args, zero tokens for arg");e.push(o),o=[];return}n.type!==31&&o.push(n)}),o.length&&e.push(o),e},Vo=function(i,e){return e===11&&i.type===12||e===28&&i.type===29?!0:e===2&&i.type===3},YA=function(i){return i.type===17||i.type===15},DA=function(i){return i.type===16||YA(i)},ue=function(i){return i.length>1?[i[0],i[1]]:[i[0]]},ZA={type:17,number:0,flags:je},he={type:16,number:50,flags:je},kt={type:16,number:100,flags:je},Yt=function(i,e,o){var n=i[0],a=i[1];return[UA(n,e),UA(typeof a<"u"?a:n,o)]},UA=function(i,e){if(i.type===16)return i.number/100*e;if(le(i))switch(i.unit){case"rem":case"em":return 16*i.number;case"px":default:return i.number}return i.number},Nt="deg",oi="grad",Ir="rad",Vi="turn",Zi={name:"angle",parse:function(i,e){if(e.type===15)switch(e.unit){case Nt:return Math.PI*e.number/180;case oi:return Math.PI/200*e.number;case Ir:return e.number;case Vi:return Math.PI*2*e.number}throw new Error("Unsupported angle type")}},It=function(i){return i.type===15&&(i.unit===Nt||i.unit===oi||i.unit===Ir||i.unit===Vi)},xr=function(i){var e=i.filter(vA).map(function(o){return o.value}).join(" ");switch(e){case"to bottom right":case"to right bottom":case"left top":case"top left":return[ZA,ZA];case"to top":case"bottom":return zA(0);case"to bottom left":case"to left bottom":case"right top":case"top right":return[ZA,kt];case"to right":case"left":return zA(90);case"to top left":case"to left top":case"right bottom":case"bottom right":return[kt,kt];case"to bottom":case"top":return zA(180);case"to top right":case"to right top":case"left bottom":case"bottom left":return[kt,ZA];case"to left":case"right":return zA(270)}return 0},zA=function(i){return Math.PI*i/180},qt={name:"color",parse:function(i,e){if(e.type===18){var o=Se[e.name];if(typeof o>"u")throw new Error('Attempting to parse an unsupported color function "'+e.name+'"');return o(i,e.values)}if(e.type===5){if(e.value.length===3){var n=e.value.substring(0,1),a=e.value.substring(1,2),l=e.value.substring(2,3);return Rt(parseInt(n+n,16),parseInt(a+a,16),parseInt(l+l,16),1)}if(e.value.length===4){var n=e.value.substring(0,1),a=e.value.substring(1,2),l=e.value.substring(2,3),u=e.value.substring(3,4);return Rt(parseInt(n+n,16),parseInt(a+a,16),parseInt(l+l,16),parseInt(u+u,16)/255)}if(e.value.length===6){var n=e.value.substring(0,2),a=e.value.substring(2,4),l=e.value.substring(4,6);return Rt(parseInt(n,16),parseInt(a,16),parseInt(l,16),1)}if(e.value.length===8){var n=e.value.substring(0,2),a=e.value.substring(2,4),l=e.value.substring(4,6),u=e.value.substring(6,8);return Rt(parseInt(n,16),parseInt(a,16),parseInt(l,16),parseInt(u,16)/255)}}if(e.type===20){var d=_t[e.value.toUpperCase()];if(typeof d<"u")return d}return _t.TRANSPARENT}},qA=function(i){return(255&i)===0},MA=function(i){var e=255&i,o=255&i>>8,n=255&i>>16,a=255&i>>24;return e<255?"rgba("+a+","+n+","+o+","+e/255+")":"rgb("+a+","+n+","+o+")"},Rt=function(i,e,o,n){return(i<<24|e<<16|o<<8|Math.round(n*255)<<0)>>>0},zi=function(i,e){if(i.type===17)return i.number;if(i.type===16){var o=e===3?1:255;return e===3?i.number/100*o:Math.round(i.number/100*o)}return 0},si=function(i,e){var o=e.filter(JA);if(o.length===3){var n=o.map(zi),a=n[0],l=n[1],u=n[2];return Rt(a,l,u,1)}if(o.length===4){var d=o.map(zi),a=d[0],l=d[1],u=d[2],f=d[3];return Rt(a,l,u,f)}return 0};function ai(i,e,o){return o<0&&(o+=1),o>=1&&(o-=1),o<1/6?(e-i)*o*6+i:o<1/2?e:o<2/3?(e-i)*6*(2/3-o)+i:i}var ci=function(i,e){var o=e.filter(JA),n=o[0],a=o[1],l=o[2],u=o[3],d=(n.type===17?zA(n.number):Zi.parse(i,n))/(Math.PI*2),f=DA(a)?a.number/100:0,w=DA(l)?l.number/100:0,m=typeof u<"u"&&DA(u)?UA(u,1):1;if(f===0)return Rt(w*255,w*255,w*255,1);var _=w<=.5?w*(f+1):w+f-w*f,E=w*2-_,K=ai(E,_,d+1/3),M=ai(E,_,d),S=ai(E,_,d-1/3);return Rt(K*255,M*255,S*255,m)},Se={hsl:ci,hsla:ci,rgb:si,rgba:si},$t=function(i,e){return qt.parse(i,yr.create(e).parseComponentValue())},_t={ALICEBLUE:4042850303,ANTIQUEWHITE:4209760255,AQUA:16777215,AQUAMARINE:2147472639,AZURE:4043309055,BEIGE:4126530815,BISQUE:4293182719,BLACK:255,BLANCHEDALMOND:4293643775,BLUE:65535,BLUEVIOLET:2318131967,BROWN:2771004159,BURLYWOOD:3736635391,CADETBLUE:1604231423,CHARTREUSE:2147418367,CHOCOLATE:3530104575,CORAL:4286533887,CORNFLOWERBLUE:1687547391,CORNSILK:4294499583,CRIMSON:3692313855,CYAN:16777215,DARKBLUE:35839,DARKCYAN:9145343,DARKGOLDENROD:3095837695,DARKGRAY:2846468607,DARKGREEN:6553855,DARKGREY:2846468607,DARKKHAKI:3182914559,DARKMAGENTA:2332068863,DARKOLIVEGREEN:1433087999,DARKORANGE:4287365375,DARKORCHID:2570243327,DARKRED:2332033279,DARKSALMON:3918953215,DARKSEAGREEN:2411499519,DARKSLATEBLUE:1211993087,DARKSLATEGRAY:793726975,DARKSLATEGREY:793726975,DARKTURQUOISE:13554175,DARKVIOLET:2483082239,DEEPPINK:4279538687,DEEPSKYBLUE:12582911,DIMGRAY:1768516095,DIMGREY:1768516095,DODGERBLUE:512819199,FIREBRICK:2988581631,FLORALWHITE:4294635775,FORESTGREEN:579543807,FUCHSIA:4278255615,GAINSBORO:3705462015,GHOSTWHITE:4177068031,GOLD:4292280575,GOLDENROD:3668254975,GRAY:2155905279,GREEN:8388863,GREENYELLOW:2919182335,GREY:2155905279,HONEYDEW:4043305215,HOTPINK:4285117695,INDIANRED:3445382399,INDIGO:1258324735,IVORY:4294963455,KHAKI:4041641215,LAVENDER:3873897215,LAVENDERBLUSH:4293981695,LAWNGREEN:2096890111,LEMONCHIFFON:4294626815,LIGHTBLUE:2916673279,LIGHTCORAL:4034953471,LIGHTCYAN:3774873599,LIGHTGOLDENRODYELLOW:4210742015,LIGHTGRAY:3553874943,LIGHTGREEN:2431553791,LIGHTGREY:3553874943,LIGHTPINK:4290167295,LIGHTSALMON:4288707327,LIGHTSEAGREEN:548580095,LIGHTSKYBLUE:2278488831,LIGHTSLATEGRAY:2005441023,LIGHTSLATEGREY:2005441023,LIGHTSTEELBLUE:2965692159,LIGHTYELLOW:4294959359,LIME:16711935,LIMEGREEN:852308735,LINEN:4210091775,MAGENTA:4278255615,MAROON:2147483903,MEDIUMAQUAMARINE:1724754687,MEDIUMBLUE:52735,MEDIUMORCHID:3126187007,MEDIUMPURPLE:2473647103,MEDIUMSEAGREEN:1018393087,MEDIUMSLATEBLUE:2070474495,MEDIUMSPRINGGREEN:16423679,MEDIUMTURQUOISE:1221709055,MEDIUMVIOLETRED:3340076543,MIDNIGHTBLUE:421097727,MINTCREAM:4127193855,MISTYROSE:4293190143,MOCCASIN:4293178879,NAVAJOWHITE:4292783615,NAVY:33023,OLDLACE:4260751103,OLIVE:2155872511,OLIVEDRAB:1804477439,ORANGE:4289003775,ORANGERED:4282712319,ORCHID:3664828159,PALEGOLDENROD:4008225535,PALEGREEN:2566625535,PALETURQUOISE:2951671551,PALEVIOLETRED:3681588223,PAPAYAWHIP:4293907967,PEACHPUFF:4292524543,PERU:3448061951,PINK:4290825215,PLUM:3718307327,POWDERBLUE:2967529215,PURPLE:2147516671,REBECCAPURPLE:1714657791,RED:4278190335,ROSYBROWN:3163525119,ROYALBLUE:1097458175,SADDLEBROWN:2336560127,SALMON:4202722047,SANDYBROWN:4104413439,SEAGREEN:780883967,SEASHELL:4294307583,SIENNA:2689740287,SILVER:3233857791,SKYBLUE:2278484991,SLATEBLUE:1784335871,SLATEGRAY:1887473919,SLATEGREY:1887473919,SNOW:4294638335,SPRINGGREEN:16744447,STEELBLUE:1182971135,TAN:3535047935,TEAL:8421631,THISTLE:3636451583,TOMATO:4284696575,TRANSPARENT:0,TURQUOISE:1088475391,VIOLET:4001558271,WHEAT:4125012991,WHITE:4294967295,WHITESMOKE:4126537215,YELLOW:4294902015,YELLOWGREEN:2597139199},Hr={name:"background-clip",initialValue:"border-box",prefix:!1,type:1,parse:function(i,e){return e.map(function(o){if(vA(o))switch(o.value){case"padding-box":return 1;case"content-box":return 2}return 0})}},Zo={name:"background-color",initialValue:"transparent",prefix:!1,type:3,format:"color"},fe=function(i,e){var o=qt.parse(i,e[0]),n=e[1];return n&&DA(n)?{color:o,stop:n}:{color:o,stop:null}},Lr=function(i,e){var o=i[0],n=i[i.length-1];o.stop===null&&(o.stop=ZA),n.stop===null&&(n.stop=kt);for(var a=[],l=0,u=0;u<i.length;u++){var d=i[u].stop;if(d!==null){var f=UA(d,e);f>l?a.push(f):a.push(l),l=f}else a.push(null)}for(var w=null,u=0;u<a.length;u++){var m=a[u];if(m===null)w===null&&(w=u);else if(w!==null){for(var _=u-w,E=a[w-1],K=(m-E)/(_+1),M=1;M<=_;M++)a[w+M-1]=K*M;w=null}}return i.map(function(S,sA){var W=S.color;return{color:W,stop:Math.max(Math.min(1,a[sA]/e),0)}})},br=function(i,e,o){var n=e/2,a=o/2,l=UA(i[0],e)-n,u=a-UA(i[1],o);return(Math.atan2(u,l)+Math.PI*2)%(Math.PI*2)},zo=function(i,e,o){var n=typeof i=="number"?i:br(i,e,o),a=Math.abs(e*Math.sin(n))+Math.abs(o*Math.cos(n)),l=e/2,u=o/2,d=a/2,f=Math.sin(n-Math.PI/2)*d,w=Math.cos(n-Math.PI/2)*d;return[a,l-w,l+w,u-f,u+f]},dt=function(i,e){return Math.sqrt(i*i+e*e)},Mr=function(i,e,o,n,a){var l=[[0,0],[0,e],[i,0],[i,e]];return l.reduce(function(u,d){var f=d[0],w=d[1],m=dt(o-f,n-w);return(a?m<u.optimumDistance:m>u.optimumDistance)?{optimumCorner:d,optimumDistance:m}:u},{optimumDistance:a?1/0:-1/0,optimumCorner:null}).optimumCorner},xt=function(i,e,o,n,a){var l=0,u=0;switch(i.size){case 0:i.shape===0?l=u=Math.min(Math.abs(e),Math.abs(e-n),Math.abs(o),Math.abs(o-a)):i.shape===1&&(l=Math.min(Math.abs(e),Math.abs(e-n)),u=Math.min(Math.abs(o),Math.abs(o-a)));break;case 2:if(i.shape===0)l=u=Math.min(dt(e,o),dt(e,o-a),dt(e-n,o),dt(e-n,o-a));else if(i.shape===1){var d=Math.min(Math.abs(o),Math.abs(o-a))/Math.min(Math.abs(e),Math.abs(e-n)),f=Mr(n,a,e,o,!0),w=f[0],m=f[1];l=dt(w-e,(m-o)/d),u=d*l}break;case 1:i.shape===0?l=u=Math.max(Math.abs(e),Math.abs(e-n),Math.abs(o),Math.abs(o-a)):i.shape===1&&(l=Math.max(Math.abs(e),Math.abs(e-n)),u=Math.max(Math.abs(o),Math.abs(o-a)));break;case 3:if(i.shape===0)l=u=Math.max(dt(e,o),dt(e,o-a),dt(e-n,o),dt(e-n,o-a));else if(i.shape===1){var d=Math.max(Math.abs(o),Math.abs(o-a))/Math.max(Math.abs(e),Math.abs(e-n)),_=Mr(n,a,e,o,!1),w=_[0],m=_[1];l=dt(w-e,(m-o)/d),u=d*l}break}return Array.isArray(i.size)&&(l=UA(i.size[0],n),u=i.size.length===2?UA(i.size[1],a):l),[l,u]},Wi=function(i,e){var o=zA(180),n=[];return $A(e).forEach(function(a,l){if(l===0){var u=a[0];if(u.type===20&&u.value==="to"){o=xr(a);return}else if(It(u)){o=Zi.parse(i,u);return}}var d=fe(i,a);n.push(d)}),{angle:o,stops:n,type:1}},Xi=function(i,e){var o=zA(180),n=[];return $A(e).forEach(function(a,l){if(l===0){var u=a[0];if(u.type===20&&["top","left","right","bottom"].indexOf(u.value)!==-1){o=xr(a);return}else if(It(u)){o=(Zi.parse(i,u)+zA(270))%zA(360);return}}var d=fe(i,a);n.push(d)}),{angle:o,stops:n,type:1}},Ji=function(i,e){var o=zA(180),n=[],a=1,l=0,u=3,d=[];return $A(e).forEach(function(f,w){var m=f[0];if(w===0){if(vA(m)&&m.value==="linear"){a=1;return}else if(vA(m)&&m.value==="radial"){a=2;return}}if(m.type===18){if(m.name==="from"){var _=qt.parse(i,m.values[0]);n.push({stop:ZA,color:_})}else if(m.name==="to"){var _=qt.parse(i,m.values[0]);n.push({stop:kt,color:_})}else if(m.name==="color-stop"){var E=m.values.filter(JA);if(E.length===2){var _=qt.parse(i,E[1]),K=E[0];Jt(K)&&n.push({stop:{type:16,number:K.number*100,flags:K.flags},color:_})}}}}),a===1?{angle:(o+zA(180))%zA(360),stops:n,type:a}:{size:u,shape:l,stops:n,position:d,type:a}},Tr="closest-side",Zn="farthest-side",Pr="closest-corner",Oe="farthest-corner",Sr="circle",de="ellipse",zn="cover",Wn="contain",Wo=function(i,e){var o=0,n=3,a=[],l=[];return $A(e).forEach(function(u,d){var f=!0;if(d===0){var w=!1;f=u.reduce(function(_,E){if(w)if(vA(E))switch(E.value){case"center":return l.push(he),_;case"top":case"left":return l.push(ZA),_;case"right":case"bottom":return l.push(kt),_}else(DA(E)||YA(E))&&l.push(E);else if(vA(E))switch(E.value){case Sr:return o=0,!1;case de:return o=1,!1;case"at":return w=!0,!1;case Tr:return n=0,!1;case zn:case Zn:return n=1,!1;case Wn:case Pr:return n=2,!1;case Oe:return n=3,!1}else if(YA(E)||DA(E))return Array.isArray(n)||(n=[]),n.push(E),!1;return _},f)}if(f){var m=fe(i,u);a.push(m)}}),{size:n,shape:o,stops:a,position:l,type:2}},Bt=function(i,e){var o=0,n=3,a=[],l=[];return $A(e).forEach(function(u,d){var f=!0;if(d===0?f=u.reduce(function(m,_){if(vA(_))switch(_.value){case"center":return l.push(he),!1;case"top":case"left":return l.push(ZA),!1;case"right":case"bottom":return l.push(kt),!1}else if(DA(_)||YA(_))return l.push(_),!1;return m},f):d===1&&(f=u.reduce(function(m,_){if(vA(_))switch(_.value){case Sr:return o=0,!1;case de:return o=1,!1;case Wn:case Tr:return n=0,!1;case Zn:return n=1,!1;case Pr:return n=2,!1;case zn:case Oe:return n=3,!1}else if(YA(_)||DA(_))return Array.isArray(n)||(n=[]),n.push(_),!1;return m},f)),f){var w=fe(i,u);a.push(w)}}),{size:n,shape:o,stops:a,position:l,type:2}},Or=function(i){return i.type===1},Dr=function(i){return i.type===2},Be={name:"image",parse:function(i,e){if(e.type===22){var o={url:e.value,type:0};return i.cache.addImage(e.value),o}if(e.type===18){var n=li[e.name];if(typeof n>"u")throw new Error('Attempting to parse an unsupported image function "'+e.name+'"');return n(i,e.values)}throw new Error("Unsupported image type "+e.type)}};function Xo(i){return!(i.type===20&&i.value==="none")&&(i.type!==18||!!li[i.name])}for(var li={"linear-gradient":Wi,"-moz-linear-gradient":Xi,"-ms-linear-gradient":Xi,"-o-linear-gradient":Xi,"-webkit-linear-gradient":Xi,"radial-gradient":Wo,"-moz-radial-gradient":Bt,"-ms-radial-gradient":Bt,"-o-radial-gradient":Bt,"-webkit-radial-gradient":Bt,"-webkit-gradient":Ji},ui={name:"background-image",initialValue:"none",type:1,prefix:!1,parse:function(i,e){if(e.length===0)return[];var o=e[0];return o.type===20&&o.value==="none"?[]:e.filter(function(n){return JA(n)&&Xo(n)}).map(function(n){return Be.parse(i,n)})}},Kr={name:"background-origin",initialValue:"border-box",prefix:!1,type:1,parse:function(i,e){return e.map(function(o){if(vA(o))switch(o.value){case"padding-box":return 1;case"content-box":return 2}return 0})}},kr={name:"background-position",initialValue:"0% 0%",type:1,prefix:!1,parse:function(i,e){return $A(e).map(function(o){return o.filter(DA)}).map(ue)}},Jo={name:"background-repeat",initialValue:"repeat",prefix:!1,type:1,parse:function(i,e){return $A(e).map(function(o){return o.filter(vA).map(function(n){return n.value}).join(" ")}).map(Nr)}},Nr=function(i){switch(i){case"no-repeat":return 1;case"repeat-x":case"repeat no-repeat":return 2;case"repeat-y":case"no-repeat repeat":return 3;case"repeat":default:return 0}},De=function(i){return i.AUTO="auto",i.CONTAIN="contain",i.COVER="cover",i}(De||{}),Rr={name:"background-size",initialValue:"0",prefix:!1,type:1,parse:function(i,e){return $A(e).map(function(o){return o.filter(Gr)})}},Gr=function(i){return vA(i)||DA(i)},hi=function(i){return{name:"border-"+i+"-color",initialValue:"transparent",prefix:!1,type:3,format:"color"}},Yo=hi("top"),Vr=hi("right"),Zr=hi("bottom"),qo=hi("left"),A=function(i){return{name:"border-radius-"+i,initialValue:"0 0",prefix:!1,type:1,parse:function(e,o){return ue(o.filter(DA))}}},t=A("top-left"),r=A("top-right"),s=A("bottom-right"),c=A("bottom-left"),h=function(i){return{name:"border-"+i+"-style",initialValue:"solid",prefix:!1,type:2,parse:function(e,o){switch(o){case"none":return 0;case"dashed":return 2;case"dotted":return 3;case"double":return 4}return 1}}},p=h("top"),U=h("right"),y=h("bottom"),H=h("left"),P=function(i){return{name:"border-"+i+"-width",initialValue:"0",type:0,prefix:!1,parse:function(e,o){return le(o)?o.number:0}}},q=P("top"),aA=P("right"),jA=P("bottom"),NA=P("left"),gt={name:"color",initialValue:"transparent",prefix:!1,type:3,format:"color"},st={name:"direction",initialValue:"ltr",prefix:!1,type:2,parse:function(i,e){switch(e){case"rtl":return 1;case"ltr":default:return 0}}},Ke={name:"display",initialValue:"inline-block",prefix:!1,type:1,parse:function(i,e){return e.filter(vA).reduce(function(o,n){return o|$o(n.value)},0)}},$o=function(i){switch(i){case"block":case"-webkit-box":return 2;case"inline":return 4;case"run-in":return 8;case"flow":return 16;case"flow-root":return 32;case"table":return 64;case"flex":case"-webkit-flex":return 128;case"grid":case"-ms-grid":return 256;case"ruby":return 512;case"subgrid":return 1024;case"list-item":return 2048;case"table-row-group":return 4096;case"table-header-group":return 8192;case"table-footer-group":return 16384;case"table-row":return 32768;case"table-cell":return 65536;case"table-column-group":return 131072;case"table-column":return 262144;case"table-caption":return 524288;case"ruby-base":return 1048576;case"ruby-text":return 2097152;case"ruby-base-container":return 4194304;case"ruby-text-container":return 8388608;case"contents":return 16777216;case"inline-block":return 33554432;case"inline-list-item":return 67108864;case"inline-table":return 134217728;case"inline-flex":return 268435456;case"inline-grid":return 536870912}return 0},jo={name:"float",initialValue:"none",prefix:!1,type:2,parse:function(i,e){switch(e){case"left":return 1;case"right":return 2;case"inline-start":return 3;case"inline-end":return 4}return 0}},As={name:"letter-spacing",initialValue:"0",prefix:!1,type:0,parse:function(i,e){return e.type===20&&e.value==="normal"?0:e.type===17||e.type===15?e.number:0}},Yi=function(i){return i.NORMAL="normal",i.STRICT="strict",i}(Yi||{}),ts={name:"line-break",initialValue:"normal",prefix:!1,type:2,parse:function(i,e){switch(e){case"strict":return Yi.STRICT;case"normal":default:return Yi.NORMAL}}},zr={name:"line-height",initialValue:"normal",prefix:!1,type:4},TA=function(i,e){return vA(i)&&i.value==="normal"?1.2*e:i.type===17?e*i.number:DA(i)?UA(i,e):e},qi={name:"list-style-image",initialValue:"none",type:0,prefix:!1,parse:function(i,e){return e.type===20&&e.value==="none"?null:Be.parse(i,e)}},es={name:"list-style-position",initialValue:"outside",prefix:!1,type:2,parse:function(i,e){switch(e){case"inside":return 0;case"outside":default:return 1}}},Xn={name:"list-style-type",initialValue:"none",prefix:!1,type:2,parse:function(i,e){switch(e){case"disc":return 0;case"circle":return 1;case"square":return 2;case"decimal":return 3;case"cjk-decimal":return 4;case"decimal-leading-zero":return 5;case"lower-roman":return 6;case"upper-roman":return 7;case"lower-greek":return 8;case"lower-alpha":return 9;case"upper-alpha":return 10;case"arabic-indic":return 11;case"armenian":return 12;case"bengali":return 13;case"cambodian":return 14;case"cjk-earthly-branch":return 15;case"cjk-heavenly-stem":return 16;case"cjk-ideographic":return 17;case"devanagari":return 18;case"ethiopic-numeric":return 19;case"georgian":return 20;case"gujarati":return 21;case"gurmukhi":return 22;case"hebrew":return 22;case"hiragana":return 23;case"hiragana-iroha":return 24;case"japanese-formal":return 25;case"japanese-informal":return 26;case"kannada":return 27;case"katakana":return 28;case"katakana-iroha":return 29;case"khmer":return 30;case"korean-hangul-formal":return 31;case"korean-hanja-formal":return 32;case"korean-hanja-informal":return 33;case"lao":return 34;case"lower-armenian":return 35;case"malayalam":return 36;case"mongolian":return 37;case"myanmar":return 38;case"oriya":return 39;case"persian":return 40;case"simp-chinese-formal":return 41;case"simp-chinese-informal":return 42;case"tamil":return 43;case"telugu":return 44;case"thai":return 45;case"tibetan":return 46;case"trad-chinese-formal":return 47;case"trad-chinese-informal":return 48;case"upper-armenian":return 49;case"disclosure-open":return 50;case"disclosure-closed":return 51;case"none":default:return-1}}},$i=function(i){return{name:"margin-"+i,initialValue:"0",prefix:!1,type:4}},Jn=$i("top"),Wr=$i("right"),is=$i("bottom"),dc=$i("left"),Bc={name:"overflow",initialValue:"visible",prefix:!1,type:1,parse:function(i,e){return e.filter(vA).map(function(o){switch(o.value){case"hidden":return 1;case"scroll":return 2;case"clip":return 3;case"auto":return 4;case"visible":default:return 0}})}},gc={name:"overflow-wrap",initialValue:"normal",prefix:!1,type:2,parse:function(i,e){switch(e){case"break-word":return"break-word";case"normal":default:return"normal"}}},Xr=function(i){return{name:"padding-"+i,initialValue:"0",prefix:!1,type:3,format:"length-percentage"}},pc=Xr("top"),wc=Xr("right"),mc=Xr("bottom"),Cc=Xr("left"),vc={name:"text-align",initialValue:"left",prefix:!1,type:2,parse:function(i,e){switch(e){case"right":return 2;case"center":case"justify":return 1;case"left":default:return 0}}},Qc={name:"position",initialValue:"static",prefix:!1,type:2,parse:function(i,e){switch(e){case"relative":return 1;case"absolute":return 2;case"fixed":return 3;case"sticky":return 4}return 0}},_c={name:"text-shadow",initialValue:"none",type:1,prefix:!1,parse:function(i,e){return e.length===1&&Gi(e[0],"none")?[]:$A(e).map(function(o){for(var n={color:_t.TRANSPARENT,offsetX:ZA,offsetY:ZA,blur:ZA},a=0,l=0;l<o.length;l++){var u=o[l];YA(u)?(a===0?n.offsetX=u:a===1?n.offsetY=u:n.blur=u,a++):n.color=qt.parse(i,u)}return n})}},Uc={name:"text-transform",initialValue:"none",prefix:!1,type:2,parse:function(i,e){switch(e){case"uppercase":return 2;case"lowercase":return 1;case"capitalize":return 3}return 0}},Fc={name:"transform",initialValue:"none",prefix:!0,type:0,parse:function(i,e){if(e.type===20&&e.value==="none")return null;if(e.type===18){var o=Ic[e.name];if(typeof o>"u")throw new Error('Attempting to parse an unsupported transform function "'+e.name+'"');return o(e.values)}return null}},yc=function(i){var e=i.filter(function(o){return o.type===17}).map(function(o){return o.number});return e.length===6?e:null},Ec=function(i){var e=i.filter(function(f){return f.type===17}).map(function(f){return f.number}),o=e[0],n=e[1];e[2],e[3];var a=e[4],l=e[5];e[6],e[7],e[8],e[9],e[10],e[11];var u=e[12],d=e[13];return e[14],e[15],e.length===16?[o,n,a,l,u,d]:null},Ic={matrix:yc,matrix3d:Ec},Ls={type:16,number:50,flags:je},xc=[Ls,Ls],Hc={name:"transform-origin",initialValue:"50% 50%",prefix:!0,type:1,parse:function(i,e){var o=e.filter(DA);return o.length!==2?xc:[o[0],o[1]]}},Lc={name:"visible",initialValue:"none",prefix:!1,type:2,parse:function(i,e){switch(e){case"hidden":return 1;case"collapse":return 2;case"visible":default:return 0}}},Jr=function(i){return i.NORMAL="normal",i.BREAK_ALL="break-all",i.KEEP_ALL="keep-all",i}(Jr||{}),bc={name:"word-break",initialValue:"normal",prefix:!1,type:2,parse:function(i,e){switch(e){case"break-all":return Jr.BREAK_ALL;case"keep-all":return Jr.KEEP_ALL;case"normal":default:return Jr.NORMAL}}},Mc={name:"z-index",initialValue:"auto",prefix:!1,type:0,parse:function(i,e){if(e.type===20)return{auto:!0,order:0};if(Jt(e))return{auto:!1,order:e.number};throw new Error("Invalid z-index number parsed")}},bs={name:"time",parse:function(i,e){if(e.type===15)switch(e.unit.toLowerCase()){case"s":return 1e3*e.number;case"ms":return e.number}throw new Error("Unsupported time type")}},Tc={name:"opacity",initialValue:"1",type:0,prefix:!1,parse:function(i,e){return Jt(e)?e.number:1}},Pc={name:"text-decoration-color",initialValue:"transparent",prefix:!1,type:3,format:"color"},Sc={name:"text-decoration-line",initialValue:"none",prefix:!1,type:1,parse:function(i,e){return e.filter(vA).map(function(o){switch(o.value){case"underline":return 1;case"overline":return 2;case"line-through":return 3;case"none":return 4}return 0}).filter(function(o){return o!==0})}},Oc={name:"font-family",initialValue:"",prefix:!1,type:1,parse:function(i,e){var o=[],n=[];return e.forEach(function(a){switch(a.type){case 20:case 0:o.push(a.value);break;case 17:o.push(a.number.toString());break;case 4:n.push(o.join(" ")),o.length=0;break}}),o.length&&n.push(o.join(" ")),n.map(function(a){return a.indexOf(" ")===-1?a:"'"+a+"'"})}},Dc={name:"font-size",initialValue:"0",prefix:!1,type:3,format:"length"},Kc={name:"font-weight",initialValue:"normal",type:0,prefix:!1,parse:function(i,e){if(Jt(e))return e.number;if(vA(e))switch(e.value){case"bold":return 700;case"normal":default:return 400}return 400}},kc={name:"font-variant",initialValue:"none",type:1,prefix:!1,parse:function(i,e){return e.filter(vA).map(function(o){return o.value})}},Nc={name:"font-style",initialValue:"normal",prefix:!1,type:2,parse:function(i,e){switch(e){case"oblique":return"oblique";case"italic":return"italic";case"normal":default:return"normal"}}},WA=function(i,e){return(i&e)!==0},Rc={name:"content",initialValue:"none",type:1,prefix:!1,parse:function(i,e){if(e.length===0)return[];var o=e[0];return o.type===20&&o.value==="none"?[]:e}},Gc={name:"counter-increment",initialValue:"none",prefix:!0,type:1,parse:function(i,e){if(e.length===0)return null;var o=e[0];if(o.type===20&&o.value==="none")return null;for(var n=[],a=e.filter(Er),l=0;l<a.length;l++){var u=a[l],d=a[l+1];if(u.type===20){var f=d&&Jt(d)?d.number:1;n.push({counter:u.value,increment:f})}}return n}},Vc={name:"counter-reset",initialValue:"none",prefix:!0,type:1,parse:function(i,e){if(e.length===0)return[];for(var o=[],n=e.filter(Er),a=0;a<n.length;a++){var l=n[a],u=n[a+1];if(vA(l)&&l.value!=="none"){var d=u&&Jt(u)?u.number:0;o.push({counter:l.value,reset:d})}}return o}},Zc={name:"duration",initialValue:"0s",prefix:!1,type:1,parse:function(i,e){return e.filter(le).map(function(o){return bs.parse(i,o)})}},zc={name:"quotes",initialValue:"none",prefix:!0,type:1,parse:function(i,e){if(e.length===0)return null;var o=e[0];if(o.type===20&&o.value==="none")return null;var n=[],a=e.filter(Go);if(a.length%2!==0)return null;for(var l=0;l<a.length;l+=2){var u=a[l].value,d=a[l+1].value;n.push({open:u,close:d})}return n}},Ms=function(i,e,o){if(!i)return"";var n=i[Math.min(e,i.length-1)];return n?o?n.open:n.close:""},Wc={name:"box-shadow",initialValue:"none",type:1,prefix:!1,parse:function(i,e){return e.length===1&&Gi(e[0],"none")?[]:$A(e).map(function(o){for(var n={color:255,offsetX:ZA,offsetY:ZA,blur:ZA,spread:ZA,inset:!1},a=0,l=0;l<o.length;l++){var u=o[l];Gi(u,"inset")?n.inset=!0:YA(u)?(a===0?n.offsetX=u:a===1?n.offsetY=u:a===2?n.blur=u:n.spread=u,a++):n.color=qt.parse(i,u)}return n})}},Xc={name:"paint-order",initialValue:"normal",prefix:!1,type:1,parse:function(i,e){var o=[0,1,2],n=[];return e.filter(vA).forEach(function(a){switch(a.value){case"stroke":n.push(1);break;case"fill":n.push(0);break;case"markers":n.push(2);break}}),o.forEach(function(a){n.indexOf(a)===-1&&n.push(a)}),n}},Jc={name:"-webkit-text-stroke-color",initialValue:"currentcolor",prefix:!1,type:3,format:"color"},Yc={name:"-webkit-text-stroke-width",initialValue:"0",type:0,prefix:!1,parse:function(i,e){return le(e)?e.number:0}},qc=function(){function i(e,o){var n,a;this.animationDuration=k(e,Zc,o.animationDuration),this.backgroundClip=k(e,Hr,o.backgroundClip),this.backgroundColor=k(e,Zo,o.backgroundColor),this.backgroundImage=k(e,ui,o.backgroundImage),this.backgroundOrigin=k(e,Kr,o.backgroundOrigin),this.backgroundPosition=k(e,kr,o.backgroundPosition),this.backgroundRepeat=k(e,Jo,o.backgroundRepeat),this.backgroundSize=k(e,Rr,o.backgroundSize),this.borderTopColor=k(e,Yo,o.borderTopColor),this.borderRightColor=k(e,Vr,o.borderRightColor),this.borderBottomColor=k(e,Zr,o.borderBottomColor),this.borderLeftColor=k(e,qo,o.borderLeftColor),this.borderTopLeftRadius=k(e,t,o.borderTopLeftRadius),this.borderTopRightRadius=k(e,r,o.borderTopRightRadius),this.borderBottomRightRadius=k(e,s,o.borderBottomRightRadius),this.borderBottomLeftRadius=k(e,c,o.borderBottomLeftRadius),this.borderTopStyle=k(e,p,o.borderTopStyle),this.borderRightStyle=k(e,U,o.borderRightStyle),this.borderBottomStyle=k(e,y,o.borderBottomStyle),this.borderLeftStyle=k(e,H,o.borderLeftStyle),this.borderTopWidth=k(e,q,o.borderTopWidth),this.borderRightWidth=k(e,aA,o.borderRightWidth),this.borderBottomWidth=k(e,jA,o.borderBottomWidth),this.borderLeftWidth=k(e,NA,o.borderLeftWidth),this.boxShadow=k(e,Wc,o.boxShadow),this.color=k(e,gt,o.color),this.direction=k(e,st,o.direction),this.display=k(e,Ke,o.display),this.float=k(e,jo,o.cssFloat),this.fontFamily=k(e,Oc,o.fontFamily),this.fontSize=k(e,Dc,o.fontSize),this.fontStyle=k(e,Nc,o.fontStyle),this.fontVariant=k(e,kc,o.fontVariant),this.fontWeight=k(e,Kc,o.fontWeight),this.letterSpacing=k(e,As,o.letterSpacing),this.lineBreak=k(e,ts,o.lineBreak),this.lineHeight=k(e,zr,o.lineHeight),this.listStyleImage=k(e,qi,o.listStyleImage),this.listStylePosition=k(e,es,o.listStylePosition),this.listStyleType=k(e,Xn,o.listStyleType),this.marginTop=k(e,Jn,o.marginTop),this.marginRight=k(e,Wr,o.marginRight),this.marginBottom=k(e,is,o.marginBottom),this.marginLeft=k(e,dc,o.marginLeft),this.opacity=k(e,Tc,o.opacity);var l=k(e,Bc,o.overflow);this.overflowX=l[0],this.overflowY=l[l.length>1?1:0],this.overflowWrap=k(e,gc,o.overflowWrap),this.paddingTop=k(e,pc,o.paddingTop),this.paddingRight=k(e,wc,o.paddingRight),this.paddingBottom=k(e,mc,o.paddingBottom),this.paddingLeft=k(e,Cc,o.paddingLeft),this.paintOrder=k(e,Xc,o.paintOrder),this.position=k(e,Qc,o.position),this.textAlign=k(e,vc,o.textAlign),this.textDecorationColor=k(e,Pc,(n=o.textDecorationColor)!==null&&n!==void 0?n:o.color),this.textDecorationLine=k(e,Sc,(a=o.textDecorationLine)!==null&&a!==void 0?a:o.textDecoration),this.textShadow=k(e,_c,o.textShadow),this.textTransform=k(e,Uc,o.textTransform),this.transform=k(e,Fc,o.transform),this.transformOrigin=k(e,Hc,o.transformOrigin),this.visibility=k(e,Lc,o.visibility),this.webkitTextStrokeColor=k(e,Jc,o.webkitTextStrokeColor),this.webkitTextStrokeWidth=k(e,Yc,o.webkitTextStrokeWidth),this.wordBreak=k(e,bc,o.wordBreak),this.zIndex=k(e,Mc,o.zIndex)}return i.prototype.isVisible=function(){return this.display>0&&this.opacity>0&&this.visibility===0},i.prototype.isTransparent=function(){return qA(this.backgroundColor)},i.prototype.isTransformed=function(){return this.transform!==null},i.prototype.isPositioned=function(){return this.position!==0},i.prototype.isPositionedWithZIndex=function(){return this.isPositioned()&&!this.zIndex.auto},i.prototype.isFloating=function(){return this.float!==0},i.prototype.isInlineLevel=function(){return WA(this.display,4)||WA(this.display,33554432)||WA(this.display,268435456)||WA(this.display,536870912)||WA(this.display,67108864)||WA(this.display,134217728)},i}(),$c=function(){function i(e,o){this.content=k(e,Rc,o.content),this.quotes=k(e,zc,o.quotes)}return i}(),Ts=function(){function i(e,o){this.counterIncrement=k(e,Gc,o.counterIncrement),this.counterReset=k(e,Vc,o.counterReset)}return i}(),k=function(i,e,o){var n=new Vn,a=o!==null&&typeof o<"u"?o.toString():e.initialValue;n.write(a);var l=new yr(n.read());switch(e.type){case 2:var u=l.parseComponentValue();return e.parse(i,vA(u)?u.value:e.initialValue);case 0:return e.parse(i,l.parseComponentValue());case 1:return e.parse(i,l.parseComponentValues());case 4:return l.parseComponentValue();case 3:switch(e.format){case"angle":return Zi.parse(i,l.parseComponentValue());case"color":return qt.parse(i,l.parseComponentValue());case"image":return Be.parse(i,l.parseComponentValue());case"length":var d=l.parseComponentValue();return YA(d)?d:ZA;case"length-percentage":var f=l.parseComponentValue();return DA(f)?f:ZA;case"time":return bs.parse(i,l.parseComponentValue())}break}},jc="data-html2canvas-debug",Al=function(i){var e=i.getAttribute(jc);switch(e){case"all":return 1;case"clone":return 2;case"parse":return 3;case"render":return 4;default:return 0}},ns=function(i,e){var o=Al(i);return o===1||e===o},jt=function(){function i(e,o){if(this.context=e,this.textNodes=[],this.elements=[],this.flags=0,ns(o,3))debugger;this.styles=new qc(e,window.getComputedStyle(o,null)),ws(o)&&(this.styles.animationDuration.some(function(n){return n>0})&&(o.style.animationDuration="0s"),this.styles.transform!==null&&(o.style.transform="none")),this.bounds=T(this.context,o),ns(o,4)&&(this.flags|=16)}return i}(),tl="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",Ps="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",Yn=typeof Uint8Array>"u"?[]:new Uint8Array(256),Yr=0;Yr<Ps.length;Yr++)Yn[Ps.charCodeAt(Yr)]=Yr;for(var el=function(i){var e=i.length*.75,o=i.length,n,a=0,l,u,d,f;i[i.length-1]==="="&&(e--,i[i.length-2]==="="&&e--);var w=typeof ArrayBuffer<"u"&&typeof Uint8Array<"u"&&typeof Uint8Array.prototype.slice<"u"?new ArrayBuffer(e):new Array(e),m=Array.isArray(w)?w:new Uint8Array(w);for(n=0;n<o;n+=4)l=Yn[i.charCodeAt(n)],u=Yn[i.charCodeAt(n+1)],d=Yn[i.charCodeAt(n+2)],f=Yn[i.charCodeAt(n+3)],m[a++]=l<<2|u>>4,m[a++]=(u&15)<<4|d>>2,m[a++]=(d&3)<<6|f&63;return w},il=function(i){for(var e=i.length,o=[],n=0;n<e;n+=2)o.push(i[n+1]<<8|i[n]);return o},nl=function(i){for(var e=i.length,o=[],n=0;n<e;n+=4)o.push(i[n+3]<<24|i[n+2]<<16|i[n+1]<<8|i[n]);return o},fi=5,rs=11,os=2,rl=rs-fi,Ss=65536>>fi,ol=1<<fi,ss=ol-1,sl=1024>>fi,al=Ss+sl,cl=al,ll=32,ul=cl+ll,hl=65536>>rs,fl=1<<rl,dl=fl-1,Os=function(i,e,o){return i.slice?i.slice(e,o):new Uint16Array(Array.prototype.slice.call(i,e,o))},Bl=function(i,e,o){return i.slice?i.slice(e,o):new Uint32Array(Array.prototype.slice.call(i,e,o))},gl=function(i,e){var o=el(i),n=Array.isArray(o)?nl(o):new Uint32Array(o),a=Array.isArray(o)?il(o):new Uint16Array(o),l=24,u=Os(a,l/2,n[4]/2),d=n[5]===2?Os(a,(l+n[4])/2):Bl(n,Math.ceil((l+n[4])/4));return new pl(n[0],n[1],n[2],n[3],u,d)},pl=function(){function i(e,o,n,a,l,u){this.initialValue=e,this.errorValue=o,this.highStart=n,this.highValueIndex=a,this.index=l,this.data=u}return i.prototype.get=function(e){var o;if(e>=0){if(e<55296||e>56319&&e<=65535)return o=this.index[e>>fi],o=(o<<os)+(e&ss),this.data[o];if(e<=65535)return o=this.index[Ss+(e-55296>>fi)],o=(o<<os)+(e&ss),this.data[o];if(e<this.highStart)return o=ul-hl+(e>>rs),o=this.index[o],o+=e>>fi&dl,o=this.index[o],o=(o<<os)+(e&ss),this.data[o];if(e<=1114111)return this.data[this.highValueIndex]}return this.errorValue},i}(),Ds="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",wl=typeof Uint8Array>"u"?[]:new Uint8Array(256),qr=0;qr<Ds.length;qr++)wl[Ds.charCodeAt(qr)]=qr;var ml=1,as=2,cs=3,Ks=4,ks=5,Cl=7,Ns=8,ls=9,us=10,Rs=11,Gs=12,Vs=13,Zs=14,hs=15,vl=function(i){for(var e=[],o=0,n=i.length;o<n;){var a=i.charCodeAt(o++);if(a>=55296&&a<=56319&&o<n){var l=i.charCodeAt(o++);(l&64512)===56320?e.push(((a&1023)<<10)+(l&1023)+65536):(e.push(a),o--)}else e.push(a)}return e},Ql=function(){for(var i=[],e=0;e<arguments.length;e++)i[e]=arguments[e];if(String.fromCodePoint)return String.fromCodePoint.apply(String,i);var o=i.length;if(!o)return"";for(var n=[],a=-1,l="";++a<o;){var u=i[a];u<=65535?n.push(u):(u-=65536,n.push((u>>10)+55296,u%1024+56320)),(a+1===o||n.length>16384)&&(l+=String.fromCharCode.apply(String,n),n.length=0)}return l},_l=gl(tl),Ht="\xD7",fs="\xF7",Ul=function(i){return _l.get(i)},Fl=function(i,e,o){var n=o-2,a=e[n],l=e[o-1],u=e[o];if(l===as&&u===cs)return Ht;if(l===as||l===cs||l===Ks||u===as||u===cs||u===Ks)return fs;if(l===Ns&&[Ns,ls,Rs,Gs].indexOf(u)!==-1||(l===Rs||l===ls)&&(u===ls||u===us)||(l===Gs||l===us)&&u===us||u===Vs||u===ks||u===Cl||l===ml)return Ht;if(l===Vs&&u===Zs){for(;a===ks;)a=e[--n];if(a===Zs)return Ht}if(l===hs&&u===hs){for(var d=0;a===hs;)d++,a=e[--n];if(d%2===0)return Ht}return fs},yl=function(i){var e=vl(i),o=e.length,n=0,a=0,l=e.map(Ul);return{next:function(){if(n>=o)return{done:!0,value:null};for(var u=Ht;n<o&&(u=Fl(e,l,++n))===Ht;);if(u!==Ht||n===o){var d=Ql.apply(null,e.slice(a,n));return a=n,{value:d,done:!1}}return{done:!0,value:null}}}},El=function(i){for(var e=yl(i),o=[],n;!(n=e.next()).done;)n.value&&o.push(n.value.slice());return o},Il=function(i){var e=123;if(i.createRange){var o=i.createRange();if(o.getBoundingClientRect){var n=i.createElement("boundtest");n.style.height=e+"px",n.style.display="block",i.body.appendChild(n),o.selectNode(n);var a=o.getBoundingClientRect(),l=Math.round(a.height);if(i.body.removeChild(n),l===e)return!0}}return!1},xl=function(i){var e=i.createElement("boundtest");e.style.width="50px",e.style.display="block",e.style.fontSize="12px",e.style.letterSpacing="0px",e.style.wordSpacing="0px",i.body.appendChild(e);var o=i.createRange();e.innerHTML=typeof"".repeat=="function"?"&#128104;".repeat(10):"";var n=e.firstChild,a=b(n.data).map(function(f){return F(f)}),l=0,u={},d=a.every(function(f,w){o.setStart(n,l),o.setEnd(n,l+f.length);var m=o.getBoundingClientRect();l+=f.length;var _=m.x>u.x||m.y>u.y;return u=m,w===0?!0:_});return i.body.removeChild(e),d},Hl=function(){return typeof new Image().crossOrigin<"u"},Ll=function(){return typeof new XMLHttpRequest().responseType=="string"},bl=function(i){var e=new Image,o=i.createElement("canvas"),n=o.getContext("2d");if(!n)return!1;e.src="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg'></svg>";try{n.drawImage(e,0,0),o.toDataURL()}catch{return!1}return!0},zs=function(i){return i[0]===0&&i[1]===255&&i[2]===0&&i[3]===255},Ml=function(i){var e=i.createElement("canvas"),o=100;e.width=o,e.height=o;var n=e.getContext("2d");if(!n)return Promise.reject(!1);n.fillStyle="rgb(0, 255, 0)",n.fillRect(0,0,o,o);var a=new Image,l=e.toDataURL();a.src=l;var u=ds(o,o,0,0,a);return n.fillStyle="red",n.fillRect(0,0,o,o),Ws(u).then(function(d){n.drawImage(d,0,0);var f=n.getImageData(0,0,o,o).data;n.fillStyle="red",n.fillRect(0,0,o,o);var w=i.createElement("div");return w.style.backgroundImage="url("+l+")",w.style.height=o+"px",zs(f)?Ws(ds(o,o,0,0,w)):Promise.reject(!1)}).then(function(d){return n.drawImage(d,0,0),zs(n.getImageData(0,0,o,o).data)}).catch(function(){return!1})},ds=function(i,e,o,n,a){var l="http://www.w3.org/2000/svg",u=document.createElementNS(l,"svg"),d=document.createElementNS(l,"foreignObject");return u.setAttributeNS(null,"width",i.toString()),u.setAttributeNS(null,"height",e.toString()),d.setAttributeNS(null,"width","100%"),d.setAttributeNS(null,"height","100%"),d.setAttributeNS(null,"x",o.toString()),d.setAttributeNS(null,"y",n.toString()),d.setAttributeNS(null,"externalResourcesRequired","true"),u.appendChild(d),d.appendChild(a),u},Ws=function(i){return new Promise(function(e,o){var n=new Image;n.onload=function(){return e(n)},n.onerror=o,n.src="data:image/svg+xml;charset=utf-8,"+encodeURIComponent(new XMLSerializer().serializeToString(i))})},At={get SUPPORT_RANGE_BOUNDS(){var i=Il(document);return Object.defineProperty(At,"SUPPORT_RANGE_BOUNDS",{value:i}),i},get SUPPORT_WORD_BREAKING(){var i=At.SUPPORT_RANGE_BOUNDS&&xl(document);return Object.defineProperty(At,"SUPPORT_WORD_BREAKING",{value:i}),i},get SUPPORT_SVG_DRAWING(){var i=bl(document);return Object.defineProperty(At,"SUPPORT_SVG_DRAWING",{value:i}),i},get SUPPORT_FOREIGNOBJECT_DRAWING(){var i=typeof Array.from=="function"&&typeof window.fetch=="function"?Ml(document):Promise.resolve(!1);return Object.defineProperty(At,"SUPPORT_FOREIGNOBJECT_DRAWING",{value:i}),i},get SUPPORT_CORS_IMAGES(){var i=Hl();return Object.defineProperty(At,"SUPPORT_CORS_IMAGES",{value:i}),i},get SUPPORT_RESPONSE_TYPE(){var i=Ll();return Object.defineProperty(At,"SUPPORT_RESPONSE_TYPE",{value:i}),i},get SUPPORT_CORS_XHR(){var i="withCredentials"in new XMLHttpRequest;return Object.defineProperty(At,"SUPPORT_CORS_XHR",{value:i}),i},get SUPPORT_NATIVE_TEXT_SEGMENTATION(){var i=!!(typeof Intl<"u"&&Intl.Segmenter);return Object.defineProperty(At,"SUPPORT_NATIVE_TEXT_SEGMENTATION",{value:i}),i}},qn=function(){function i(e,o){this.text=e,this.bounds=o}return i}(),Tl=function(i,e,o,n){var a=Ol(e,o),l=[],u=0;return a.forEach(function(d){if(o.textDecorationLine.length||d.trim().length>0)if(At.SUPPORT_RANGE_BOUNDS){var f=Xs(n,u,d.length).getClientRects();if(f.length>1){var w=Bs(d),m=0;w.forEach(function(E){l.push(new qn(E,C.fromDOMRectList(i,Xs(n,m+u,E.length).getClientRects()))),m+=E.length})}else l.push(new qn(d,C.fromDOMRectList(i,f)))}else{var _=n.splitText(d.length);l.push(new qn(d,Pl(i,n))),n=_}else At.SUPPORT_RANGE_BOUNDS||(n=n.splitText(d.length));u+=d.length}),l},Pl=function(i,e){var o=e.ownerDocument;if(o){var n=o.createElement("html2canvaswrapper");n.appendChild(e.cloneNode(!0));var a=e.parentNode;if(a){a.replaceChild(n,e);var l=T(i,n);return n.firstChild&&a.replaceChild(n.firstChild,n),l}}return C.EMPTY},Xs=function(i,e,o){var n=i.ownerDocument;if(!n)throw new Error("Node has no owner document");var a=n.createRange();return a.setStart(i,e),a.setEnd(i,e+o),a},Bs=function(i){if(At.SUPPORT_NATIVE_TEXT_SEGMENTATION){var e=new Intl.Segmenter(void 0,{granularity:"grapheme"});return Array.from(e.segment(i)).map(function(o){return o.segment})}return El(i)},Sl=function(i,e){if(At.SUPPORT_NATIVE_TEXT_SEGMENTATION){var o=new Intl.Segmenter(void 0,{granularity:"word"});return Array.from(o.segment(i)).map(function(n){return n.segment})}return Kl(i,e)},Ol=function(i,e){return e.letterSpacing!==0?Bs(i):Sl(i,e)},Dl=[32,160,4961,65792,65793,4153,4241],Kl=function(i,e){for(var o=bi(i,{lineBreak:e.lineBreak,wordBreak:e.overflowWrap==="break-word"?"break-word":e.wordBreak}),n=[],a,l=function(){if(a.value){var u=a.value.slice(),d=b(u),f="";d.forEach(function(w){Dl.indexOf(w)===-1?f+=F(w):(f.length&&n.push(f),n.push(F(w)),f="")}),f.length&&n.push(f)}};!(a=o.next()).done;)l();return n},kl=function(){function i(e,o,n){this.text=Nl(o.data,n.textTransform),this.textBounds=Tl(e,this.text,n,o)}return i}(),Nl=function(i,e){switch(e){case 1:return i.toLowerCase();case 3:return i.replace(Rl,Gl);case 2:return i.toUpperCase();default:return i}},Rl=/(^|\s|:|-|\(|\))([a-z])/g,Gl=function(i,e,o){return i.length>0?e+o.toUpperCase():i},Js=function(i){N(e,i);function e(o,n){var a=i.call(this,o,n)||this;return a.src=n.currentSrc||n.src,a.intrinsicWidth=n.naturalWidth,a.intrinsicHeight=n.naturalHeight,a.context.cache.addImage(a.src),a}return e}(jt),Ys=function(i){N(e,i);function e(o,n){var a=i.call(this,o,n)||this;return a.canvas=n,a.intrinsicWidth=n.width,a.intrinsicHeight=n.height,a}return e}(jt),qs=function(i){N(e,i);function e(o,n){var a=i.call(this,o,n)||this,l=new XMLSerializer,u=T(o,n);return n.setAttribute("width",u.width+"px"),n.setAttribute("height",u.height+"px"),a.svg="data:image/svg+xml,"+encodeURIComponent(l.serializeToString(n)),a.intrinsicWidth=n.width.baseVal.value,a.intrinsicHeight=n.height.baseVal.value,a.context.cache.addImage(a.svg),a}return e}(jt),$s=function(i){N(e,i);function e(o,n){var a=i.call(this,o,n)||this;return a.value=n.value,a}return e}(jt),gs=function(i){N(e,i);function e(o,n){var a=i.call(this,o,n)||this;return a.start=n.start,a.reversed=typeof n.reversed=="boolean"&&n.reversed===!0,a}return e}(jt),Vl=[{type:15,flags:0,unit:"px",number:3}],Zl=[{type:16,flags:0,number:50}],zl=function(i){return i.width>i.height?new C(i.left+(i.width-i.height)/2,i.top,i.height,i.height):i.width<i.height?new C(i.left,i.top+(i.height-i.width)/2,i.width,i.width):i},Wl=function(i){var e=i.type===Xl?new Array(i.value.length+1).join("\u2022"):i.value;return e.length===0?i.placeholder||"":e},$r="checkbox",jr="radio",Xl="password",js=707406591,ps=function(i){N(e,i);function e(o,n){var a=i.call(this,o,n)||this;switch(a.type=n.type.toLowerCase(),a.checked=n.checked,a.value=Wl(n),(a.type===$r||a.type===jr)&&(a.styles.backgroundColor=3739148031,a.styles.borderTopColor=a.styles.borderRightColor=a.styles.borderBottomColor=a.styles.borderLeftColor=2779096575,a.styles.borderTopWidth=a.styles.borderRightWidth=a.styles.borderBottomWidth=a.styles.borderLeftWidth=1,a.styles.borderTopStyle=a.styles.borderRightStyle=a.styles.borderBottomStyle=a.styles.borderLeftStyle=1,a.styles.backgroundClip=[0],a.styles.backgroundOrigin=[0],a.bounds=zl(a.bounds)),a.type){case $r:a.styles.borderTopRightRadius=a.styles.borderTopLeftRadius=a.styles.borderBottomRightRadius=a.styles.borderBottomLeftRadius=Vl;break;case jr:a.styles.borderTopRightRadius=a.styles.borderTopLeftRadius=a.styles.borderBottomRightRadius=a.styles.borderBottomLeftRadius=Zl;break}return a}return e}(jt),Aa=function(i){N(e,i);function e(o,n){var a=i.call(this,o,n)||this,l=n.options[n.selectedIndex||0];return a.value=l&&l.text||"",a}return e}(jt),ta=function(i){N(e,i);function e(o,n){var a=i.call(this,o,n)||this;return a.value=n.value,a}return e}(jt),ea=function(i){N(e,i);function e(o,n){var a=i.call(this,o,n)||this;a.src=n.src,a.width=parseInt(n.width,10)||0,a.height=parseInt(n.height,10)||0,a.backgroundColor=a.styles.backgroundColor;try{if(n.contentWindow&&n.contentWindow.document&&n.contentWindow.document.documentElement){a.tree=na(o,n.contentWindow.document.documentElement);var l=n.contentWindow.document.documentElement?$t(o,getComputedStyle(n.contentWindow.document.documentElement).backgroundColor):_t.TRANSPARENT,u=n.contentWindow.document.body?$t(o,getComputedStyle(n.contentWindow.document.body).backgroundColor):_t.TRANSPARENT;a.backgroundColor=qA(l)?qA(u)?a.styles.backgroundColor:u:l}}catch{}return a}return e}(jt),Jl=["OL","UL","MENU"],Ao=function(i,e,o,n){for(var a=e.firstChild,l=void 0;a;a=l)if(l=a.nextSibling,ra(a)&&a.data.trim().length>0)o.textNodes.push(new kl(i,a,o.styles));else if(ji(a))if(ua(a)&&a.assignedNodes)a.assignedNodes().forEach(function(d){return Ao(i,d,o,n)});else{var u=ia(i,a);u.styles.isVisible()&&(Yl(a,u,n)?u.flags|=4:ql(u.styles)&&(u.flags|=2),Jl.indexOf(a.tagName)!==-1&&(u.flags|=8),o.elements.push(u),a.slot,a.shadowRoot?Ao(i,a.shadowRoot,u,n):!eo(a)&&!oa(a)&&!io(a)&&Ao(i,a,u,n))}},ia=function(i,e){return Cs(e)?new Js(i,e):sa(e)?new Ys(i,e):oa(e)?new qs(i,e):$l(e)?new $s(i,e):jl(e)?new gs(i,e):Au(e)?new ps(i,e):io(e)?new Aa(i,e):eo(e)?new ta(i,e):ca(e)?new ea(i,e):new jt(i,e)},na=function(i,e){var o=ia(i,e);return o.flags|=4,Ao(i,e,o,o),o},Yl=function(i,e,o){return e.styles.isPositionedWithZIndex()||e.styles.opacity<1||e.styles.isTransformed()||ms(i)&&o.styles.isTransparent()},ql=function(i){return i.isPositioned()||i.isFloating()},ra=function(i){return i.nodeType===Node.TEXT_NODE},ji=function(i){return i.nodeType===Node.ELEMENT_NODE},ws=function(i){return ji(i)&&typeof i.style<"u"&&!to(i)},to=function(i){return typeof i.className=="object"},$l=function(i){return i.tagName==="LI"},jl=function(i){return i.tagName==="OL"},Au=function(i){return i.tagName==="INPUT"},tu=function(i){return i.tagName==="HTML"},oa=function(i){return i.tagName==="svg"},ms=function(i){return i.tagName==="BODY"},sa=function(i){return i.tagName==="CANVAS"},aa=function(i){return i.tagName==="VIDEO"},Cs=function(i){return i.tagName==="IMG"},ca=function(i){return i.tagName==="IFRAME"},la=function(i){return i.tagName==="STYLE"},eu=function(i){return i.tagName==="SCRIPT"},eo=function(i){return i.tagName==="TEXTAREA"},io=function(i){return i.tagName==="SELECT"},ua=function(i){return i.tagName==="SLOT"},ha=function(i){return i.tagName.indexOf("-")>0},iu=function(){function i(){this.counters={}}return i.prototype.getCounterValue=function(e){var o=this.counters[e];return o&&o.length?o[o.length-1]:1},i.prototype.getCounterValues=function(e){var o=this.counters[e];return o||[]},i.prototype.pop=function(e){var o=this;e.forEach(function(n){return o.counters[n].pop()})},i.prototype.parse=function(e){var o=this,n=e.counterIncrement,a=e.counterReset,l=!0;n!==null&&n.forEach(function(d){var f=o.counters[d.counter];f&&d.increment!==0&&(l=!1,f.length||f.push(1),f[Math.max(0,f.length-1)]+=d.increment)});var u=[];return l&&a.forEach(function(d){var f=o.counters[d.counter];u.push(d.counter),f||(f=o.counters[d.counter]=[]),f.push(d.reset)}),u},i}(),fa={integers:[1e3,900,500,400,100,90,50,40,10,9,5,4,1],values:["M","CM","D","CD","C","XC","L","XL","X","IX","V","IV","I"]},da={integers:[9e3,8e3,7e3,6e3,5e3,4e3,3e3,2e3,1e3,900,800,700,600,500,400,300,200,100,90,80,70,60,50,40,30,20,10,9,8,7,6,5,4,3,2,1],values:["\u0554","\u0553","\u0552","\u0551","\u0550","\u054F","\u054E","\u054D","\u054C","\u054B","\u054A","\u0549","\u0548","\u0547","\u0546","\u0545","\u0544","\u0543","\u0542","\u0541","\u0540","\u053F","\u053E","\u053D","\u053C","\u053B","\u053A","\u0539","\u0538","\u0537","\u0536","\u0535","\u0534","\u0533","\u0532","\u0531"]},nu={integers:[1e4,9e3,8e3,7e3,6e3,5e3,4e3,3e3,2e3,1e3,400,300,200,100,90,80,70,60,50,40,30,20,19,18,17,16,15,10,9,8,7,6,5,4,3,2,1],values:["\u05D9\u05F3","\u05D8\u05F3","\u05D7\u05F3","\u05D6\u05F3","\u05D5\u05F3","\u05D4\u05F3","\u05D3\u05F3","\u05D2\u05F3","\u05D1\u05F3","\u05D0\u05F3","\u05EA","\u05E9","\u05E8","\u05E7","\u05E6","\u05E4","\u05E2","\u05E1","\u05E0","\u05DE","\u05DC","\u05DB","\u05D9\u05D8","\u05D9\u05D7","\u05D9\u05D6","\u05D8\u05D6","\u05D8\u05D5","\u05D9","\u05D8","\u05D7","\u05D6","\u05D5","\u05D4","\u05D3","\u05D2","\u05D1","\u05D0"]},ru={integers:[1e4,9e3,8e3,7e3,6e3,5e3,4e3,3e3,2e3,1e3,900,800,700,600,500,400,300,200,100,90,80,70,60,50,40,30,20,10,9,8,7,6,5,4,3,2,1],values:["\u10F5","\u10F0","\u10EF","\u10F4","\u10EE","\u10ED","\u10EC","\u10EB","\u10EA","\u10E9","\u10E8","\u10E7","\u10E6","\u10E5","\u10E4","\u10F3","\u10E2","\u10E1","\u10E0","\u10DF","\u10DE","\u10DD","\u10F2","\u10DC","\u10DB","\u10DA","\u10D9","\u10D8","\u10D7","\u10F1","\u10D6","\u10D5","\u10D4","\u10D3","\u10D2","\u10D1","\u10D0"]},An=function(i,e,o,n,a,l){return i<e||i>o?jn(i,a,l.length>0):n.integers.reduce(function(u,d,f){for(;i>=d;)i-=d,u+=n.values[f];return u},"")+l},Ba=function(i,e,o,n){var a="";do o||i--,a=n(i)+a,i/=e;while(i*e>=e);return a},kA=function(i,e,o,n,a){var l=o-e+1;return(i<0?"-":"")+(Ba(Math.abs(i),l,n,function(u){return F(Math.floor(u%l)+e)})+a)},di=function(i,e,o){o===void 0&&(o=". ");var n=e.length;return Ba(Math.abs(i),n,!1,function(a){return e[Math.floor(a%n)]})+o},tn=1,ke=2,Ne=4,$n=8,ge=function(i,e,o,n,a,l){if(i<-9999||i>9999)return jn(i,4,a.length>0);var u=Math.abs(i),d=a;if(u===0)return e[0]+d;for(var f=0;u>0&&f<=4;f++){var w=u%10;w===0&&WA(l,tn)&&d!==""?d=e[w]+d:w>1||w===1&&f===0||w===1&&f===1&&WA(l,ke)||w===1&&f===1&&WA(l,Ne)&&i>100||w===1&&f>1&&WA(l,$n)?d=e[w]+(f>0?o[f-1]:"")+d:w===1&&f>0&&(d=o[f-1]+d),u=Math.floor(u/10)}return(i<0?n:"")+d},ga="\u5341\u767E\u5343\u842C",pa="\u62FE\u4F70\u4EDF\u842C",wa="\u30DE\u30A4\u30CA\u30B9",vs="\uB9C8\uC774\uB108\uC2A4",jn=function(i,e,o){var n=o?". ":"",a=o?"\u3001":"",l=o?", ":"",u=o?" ":"";switch(e){case 0:return"\u2022"+u;case 1:return"\u25E6"+u;case 2:return"\u25FE"+u;case 5:var d=kA(i,48,57,!0,n);return d.length<4?"0"+d:d;case 4:return di(i,"\u3007\u4E00\u4E8C\u4E09\u56DB\u4E94\u516D\u4E03\u516B\u4E5D",a);case 6:return An(i,1,3999,fa,3,n).toLowerCase();case 7:return An(i,1,3999,fa,3,n);case 8:return kA(i,945,969,!1,n);case 9:return kA(i,97,122,!1,n);case 10:return kA(i,65,90,!1,n);case 11:return kA(i,1632,1641,!0,n);case 12:case 49:return An(i,1,9999,da,3,n);case 35:return An(i,1,9999,da,3,n).toLowerCase();case 13:return kA(i,2534,2543,!0,n);case 14:case 30:return kA(i,6112,6121,!0,n);case 15:return di(i,"\u5B50\u4E11\u5BC5\u536F\u8FB0\u5DF3\u5348\u672A\u7533\u9149\u620C\u4EA5",a);case 16:return di(i,"\u7532\u4E59\u4E19\u4E01\u620A\u5DF1\u5E9A\u8F9B\u58EC\u7678",a);case 17:case 48:return ge(i,"\u96F6\u4E00\u4E8C\u4E09\u56DB\u4E94\u516D\u4E03\u516B\u4E5D",ga,"\u8CA0",a,ke|Ne|$n);case 47:return ge(i,"\u96F6\u58F9\u8CB3\u53C3\u8086\u4F0D\u9678\u67D2\u634C\u7396",pa,"\u8CA0",a,tn|ke|Ne|$n);case 42:return ge(i,"\u96F6\u4E00\u4E8C\u4E09\u56DB\u4E94\u516D\u4E03\u516B\u4E5D",ga,"\u8D1F",a,ke|Ne|$n);case 41:return ge(i,"\u96F6\u58F9\u8D30\u53C1\u8086\u4F0D\u9646\u67D2\u634C\u7396",pa,"\u8D1F",a,tn|ke|Ne|$n);case 26:return ge(i,"\u3007\u4E00\u4E8C\u4E09\u56DB\u4E94\u516D\u4E03\u516B\u4E5D","\u5341\u767E\u5343\u4E07",wa,a,0);case 25:return ge(i,"\u96F6\u58F1\u5F10\u53C2\u56DB\u4F0D\u516D\u4E03\u516B\u4E5D","\u62FE\u767E\u5343\u4E07",wa,a,tn|ke|Ne);case 31:return ge(i,"\uC601\uC77C\uC774\uC0BC\uC0AC\uC624\uC721\uCE60\uD314\uAD6C","\uC2ED\uBC31\uCC9C\uB9CC",vs,l,tn|ke|Ne);case 33:return ge(i,"\u96F6\u4E00\u4E8C\u4E09\u56DB\u4E94\u516D\u4E03\u516B\u4E5D","\u5341\u767E\u5343\u842C",vs,l,0);case 32:return ge(i,"\u96F6\u58F9\u8CB3\u53C3\u56DB\u4E94\u516D\u4E03\u516B\u4E5D","\u62FE\u767E\u5343",vs,l,tn|ke|Ne);case 18:return kA(i,2406,2415,!0,n);case 20:return An(i,1,19999,ru,3,n);case 21:return kA(i,2790,2799,!0,n);case 22:return kA(i,2662,2671,!0,n);case 22:return An(i,1,10999,nu,3,n);case 23:return di(i,"\u3042\u3044\u3046\u3048\u304A\u304B\u304D\u304F\u3051\u3053\u3055\u3057\u3059\u305B\u305D\u305F\u3061\u3064\u3066\u3068\u306A\u306B\u306C\u306D\u306E\u306F\u3072\u3075\u3078\u307B\u307E\u307F\u3080\u3081\u3082\u3084\u3086\u3088\u3089\u308A\u308B\u308C\u308D\u308F\u3090\u3091\u3092\u3093");case 24:return di(i,"\u3044\u308D\u306F\u306B\u307B\u3078\u3068\u3061\u308A\u306C\u308B\u3092\u308F\u304B\u3088\u305F\u308C\u305D\u3064\u306D\u306A\u3089\u3080\u3046\u3090\u306E\u304A\u304F\u3084\u307E\u3051\u3075\u3053\u3048\u3066\u3042\u3055\u304D\u3086\u3081\u307F\u3057\u3091\u3072\u3082\u305B\u3059");case 27:return kA(i,3302,3311,!0,n);case 28:return di(i,"\u30A2\u30A4\u30A6\u30A8\u30AA\u30AB\u30AD\u30AF\u30B1\u30B3\u30B5\u30B7\u30B9\u30BB\u30BD\u30BF\u30C1\u30C4\u30C6\u30C8\u30CA\u30CB\u30CC\u30CD\u30CE\u30CF\u30D2\u30D5\u30D8\u30DB\u30DE\u30DF\u30E0\u30E1\u30E2\u30E4\u30E6\u30E8\u30E9\u30EA\u30EB\u30EC\u30ED\u30EF\u30F0\u30F1\u30F2\u30F3",a);case 29:return di(i,"\u30A4\u30ED\u30CF\u30CB\u30DB\u30D8\u30C8\u30C1\u30EA\u30CC\u30EB\u30F2\u30EF\u30AB\u30E8\u30BF\u30EC\u30BD\u30C4\u30CD\u30CA\u30E9\u30E0\u30A6\u30F0\u30CE\u30AA\u30AF\u30E4\u30DE\u30B1\u30D5\u30B3\u30A8\u30C6\u30A2\u30B5\u30AD\u30E6\u30E1\u30DF\u30B7\u30F1\u30D2\u30E2\u30BB\u30B9",a);case 34:return kA(i,3792,3801,!0,n);case 37:return kA(i,6160,6169,!0,n);case 38:return kA(i,4160,4169,!0,n);case 39:return kA(i,2918,2927,!0,n);case 40:return kA(i,1776,1785,!0,n);case 43:return kA(i,3046,3055,!0,n);case 44:return kA(i,3174,3183,!0,n);case 45:return kA(i,3664,3673,!0,n);case 46:return kA(i,3872,3881,!0,n);case 3:default:return kA(i,48,57,!0,n)}},ma="data-html2canvas-ignore",Ca=function(){function i(e,o,n){if(this.context=e,this.options=n,this.scrolledElements=[],this.referenceElement=o,this.counters=new iu,this.quoteDepth=0,!o.ownerDocument)throw new Error("Cloned element does not have an owner document");this.documentElement=this.cloneNode(o.ownerDocument.documentElement,!1)}return i.prototype.toIFrame=function(e,o){var n=this,a=ou(e,o);if(!a.contentWindow)return Promise.reject("Unable to find iframe window");var l=e.defaultView.pageXOffset,u=e.defaultView.pageYOffset,d=a.contentWindow,f=d.document,w=cu(a).then(function(){return B(n,void 0,void 0,function(){var m,_;return g(this,function(E){switch(E.label){case 0:return this.scrolledElements.forEach(fu),d&&(d.scrollTo(o.left,o.top),/(iPad|iPhone|iPod)/g.test(navigator.userAgent)&&(d.scrollY!==o.top||d.scrollX!==o.left)&&(this.context.logger.warn("Unable to restore scroll position for cloned document"),this.context.windowBounds=this.context.windowBounds.add(d.scrollX-o.left,d.scrollY-o.top,0,0))),m=this.options.onclone,_=this.clonedReferenceElement,typeof _>"u"?[2,Promise.reject("Error finding the "+this.referenceElement.nodeName+" in the cloned document")]:f.fonts&&f.fonts.ready?[4,f.fonts.ready]:[3,2];case 1:E.sent(),E.label=2;case 2:return/(AppleWebKit)/g.test(navigator.userAgent)?[4,au(f)]:[3,4];case 3:E.sent(),E.label=4;case 4:return typeof m=="function"?[2,Promise.resolve().then(function(){return m(f,_)}).then(function(){return a})]:[2,a]}})})});return f.open(),f.write(uu(document.doctype)+"<html></html>"),hu(this.referenceElement.ownerDocument,l,u),f.replaceChild(f.adoptNode(this.documentElement),f.documentElement),f.close(),w},i.prototype.createElementClone=function(e){if(ns(e,2))debugger;if(sa(e))return this.createCanvasClone(e);if(aa(e))return this.createVideoClone(e);if(la(e))return this.createStyleClone(e);var o=e.cloneNode(!1);return Cs(o)&&(Cs(e)&&e.currentSrc&&e.currentSrc!==e.src&&(o.src=e.currentSrc,o.srcset=""),o.loading==="lazy"&&(o.loading="eager")),ha(o)?this.createCustomElementClone(o):o},i.prototype.createCustomElementClone=function(e){var o=document.createElement("html2canvascustomelement");return Qs(e.style,o),o},i.prototype.createStyleClone=function(e){try{var o=e.sheet;if(o&&o.cssRules){var n=[].slice.call(o.cssRules,0).reduce(function(l,u){return u&&typeof u.cssText=="string"?l+u.cssText:l},""),a=e.cloneNode(!1);return a.textContent=n,a}}catch(l){if(this.context.logger.error("Unable to access cssRules property",l),l.name!=="SecurityError")throw l}return e.cloneNode(!1)},i.prototype.createCanvasClone=function(e){var o;if(this.options.inlineImages&&e.ownerDocument){var n=e.ownerDocument.createElement("img");try{return n.src=e.toDataURL(),n}catch{this.context.logger.info("Unable to inline canvas contents, canvas is tainted",e)}}var a=e.cloneNode(!1);try{a.width=e.width,a.height=e.height;var l=e.getContext("2d"),u=a.getContext("2d");if(u)if(!this.options.allowTaint&&l)u.putImageData(l.getImageData(0,0,e.width,e.height),0,0);else{var d=(o=e.getContext("webgl2"))!==null&&o!==void 0?o:e.getContext("webgl");if(d){var f=d.getContextAttributes();(f==null?void 0:f.preserveDrawingBuffer)===!1&&this.context.logger.warn("Unable to clone WebGL context as it has preserveDrawingBuffer=false",e)}u.drawImage(e,0,0)}return a}catch{this.context.logger.info("Unable to clone canvas as it is tainted",e)}return a},i.prototype.createVideoClone=function(e){var o=e.ownerDocument.createElement("canvas");o.width=e.offsetWidth,o.height=e.offsetHeight;var n=o.getContext("2d");try{return n&&(n.drawImage(e,0,0,o.width,o.height),this.options.allowTaint||n.getImageData(0,0,o.width,o.height)),o}catch{this.context.logger.info("Unable to clone video as it is tainted",e)}var a=e.ownerDocument.createElement("canvas");return a.width=e.offsetWidth,a.height=e.offsetHeight,a},i.prototype.appendChildNode=function(e,o,n){(!ji(o)||!eu(o)&&!o.hasAttribute(ma)&&(typeof this.options.ignoreElements!="function"||!this.options.ignoreElements(o)))&&(!this.options.copyStyles||!ji(o)||!la(o))&&e.appendChild(this.cloneNode(o,n))},i.prototype.cloneChildNodes=function(e,o,n){for(var a=this,l=e.shadowRoot?e.shadowRoot.firstChild:e.firstChild;l;l=l.nextSibling)if(ji(l)&&ua(l)&&typeof l.assignedNodes=="function"){var u=l.assignedNodes();u.length&&u.forEach(function(d){return a.appendChildNode(o,d,n)})}else this.appendChildNode(o,l,n)},i.prototype.cloneNode=function(e,o){if(ra(e))return document.createTextNode(e.data);if(!e.ownerDocument)return e.cloneNode(!1);var n=e.ownerDocument.defaultView;if(n&&ji(e)&&(ws(e)||to(e))){var a=this.createElementClone(e);a.style.transitionProperty="none";var l=n.getComputedStyle(e),u=n.getComputedStyle(e,":before"),d=n.getComputedStyle(e,":after");this.referenceElement===e&&ws(a)&&(this.clonedReferenceElement=a),ms(a)&&gu(a);var f=this.counters.parse(new Ts(this.context,l)),w=this.resolvePseudoContent(e,a,u,no.BEFORE);ha(e)&&(o=!0),aa(e)||this.cloneChildNodes(e,a,o),w&&a.insertBefore(w,a.firstChild);var m=this.resolvePseudoContent(e,a,d,no.AFTER);return m&&a.appendChild(m),this.counters.pop(f),(l&&(this.options.copyStyles||to(e))&&!ca(e)||o)&&Qs(l,a),(e.scrollTop!==0||e.scrollLeft!==0)&&this.scrolledElements.push([a,e.scrollLeft,e.scrollTop]),(eo(e)||io(e))&&(eo(a)||io(a))&&(a.value=e.value),a}return e.cloneNode(!1)},i.prototype.resolvePseudoContent=function(e,o,n,a){var l=this;if(n){var u=n.content,d=o.ownerDocument;if(!(!d||!u||u==="none"||u==="-moz-alt-content"||n.display==="none")){this.counters.parse(new Ts(this.context,n));var f=new $c(this.context,n),w=d.createElement("html2canvaspseudoelement");Qs(n,w),f.content.forEach(function(_){if(_.type===0)w.appendChild(d.createTextNode(_.value));else if(_.type===22){var E=d.createElement("img");E.src=_.value,E.style.opacity="1",w.appendChild(E)}else if(_.type===18){if(_.name==="attr"){var K=_.values.filter(vA);K.length&&w.appendChild(d.createTextNode(e.getAttribute(K[0].value)||""))}else if(_.name==="counter"){var M=_.values.filter(JA),S=M[0],sA=M[1];if(S&&vA(S)){var W=l.counters.getCounterValue(S.value),V=sA&&vA(sA)?Xn.parse(l.context,sA.value):3;w.appendChild(d.createTextNode(jn(W,V,!1)))}}else if(_.name==="counters"){var mA=_.values.filter(JA),S=mA[0],cA=mA[1],sA=mA[2];if(S&&vA(S)){var tA=l.counters.getCounterValues(S.value),D=sA&&vA(sA)?Xn.parse(l.context,sA.value):3,gA=cA&&cA.type===0?cA.value:"",pA=tA.map(function(pt){return jn(pt,D,!1)}).join(gA);w.appendChild(d.createTextNode(pA))}}}else if(_.type===20)switch(_.value){case"open-quote":w.appendChild(d.createTextNode(Ms(f.quotes,l.quoteDepth++,!0)));break;case"close-quote":w.appendChild(d.createTextNode(Ms(f.quotes,--l.quoteDepth,!1)));break;default:w.appendChild(d.createTextNode(_.value))}}),w.className=_s+" "+Us;var m=a===no.BEFORE?" "+_s:" "+Us;return to(o)?o.className.baseValue+=m:o.className+=m,w}}},i.destroy=function(e){return e.parentNode?(e.parentNode.removeChild(e),!0):!1},i}(),no=function(i){return i[i.BEFORE=0]="BEFORE",i[i.AFTER=1]="AFTER",i}(no||{}),ou=function(i,e){var o=i.createElement("iframe");return o.className="html2canvas-container",o.style.visibility="hidden",o.style.position="fixed",o.style.left="-10000px",o.style.top="0px",o.style.border="0",o.width=e.width.toString(),o.height=e.height.toString(),o.scrolling="no",o.setAttribute(ma,"true"),i.body.appendChild(o),o},su=function(i){return new Promise(function(e){if(i.complete){e();return}if(!i.src){e();return}i.onload=e,i.onerror=e})},au=function(i){return Promise.all([].slice.call(i.images,0).map(su))},cu=function(i){return new Promise(function(e,o){var n=i.contentWindow;if(!n)return o("No window assigned for iframe");var a=n.document;n.onload=i.onload=function(){n.onload=i.onload=null;var l=setInterval(function(){a.body.childNodes.length>0&&a.readyState==="complete"&&(clearInterval(l),e(i))},50)}})},lu=["all","d","content"],Qs=function(i,e){for(var o=i.length-1;o>=0;o--){var n=i.item(o);lu.indexOf(n)===-1&&e.style.setProperty(n,i.getPropertyValue(n))}return e},uu=function(i){var e="";return i&&(e+="<!DOCTYPE ",i.name&&(e+=i.name),i.internalSubset&&(e+=i.internalSubset),i.publicId&&(e+='"'+i.publicId+'"'),i.systemId&&(e+='"'+i.systemId+'"'),e+=">"),e},hu=function(i,e,o){i&&i.defaultView&&(e!==i.defaultView.pageXOffset||o!==i.defaultView.pageYOffset)&&i.defaultView.scrollTo(e,o)},fu=function(i){var e=i[0],o=i[1],n=i[2];e.scrollLeft=o,e.scrollTop=n},du=":before",Bu=":after",_s="___html2canvas___pseudoelement_before",Us="___html2canvas___pseudoelement_after",va=`{
    content: "" !important;
    display: none !important;
}`,gu=function(i){pu(i,"."+_s+du+va+`
         .`+Us+Bu+va)},pu=function(i,e){var o=i.ownerDocument;if(o){var n=o.createElement("style");n.textContent=e,i.appendChild(n)}},Qa=function(){function i(){}return i.getOrigin=function(e){var o=i._link;return o?(o.href=e,o.href=o.href,o.protocol+o.hostname+o.port):"about:blank"},i.isSameOrigin=function(e){return i.getOrigin(e)===i._origin},i.setContext=function(e){i._link=e.document.createElement("a"),i._origin=i.getOrigin(e.location.href)},i._origin="about:blank",i}(),wu=function(){function i(e,o){this.context=e,this._options=o,this._cache={}}return i.prototype.addImage=function(e){var o=Promise.resolve();return this.has(e)||(ys(e)||Qu(e))&&(this._cache[e]=this.loadImage(e)).catch(function(){}),o},i.prototype.match=function(e){return this._cache[e]},i.prototype.loadImage=function(e){return B(this,void 0,void 0,function(){var o,n,a,l,u=this;return g(this,function(d){switch(d.label){case 0:return o=Qa.isSameOrigin(e),n=!Fs(e)&&this._options.useCORS===!0&&At.SUPPORT_CORS_IMAGES&&!o,a=!Fs(e)&&!o&&!ys(e)&&typeof this._options.proxy=="string"&&At.SUPPORT_CORS_XHR&&!n,!o&&this._options.allowTaint===!1&&!Fs(e)&&!ys(e)&&!a&&!n?[2]:(l=e,a?[4,this.proxy(l)]:[3,2]);case 1:l=d.sent(),d.label=2;case 2:return this.context.logger.debug("Added image "+e.substring(0,256)),[4,new Promise(function(f,w){var m=new Image;m.onload=function(){return f(m)},m.onerror=w,(_u(l)||n)&&(m.crossOrigin="anonymous"),m.src=l,m.complete===!0&&setTimeout(function(){return f(m)},500),u._options.imageTimeout>0&&setTimeout(function(){return w("Timed out ("+u._options.imageTimeout+"ms) loading image")},u._options.imageTimeout)})];case 3:return[2,d.sent()]}})})},i.prototype.has=function(e){return typeof this._cache[e]<"u"},i.prototype.keys=function(){return Promise.resolve(Object.keys(this._cache))},i.prototype.proxy=function(e){var o=this,n=this._options.proxy;if(!n)throw new Error("No proxy defined");var a=e.substring(0,256);return new Promise(function(l,u){var d=At.SUPPORT_RESPONSE_TYPE?"blob":"text",f=new XMLHttpRequest;f.onload=function(){if(f.status===200)if(d==="text")l(f.response);else{var _=new FileReader;_.addEventListener("load",function(){return l(_.result)},!1),_.addEventListener("error",function(E){return u(E)},!1),_.readAsDataURL(f.response)}else u("Failed to proxy resource "+a+" with status code "+f.status)},f.onerror=u;var w=n.indexOf("?")>-1?"&":"?";if(f.open("GET",""+n+w+"url="+encodeURIComponent(e)+"&responseType="+d),d!=="text"&&f instanceof XMLHttpRequest&&(f.responseType=d),o._options.imageTimeout){var m=o._options.imageTimeout;f.timeout=m,f.ontimeout=function(){return u("Timed out ("+m+"ms) proxying "+a)}}f.send()})},i}(),mu=/^data:image\/svg\+xml/i,Cu=/^data:image\/.*;base64,/i,vu=/^data:image\/.*/i,Qu=function(i){return At.SUPPORT_SVG_DRAWING||!Uu(i)},Fs=function(i){return vu.test(i)},_u=function(i){return Cu.test(i)},ys=function(i){return i.substr(0,4)==="blob"},Uu=function(i){return i.substr(-3).toLowerCase()==="svg"||mu.test(i)},O=function(){function i(e,o){this.type=0,this.x=e,this.y=o}return i.prototype.add=function(e,o){return new i(this.x+e,this.y+o)},i}(),en=function(i,e,o){return new O(i.x+(e.x-i.x)*o,i.y+(e.y-i.y)*o)},ro=function(){function i(e,o,n,a){this.type=1,this.start=e,this.startControl=o,this.endControl=n,this.end=a}return i.prototype.subdivide=function(e,o){var n=en(this.start,this.startControl,e),a=en(this.startControl,this.endControl,e),l=en(this.endControl,this.end,e),u=en(n,a,e),d=en(a,l,e),f=en(u,d,e);return o?new i(this.start,n,u,f):new i(f,d,l,this.end)},i.prototype.add=function(e,o){return new i(this.start.add(e,o),this.startControl.add(e,o),this.endControl.add(e,o),this.end.add(e,o))},i.prototype.reverse=function(){return new i(this.end,this.endControl,this.startControl,this.start)},i}(),Lt=function(i){return i.type===1},Fu=function(){function i(e){var o=e.styles,n=e.bounds,a=Yt(o.borderTopLeftRadius,n.width,n.height),l=a[0],u=a[1],d=Yt(o.borderTopRightRadius,n.width,n.height),f=d[0],w=d[1],m=Yt(o.borderBottomRightRadius,n.width,n.height),_=m[0],E=m[1],K=Yt(o.borderBottomLeftRadius,n.width,n.height),M=K[0],S=K[1],sA=[];sA.push((l+f)/n.width),sA.push((M+_)/n.width),sA.push((u+S)/n.height),sA.push((w+E)/n.height);var W=Math.max.apply(Math,sA);W>1&&(l/=W,u/=W,f/=W,w/=W,_/=W,E/=W,M/=W,S/=W);var V=n.width-f,mA=n.height-E,cA=n.width-_,tA=n.height-S,D=o.borderTopWidth,gA=o.borderRightWidth,pA=o.borderBottomWidth,nA=o.borderLeftWidth,RA=UA(o.paddingTop,e.bounds.width),pt=UA(o.paddingRight,e.bounds.width),Ut=UA(o.paddingBottom,e.bounds.width),FA=UA(o.paddingLeft,e.bounds.width);this.topLeftBorderDoubleOuterBox=l>0||u>0?LA(n.left+nA/3,n.top+D/3,l-nA/3,u-D/3,QA.TOP_LEFT):new O(n.left+nA/3,n.top+D/3),this.topRightBorderDoubleOuterBox=l>0||u>0?LA(n.left+V,n.top+D/3,f-gA/3,w-D/3,QA.TOP_RIGHT):new O(n.left+n.width-gA/3,n.top+D/3),this.bottomRightBorderDoubleOuterBox=_>0||E>0?LA(n.left+cA,n.top+mA,_-gA/3,E-pA/3,QA.BOTTOM_RIGHT):new O(n.left+n.width-gA/3,n.top+n.height-pA/3),this.bottomLeftBorderDoubleOuterBox=M>0||S>0?LA(n.left+nA/3,n.top+tA,M-nA/3,S-pA/3,QA.BOTTOM_LEFT):new O(n.left+nA/3,n.top+n.height-pA/3),this.topLeftBorderDoubleInnerBox=l>0||u>0?LA(n.left+nA*2/3,n.top+D*2/3,l-nA*2/3,u-D*2/3,QA.TOP_LEFT):new O(n.left+nA*2/3,n.top+D*2/3),this.topRightBorderDoubleInnerBox=l>0||u>0?LA(n.left+V,n.top+D*2/3,f-gA*2/3,w-D*2/3,QA.TOP_RIGHT):new O(n.left+n.width-gA*2/3,n.top+D*2/3),this.bottomRightBorderDoubleInnerBox=_>0||E>0?LA(n.left+cA,n.top+mA,_-gA*2/3,E-pA*2/3,QA.BOTTOM_RIGHT):new O(n.left+n.width-gA*2/3,n.top+n.height-pA*2/3),this.bottomLeftBorderDoubleInnerBox=M>0||S>0?LA(n.left+nA*2/3,n.top+tA,M-nA*2/3,S-pA*2/3,QA.BOTTOM_LEFT):new O(n.left+nA*2/3,n.top+n.height-pA*2/3),this.topLeftBorderStroke=l>0||u>0?LA(n.left+nA/2,n.top+D/2,l-nA/2,u-D/2,QA.TOP_LEFT):new O(n.left+nA/2,n.top+D/2),this.topRightBorderStroke=l>0||u>0?LA(n.left+V,n.top+D/2,f-gA/2,w-D/2,QA.TOP_RIGHT):new O(n.left+n.width-gA/2,n.top+D/2),this.bottomRightBorderStroke=_>0||E>0?LA(n.left+cA,n.top+mA,_-gA/2,E-pA/2,QA.BOTTOM_RIGHT):new O(n.left+n.width-gA/2,n.top+n.height-pA/2),this.bottomLeftBorderStroke=M>0||S>0?LA(n.left+nA/2,n.top+tA,M-nA/2,S-pA/2,QA.BOTTOM_LEFT):new O(n.left+nA/2,n.top+n.height-pA/2),this.topLeftBorderBox=l>0||u>0?LA(n.left,n.top,l,u,QA.TOP_LEFT):new O(n.left,n.top),this.topRightBorderBox=f>0||w>0?LA(n.left+V,n.top,f,w,QA.TOP_RIGHT):new O(n.left+n.width,n.top),this.bottomRightBorderBox=_>0||E>0?LA(n.left+cA,n.top+mA,_,E,QA.BOTTOM_RIGHT):new O(n.left+n.width,n.top+n.height),this.bottomLeftBorderBox=M>0||S>0?LA(n.left,n.top+tA,M,S,QA.BOTTOM_LEFT):new O(n.left,n.top+n.height),this.topLeftPaddingBox=l>0||u>0?LA(n.left+nA,n.top+D,Math.max(0,l-nA),Math.max(0,u-D),QA.TOP_LEFT):new O(n.left+nA,n.top+D),this.topRightPaddingBox=f>0||w>0?LA(n.left+Math.min(V,n.width-gA),n.top+D,V>n.width+gA?0:Math.max(0,f-gA),Math.max(0,w-D),QA.TOP_RIGHT):new O(n.left+n.width-gA,n.top+D),this.bottomRightPaddingBox=_>0||E>0?LA(n.left+Math.min(cA,n.width-nA),n.top+Math.min(mA,n.height-pA),Math.max(0,_-gA),Math.max(0,E-pA),QA.BOTTOM_RIGHT):new O(n.left+n.width-gA,n.top+n.height-pA),this.bottomLeftPaddingBox=M>0||S>0?LA(n.left+nA,n.top+Math.min(tA,n.height-pA),Math.max(0,M-nA),Math.max(0,S-pA),QA.BOTTOM_LEFT):new O(n.left+nA,n.top+n.height-pA),this.topLeftContentBox=l>0||u>0?LA(n.left+nA+FA,n.top+D+RA,Math.max(0,l-(nA+FA)),Math.max(0,u-(D+RA)),QA.TOP_LEFT):new O(n.left+nA+FA,n.top+D+RA),this.topRightContentBox=f>0||w>0?LA(n.left+Math.min(V,n.width+nA+FA),n.top+D+RA,V>n.width+nA+FA?0:f-nA+FA,w-(D+RA),QA.TOP_RIGHT):new O(n.left+n.width-(gA+pt),n.top+D+RA),this.bottomRightContentBox=_>0||E>0?LA(n.left+Math.min(cA,n.width-(nA+FA)),n.top+Math.min(mA,n.height+D+RA),Math.max(0,_-(gA+pt)),E-(pA+Ut),QA.BOTTOM_RIGHT):new O(n.left+n.width-(gA+pt),n.top+n.height-(pA+Ut)),this.bottomLeftContentBox=M>0||S>0?LA(n.left+nA+FA,n.top+tA,Math.max(0,M-(nA+FA)),S-(pA+Ut),QA.BOTTOM_LEFT):new O(n.left+nA+FA,n.top+n.height-(pA+Ut))}return i}(),QA=function(i){return i[i.TOP_LEFT=0]="TOP_LEFT",i[i.TOP_RIGHT=1]="TOP_RIGHT",i[i.BOTTOM_RIGHT=2]="BOTTOM_RIGHT",i[i.BOTTOM_LEFT=3]="BOTTOM_LEFT",i}(QA||{}),LA=function(i,e,o,n,a){var l=4*((Math.sqrt(2)-1)/3),u=o*l,d=n*l,f=i+o,w=e+n;switch(a){case QA.TOP_LEFT:return new ro(new O(i,w),new O(i,w-d),new O(f-u,e),new O(f,e));case QA.TOP_RIGHT:return new ro(new O(i,e),new O(i+u,e),new O(f,w-d),new O(f,w));case QA.BOTTOM_RIGHT:return new ro(new O(f,e),new O(f,e+d),new O(i+u,w),new O(i,w));case QA.BOTTOM_LEFT:default:return new ro(new O(f,w),new O(f-u,w),new O(i,e+d),new O(i,e))}},oo=function(i){return[i.topLeftBorderBox,i.topRightBorderBox,i.bottomRightBorderBox,i.bottomLeftBorderBox]},yu=function(i){return[i.topLeftContentBox,i.topRightContentBox,i.bottomRightContentBox,i.bottomLeftContentBox]},so=function(i){return[i.topLeftPaddingBox,i.topRightPaddingBox,i.bottomRightPaddingBox,i.bottomLeftPaddingBox]},Eu=function(){function i(e,o,n){this.offsetX=e,this.offsetY=o,this.matrix=n,this.type=0,this.target=6}return i}(),ao=function(){function i(e,o){this.path=e,this.target=o,this.type=1}return i}(),Iu=function(){function i(e){this.opacity=e,this.type=2,this.target=6}return i}(),xu=function(i){return i.type===0},_a=function(i){return i.type===1},Hu=function(i){return i.type===2},Ua=function(i,e){return i.length===e.length?i.some(function(o,n){return o===e[n]}):!1},Lu=function(i,e,o,n,a){return i.map(function(l,u){switch(u){case 0:return l.add(e,o);case 1:return l.add(e+n,o);case 2:return l.add(e+n,o+a);case 3:return l.add(e,o+a)}return l})},Fa=function(){function i(e){this.element=e,this.inlineLevel=[],this.nonInlineLevel=[],this.negativeZIndex=[],this.zeroOrAutoZIndexOrTransformedOrOpacity=[],this.positiveZIndex=[],this.nonPositionedFloats=[],this.nonPositionedInlineLevel=[]}return i}(),ya=function(){function i(e,o){if(this.container=e,this.parent=o,this.effects=[],this.curves=new Fu(this.container),this.container.styles.opacity<1&&this.effects.push(new Iu(this.container.styles.opacity)),this.container.styles.transform!==null){var n=this.container.bounds.left+this.container.styles.transformOrigin[0].number,a=this.container.bounds.top+this.container.styles.transformOrigin[1].number,l=this.container.styles.transform;this.effects.push(new Eu(n,a,l))}if(this.container.styles.overflowX!==0){var u=oo(this.curves),d=so(this.curves);Ua(u,d)?this.effects.push(new ao(u,6)):(this.effects.push(new ao(u,2)),this.effects.push(new ao(d,4)))}}return i.prototype.getEffects=function(e){for(var o=[2,3].indexOf(this.container.styles.position)===-1,n=this.parent,a=this.effects.slice(0);n;){var l=n.effects.filter(function(f){return!_a(f)});if(o||n.container.styles.position!==0||!n.parent){if(a.unshift.apply(a,l),o=[2,3].indexOf(n.container.styles.position)===-1,n.container.styles.overflowX!==0){var u=oo(n.curves),d=so(n.curves);Ua(u,d)||a.unshift(new ao(d,6))}}else a.unshift.apply(a,l);n=n.parent}return a.filter(function(f){return WA(f.target,e)})},i}(),Es=function(i,e,o,n){i.container.elements.forEach(function(a){var l=WA(a.flags,4),u=WA(a.flags,2),d=new ya(a,i);WA(a.styles.display,2048)&&n.push(d);var f=WA(a.flags,8)?[]:n;if(l||u){var w=l||a.styles.isPositioned()?o:e,m=new Fa(d);if(a.styles.isPositioned()||a.styles.opacity<1||a.styles.isTransformed()){var _=a.styles.zIndex.order;if(_<0){var E=0;w.negativeZIndex.some(function(M,S){return _>M.element.container.styles.zIndex.order?(E=S,!1):E>0}),w.negativeZIndex.splice(E,0,m)}else if(_>0){var K=0;w.positiveZIndex.some(function(M,S){return _>=M.element.container.styles.zIndex.order?(K=S+1,!1):K>0}),w.positiveZIndex.splice(K,0,m)}else w.zeroOrAutoZIndexOrTransformedOrOpacity.push(m)}else a.styles.isFloating()?w.nonPositionedFloats.push(m):w.nonPositionedInlineLevel.push(m);Es(d,m,l?m:o,f)}else a.styles.isInlineLevel()?e.inlineLevel.push(d):e.nonInlineLevel.push(d),Es(d,e,o,f);WA(a.flags,8)&&Ea(a,f)})},Ea=function(i,e){for(var o=i instanceof gs?i.start:1,n=i instanceof gs?i.reversed:!1,a=0;a<e.length;a++){var l=e[a];l.container instanceof $s&&typeof l.container.value=="number"&&l.container.value!==0&&(o=l.container.value),l.listValue=jn(o,l.container.styles.listStyleType,!0),o+=n?-1:1}},bu=function(i){var e=new ya(i,null),o=new Fa(e),n=[];return Es(e,o,o,n),Ea(e.container,n),o},Ia=function(i,e){switch(e){case 0:return bt(i.topLeftBorderBox,i.topLeftPaddingBox,i.topRightBorderBox,i.topRightPaddingBox);case 1:return bt(i.topRightBorderBox,i.topRightPaddingBox,i.bottomRightBorderBox,i.bottomRightPaddingBox);case 2:return bt(i.bottomRightBorderBox,i.bottomRightPaddingBox,i.bottomLeftBorderBox,i.bottomLeftPaddingBox);case 3:default:return bt(i.bottomLeftBorderBox,i.bottomLeftPaddingBox,i.topLeftBorderBox,i.topLeftPaddingBox)}},Mu=function(i,e){switch(e){case 0:return bt(i.topLeftBorderBox,i.topLeftBorderDoubleOuterBox,i.topRightBorderBox,i.topRightBorderDoubleOuterBox);case 1:return bt(i.topRightBorderBox,i.topRightBorderDoubleOuterBox,i.bottomRightBorderBox,i.bottomRightBorderDoubleOuterBox);case 2:return bt(i.bottomRightBorderBox,i.bottomRightBorderDoubleOuterBox,i.bottomLeftBorderBox,i.bottomLeftBorderDoubleOuterBox);case 3:default:return bt(i.bottomLeftBorderBox,i.bottomLeftBorderDoubleOuterBox,i.topLeftBorderBox,i.topLeftBorderDoubleOuterBox)}},Tu=function(i,e){switch(e){case 0:return bt(i.topLeftBorderDoubleInnerBox,i.topLeftPaddingBox,i.topRightBorderDoubleInnerBox,i.topRightPaddingBox);case 1:return bt(i.topRightBorderDoubleInnerBox,i.topRightPaddingBox,i.bottomRightBorderDoubleInnerBox,i.bottomRightPaddingBox);case 2:return bt(i.bottomRightBorderDoubleInnerBox,i.bottomRightPaddingBox,i.bottomLeftBorderDoubleInnerBox,i.bottomLeftPaddingBox);case 3:default:return bt(i.bottomLeftBorderDoubleInnerBox,i.bottomLeftPaddingBox,i.topLeftBorderDoubleInnerBox,i.topLeftPaddingBox)}},Pu=function(i,e){switch(e){case 0:return co(i.topLeftBorderStroke,i.topRightBorderStroke);case 1:return co(i.topRightBorderStroke,i.bottomRightBorderStroke);case 2:return co(i.bottomRightBorderStroke,i.bottomLeftBorderStroke);case 3:default:return co(i.bottomLeftBorderStroke,i.topLeftBorderStroke)}},co=function(i,e){var o=[];return Lt(i)?o.push(i.subdivide(.5,!1)):o.push(i),Lt(e)?o.push(e.subdivide(.5,!0)):o.push(e),o},bt=function(i,e,o,n){var a=[];return Lt(i)?a.push(i.subdivide(.5,!1)):a.push(i),Lt(o)?a.push(o.subdivide(.5,!0)):a.push(o),Lt(n)?a.push(n.subdivide(.5,!0).reverse()):a.push(n),Lt(e)?a.push(e.subdivide(.5,!1).reverse()):a.push(e),a},xa=function(i){var e=i.bounds,o=i.styles;return e.add(o.borderLeftWidth,o.borderTopWidth,-(o.borderRightWidth+o.borderLeftWidth),-(o.borderTopWidth+o.borderBottomWidth))},lo=function(i){var e=i.styles,o=i.bounds,n=UA(e.paddingLeft,o.width),a=UA(e.paddingRight,o.width),l=UA(e.paddingTop,o.width),u=UA(e.paddingBottom,o.width);return o.add(n+e.borderLeftWidth,l+e.borderTopWidth,-(e.borderRightWidth+e.borderLeftWidth+n+a),-(e.borderTopWidth+e.borderBottomWidth+l+u))},Su=function(i,e){return i===0?e.bounds:i===2?lo(e):xa(e)},Ou=function(i,e){return i===0?e.bounds:i===2?lo(e):xa(e)},Is=function(i,e,o){var n=Su(rn(i.styles.backgroundOrigin,e),i),a=Ou(rn(i.styles.backgroundClip,e),i),l=Du(rn(i.styles.backgroundSize,e),o,n),u=l[0],d=l[1],f=Yt(rn(i.styles.backgroundPosition,e),n.width-u,n.height-d),w=Ku(rn(i.styles.backgroundRepeat,e),f,l,n,a),m=Math.round(n.left+f[0]),_=Math.round(n.top+f[1]);return[w,m,_,u,d]},nn=function(i){return vA(i)&&i.value===De.AUTO},uo=function(i){return typeof i=="number"},Du=function(i,e,o){var n=e[0],a=e[1],l=e[2],u=i[0],d=i[1];if(!u)return[0,0];if(DA(u)&&d&&DA(d))return[UA(u,o.width),UA(d,o.height)];var f=uo(l);if(vA(u)&&(u.value===De.CONTAIN||u.value===De.COVER)){if(uo(l)){var w=o.width/o.height;return w<l!=(u.value===De.COVER)?[o.width,o.width/l]:[o.height*l,o.height]}return[o.width,o.height]}var m=uo(n),_=uo(a),E=m||_;if(nn(u)&&(!d||nn(d))){if(m&&_)return[n,a];if(!f&&!E)return[o.width,o.height];if(E&&f){var K=m?n:a*l,M=_?a:n/l;return[K,M]}var S=m?n:o.width,sA=_?a:o.height;return[S,sA]}if(f){var W=0,V=0;return DA(u)?W=UA(u,o.width):DA(d)&&(V=UA(d,o.height)),nn(u)?W=V*l:(!d||nn(d))&&(V=W/l),[W,V]}var mA=null,cA=null;if(DA(u)?mA=UA(u,o.width):d&&DA(d)&&(cA=UA(d,o.height)),mA!==null&&(!d||nn(d))&&(cA=m&&_?mA/n*a:o.height),cA!==null&&nn(u)&&(mA=m&&_?cA/a*n:o.width),mA!==null&&cA!==null)return[mA,cA];throw new Error("Unable to calculate background-size for element")},rn=function(i,e){var o=i[e];return typeof o>"u"?i[0]:o},Ku=function(i,e,o,n,a){var l=e[0],u=e[1],d=o[0],f=o[1];switch(i){case 2:return[new O(Math.round(n.left),Math.round(n.top+u)),new O(Math.round(n.left+n.width),Math.round(n.top+u)),new O(Math.round(n.left+n.width),Math.round(f+n.top+u)),new O(Math.round(n.left),Math.round(f+n.top+u))];case 3:return[new O(Math.round(n.left+l),Math.round(n.top)),new O(Math.round(n.left+l+d),Math.round(n.top)),new O(Math.round(n.left+l+d),Math.round(n.height+n.top)),new O(Math.round(n.left+l),Math.round(n.height+n.top))];case 1:return[new O(Math.round(n.left+l),Math.round(n.top+u)),new O(Math.round(n.left+l+d),Math.round(n.top+u)),new O(Math.round(n.left+l+d),Math.round(n.top+u+f)),new O(Math.round(n.left+l),Math.round(n.top+u+f))];default:return[new O(Math.round(a.left),Math.round(a.top)),new O(Math.round(a.left+a.width),Math.round(a.top)),new O(Math.round(a.left+a.width),Math.round(a.height+a.top)),new O(Math.round(a.left),Math.round(a.height+a.top))]}},ku="data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7",Ha="Hidden Text",Nu=function(){function i(e){this._data={},this._document=e}return i.prototype.parseMetrics=function(e,o){var n=this._document.createElement("div"),a=this._document.createElement("img"),l=this._document.createElement("span"),u=this._document.body;n.style.visibility="hidden",n.style.fontFamily=e,n.style.fontSize=o,n.style.margin="0",n.style.padding="0",n.style.whiteSpace="nowrap",u.appendChild(n),a.src=ku,a.width=1,a.height=1,a.style.margin="0",a.style.padding="0",a.style.verticalAlign="baseline",l.style.fontFamily=e,l.style.fontSize=o,l.style.margin="0",l.style.padding="0",l.appendChild(this._document.createTextNode(Ha)),n.appendChild(l),n.appendChild(a);var d=a.offsetTop-l.offsetTop+2;n.removeChild(l),n.appendChild(this._document.createTextNode(Ha)),n.style.lineHeight="normal",a.style.verticalAlign="super";var f=a.offsetTop-n.offsetTop+2;return u.removeChild(n),{baseline:d,middle:f}},i.prototype.getMetrics=function(e,o){var n=e+" "+o;return typeof this._data[n]>"u"&&(this._data[n]=this.parseMetrics(e,o)),this._data[n]},i}(),La=function(){function i(e,o){this.context=e,this.options=o}return i}(),Ru=1e4,Gu=function(i){N(e,i);function e(o,n){var a=i.call(this,o,n)||this;return a._activeEffects=[],a.canvas=n.canvas?n.canvas:document.createElement("canvas"),a.ctx=a.canvas.getContext("2d"),n.canvas||(a.canvas.width=Math.floor(n.width*n.scale),a.canvas.height=Math.floor(n.height*n.scale),a.canvas.style.width=n.width+"px",a.canvas.style.height=n.height+"px"),a.fontMetrics=new Nu(document),a.ctx.scale(a.options.scale,a.options.scale),a.ctx.translate(-n.x,-n.y),a.ctx.textBaseline="bottom",a._activeEffects=[],a.context.logger.debug("Canvas renderer initialized ("+n.width+"x"+n.height+") with scale "+n.scale),a}return e.prototype.applyEffects=function(o){for(var n=this;this._activeEffects.length;)this.popEffect();o.forEach(function(a){return n.applyEffect(a)})},e.prototype.applyEffect=function(o){this.ctx.save(),Hu(o)&&(this.ctx.globalAlpha=o.opacity),xu(o)&&(this.ctx.translate(o.offsetX,o.offsetY),this.ctx.transform(o.matrix[0],o.matrix[1],o.matrix[2],o.matrix[3],o.matrix[4],o.matrix[5]),this.ctx.translate(-o.offsetX,-o.offsetY)),_a(o)&&(this.path(o.path),this.ctx.clip()),this._activeEffects.push(o)},e.prototype.popEffect=function(){this._activeEffects.pop(),this.ctx.restore()},e.prototype.renderStack=function(o){return B(this,void 0,void 0,function(){var n;return g(this,function(a){switch(a.label){case 0:return n=o.element.container.styles,n.isVisible()?[4,this.renderStackContent(o)]:[3,2];case 1:a.sent(),a.label=2;case 2:return[2]}})})},e.prototype.renderNode=function(o){return B(this,void 0,void 0,function(){return g(this,function(n){switch(n.label){case 0:if(WA(o.container.flags,16))debugger;return o.container.styles.isVisible()?[4,this.renderNodeBackgroundAndBorders(o)]:[3,3];case 1:return n.sent(),[4,this.renderNodeContent(o)];case 2:n.sent(),n.label=3;case 3:return[2]}})})},e.prototype.renderTextWithLetterSpacing=function(o,n,a){var l=this;if(n===0)this.ctx.fillText(o.text,o.bounds.left,o.bounds.top+a);else{var u=Bs(o.text);u.reduce(function(d,f){return l.ctx.fillText(f,d,o.bounds.top+a),d+l.ctx.measureText(f).width},o.bounds.left)}},e.prototype.createFontStyle=function(o){var n=o.fontVariant.filter(function(u){return u==="normal"||u==="small-caps"}).join(""),a=Xu(o.fontFamily).join(", "),l=le(o.fontSize)?""+o.fontSize.number+o.fontSize.unit:o.fontSize.number+"px";return[[o.fontStyle,n,o.fontWeight,l,a].join(" "),a,l]},e.prototype.renderTextNode=function(o,n){return B(this,void 0,void 0,function(){var a,l,u,d,f,w,m,_,E=this;return g(this,function(K){return a=this.createFontStyle(n),l=a[0],u=a[1],d=a[2],this.ctx.font=l,this.ctx.direction=n.direction===1?"rtl":"ltr",this.ctx.textAlign="left",this.ctx.textBaseline="alphabetic",f=this.fontMetrics.getMetrics(u,d),w=f.baseline,m=f.middle,_=n.paintOrder,o.textBounds.forEach(function(M){_.forEach(function(S){switch(S){case 0:E.ctx.fillStyle=MA(n.color),E.renderTextWithLetterSpacing(M,n.letterSpacing,w);var sA=n.textShadow;sA.length&&M.text.trim().length&&(sA.slice(0).reverse().forEach(function(W){E.ctx.shadowColor=MA(W.color),E.ctx.shadowOffsetX=W.offsetX.number*E.options.scale,E.ctx.shadowOffsetY=W.offsetY.number*E.options.scale,E.ctx.shadowBlur=W.blur.number,E.renderTextWithLetterSpacing(M,n.letterSpacing,w)}),E.ctx.shadowColor="",E.ctx.shadowOffsetX=0,E.ctx.shadowOffsetY=0,E.ctx.shadowBlur=0),n.textDecorationLine.length&&(E.ctx.fillStyle=MA(n.textDecorationColor||n.color),n.textDecorationLine.forEach(function(W){switch(W){case 1:E.ctx.fillRect(M.bounds.left,Math.round(M.bounds.top+w),M.bounds.width,1);break;case 2:E.ctx.fillRect(M.bounds.left,Math.round(M.bounds.top),M.bounds.width,1);break;case 3:E.ctx.fillRect(M.bounds.left,Math.ceil(M.bounds.top+m),M.bounds.width,1);break}}));break;case 1:n.webkitTextStrokeWidth&&M.text.trim().length&&(E.ctx.strokeStyle=MA(n.webkitTextStrokeColor),E.ctx.lineWidth=n.webkitTextStrokeWidth,E.ctx.lineJoin=window.chrome?"miter":"round",E.ctx.strokeText(M.text,M.bounds.left,M.bounds.top+w)),E.ctx.strokeStyle="",E.ctx.lineWidth=0,E.ctx.lineJoin="miter";break}})}),[2]})})},e.prototype.renderReplacedElement=function(o,n,a){if(a&&o.intrinsicWidth>0&&o.intrinsicHeight>0){var l=lo(o),u=so(n);this.path(u),this.ctx.save(),this.ctx.clip(),this.ctx.drawImage(a,0,0,o.intrinsicWidth,o.intrinsicHeight,l.left,l.top,l.width,l.height),this.ctx.restore()}},e.prototype.renderNodeContent=function(o){return B(this,void 0,void 0,function(){var n,a,l,u,d,f,V,V,w,m,_,E,cA,K,M,tA,S,sA,W,V,mA,cA,tA;return g(this,function(D){switch(D.label){case 0:this.applyEffects(o.getEffects(4)),n=o.container,a=o.curves,l=n.styles,u=0,d=n.textNodes,D.label=1;case 1:return u<d.length?(f=d[u],[4,this.renderTextNode(f,l)]):[3,4];case 2:D.sent(),D.label=3;case 3:return u++,[3,1];case 4:if(!(n instanceof Js))return[3,8];D.label=5;case 5:return D.trys.push([5,7,,8]),[4,this.context.cache.match(n.src)];case 6:return V=D.sent(),this.renderReplacedElement(n,a,V),[3,8];case 7:return D.sent(),this.context.logger.error("Error loading image "+n.src),[3,8];case 8:if(n instanceof Ys&&this.renderReplacedElement(n,a,n.canvas),!(n instanceof qs))return[3,12];D.label=9;case 9:return D.trys.push([9,11,,12]),[4,this.context.cache.match(n.svg)];case 10:return V=D.sent(),this.renderReplacedElement(n,a,V),[3,12];case 11:return D.sent(),this.context.logger.error("Error loading svg "+n.svg.substring(0,255)),[3,12];case 12:return n instanceof ea&&n.tree?(w=new e(this.context,{scale:this.options.scale,backgroundColor:n.backgroundColor,x:0,y:0,width:n.width,height:n.height}),[4,w.render(n.tree)]):[3,14];case 13:m=D.sent(),n.width&&n.height&&this.ctx.drawImage(m,0,0,n.width,n.height,n.bounds.left,n.bounds.top,n.bounds.width,n.bounds.height),D.label=14;case 14:if(n instanceof ps&&(_=Math.min(n.bounds.width,n.bounds.height),n.type===$r?n.checked&&(this.ctx.save(),this.path([new O(n.bounds.left+_*.39363,n.bounds.top+_*.79),new O(n.bounds.left+_*.16,n.bounds.top+_*.5549),new O(n.bounds.left+_*.27347,n.bounds.top+_*.44071),new O(n.bounds.left+_*.39694,n.bounds.top+_*.5649),new O(n.bounds.left+_*.72983,n.bounds.top+_*.23),new O(n.bounds.left+_*.84,n.bounds.top+_*.34085),new O(n.bounds.left+_*.39363,n.bounds.top+_*.79)]),this.ctx.fillStyle=MA(js),this.ctx.fill(),this.ctx.restore()):n.type===jr&&n.checked&&(this.ctx.save(),this.ctx.beginPath(),this.ctx.arc(n.bounds.left+_/2,n.bounds.top+_/2,_/4,0,Math.PI*2,!0),this.ctx.fillStyle=MA(js),this.ctx.fill(),this.ctx.restore())),Vu(n)&&n.value.length){switch(E=this.createFontStyle(l),cA=E[0],K=E[1],M=this.fontMetrics.getMetrics(cA,K).baseline,this.ctx.font=cA,this.ctx.fillStyle=MA(l.color),this.ctx.textBaseline="alphabetic",this.ctx.textAlign=zu(n.styles.textAlign),tA=lo(n),S=0,n.styles.textAlign){case 1:S+=tA.width/2;break;case 2:S+=tA.width;break}sA=tA.add(S,0,0,-tA.height/2+1),this.ctx.save(),this.path([new O(tA.left,tA.top),new O(tA.left+tA.width,tA.top),new O(tA.left+tA.width,tA.top+tA.height),new O(tA.left,tA.top+tA.height)]),this.ctx.clip(),this.renderTextWithLetterSpacing(new qn(n.value,sA),l.letterSpacing,M),this.ctx.restore(),this.ctx.textBaseline="alphabetic",this.ctx.textAlign="left"}if(!WA(n.styles.display,2048))return[3,20];if(n.styles.listStyleImage===null)return[3,19];if(W=n.styles.listStyleImage,W.type!==0)return[3,18];V=void 0,mA=W.url,D.label=15;case 15:return D.trys.push([15,17,,18]),[4,this.context.cache.match(mA)];case 16:return V=D.sent(),this.ctx.drawImage(V,n.bounds.left-(V.width+10),n.bounds.top),[3,18];case 17:return D.sent(),this.context.logger.error("Error loading list-style-image "+mA),[3,18];case 18:return[3,20];case 19:o.listValue&&n.styles.listStyleType!==-1&&(cA=this.createFontStyle(l)[0],this.ctx.font=cA,this.ctx.fillStyle=MA(l.color),this.ctx.textBaseline="middle",this.ctx.textAlign="right",tA=new C(n.bounds.left,n.bounds.top+UA(n.styles.paddingTop,n.bounds.width),n.bounds.width,TA(l.lineHeight,l.fontSize.number)/2+1),this.renderTextWithLetterSpacing(new qn(o.listValue,tA),l.letterSpacing,TA(l.lineHeight,l.fontSize.number)/2+2),this.ctx.textBaseline="bottom",this.ctx.textAlign="left"),D.label=20;case 20:return[2]}})})},e.prototype.renderStackContent=function(o){return B(this,void 0,void 0,function(){var n,a,W,l,u,W,d,f,W,w,m,W,_,E,W,K,M,W,S,sA,W;return g(this,function(V){switch(V.label){case 0:if(WA(o.element.container.flags,16))debugger;return[4,this.renderNodeBackgroundAndBorders(o.element)];case 1:V.sent(),n=0,a=o.negativeZIndex,V.label=2;case 2:return n<a.length?(W=a[n],[4,this.renderStack(W)]):[3,5];case 3:V.sent(),V.label=4;case 4:return n++,[3,2];case 5:return[4,this.renderNodeContent(o.element)];case 6:V.sent(),l=0,u=o.nonInlineLevel,V.label=7;case 7:return l<u.length?(W=u[l],[4,this.renderNode(W)]):[3,10];case 8:V.sent(),V.label=9;case 9:return l++,[3,7];case 10:d=0,f=o.nonPositionedFloats,V.label=11;case 11:return d<f.length?(W=f[d],[4,this.renderStack(W)]):[3,14];case 12:V.sent(),V.label=13;case 13:return d++,[3,11];case 14:w=0,m=o.nonPositionedInlineLevel,V.label=15;case 15:return w<m.length?(W=m[w],[4,this.renderStack(W)]):[3,18];case 16:V.sent(),V.label=17;case 17:return w++,[3,15];case 18:_=0,E=o.inlineLevel,V.label=19;case 19:return _<E.length?(W=E[_],[4,this.renderNode(W)]):[3,22];case 20:V.sent(),V.label=21;case 21:return _++,[3,19];case 22:K=0,M=o.zeroOrAutoZIndexOrTransformedOrOpacity,V.label=23;case 23:return K<M.length?(W=M[K],[4,this.renderStack(W)]):[3,26];case 24:V.sent(),V.label=25;case 25:return K++,[3,23];case 26:S=0,sA=o.positiveZIndex,V.label=27;case 27:return S<sA.length?(W=sA[S],[4,this.renderStack(W)]):[3,30];case 28:V.sent(),V.label=29;case 29:return S++,[3,27];case 30:return[2]}})})},e.prototype.mask=function(o){this.ctx.beginPath(),this.ctx.moveTo(0,0),this.ctx.lineTo(this.canvas.width,0),this.ctx.lineTo(this.canvas.width,this.canvas.height),this.ctx.lineTo(0,this.canvas.height),this.ctx.lineTo(0,0),this.formatPath(o.slice(0).reverse()),this.ctx.closePath()},e.prototype.path=function(o){this.ctx.beginPath(),this.formatPath(o),this.ctx.closePath()},e.prototype.formatPath=function(o){var n=this;o.forEach(function(a,l){var u=Lt(a)?a.start:a;l===0?n.ctx.moveTo(u.x,u.y):n.ctx.lineTo(u.x,u.y),Lt(a)&&n.ctx.bezierCurveTo(a.startControl.x,a.startControl.y,a.endControl.x,a.endControl.y,a.end.x,a.end.y)})},e.prototype.renderRepeat=function(o,n,a,l){this.path(o),this.ctx.fillStyle=n,this.ctx.translate(a,l),this.ctx.fill(),this.ctx.translate(-a,-l)},e.prototype.resizeImage=function(o,n,a){var l;if(o.width===n&&o.height===a)return o;var u=(l=this.canvas.ownerDocument)!==null&&l!==void 0?l:document,d=u.createElement("canvas");d.width=Math.max(1,n),d.height=Math.max(1,a);var f=d.getContext("2d");return f.drawImage(o,0,0,o.width,o.height,0,0,n,a),d},e.prototype.renderBackgroundImage=function(o){return B(this,void 0,void 0,function(){var n,a,l,u,d,f;return g(this,function(w){switch(w.label){case 0:n=o.styles.backgroundImage.length-1,a=function(m){var _,E,K,RA,wt,mt,FA,tt,pA,M,RA,wt,mt,FA,tt,S,sA,W,V,mA,cA,tA,D,gA,pA,nA,RA,pt,Ut,FA,tt,Re,wt,mt,Bi,Ae,Ge,gi,pi,pe,wi,we;return g(this,function(on){switch(on.label){case 0:if(m.type!==0)return[3,5];_=void 0,E=m.url,on.label=1;case 1:return on.trys.push([1,3,,4]),[4,l.context.cache.match(E)];case 2:return _=on.sent(),[3,4];case 3:return on.sent(),l.context.logger.error("Error loading background-image "+E),[3,4];case 4:return _&&(K=Is(o,n,[_.width,_.height,_.width/_.height]),RA=K[0],wt=K[1],mt=K[2],FA=K[3],tt=K[4],pA=l.ctx.createPattern(l.resizeImage(_,FA,tt),"repeat"),l.renderRepeat(RA,pA,wt,mt)),[3,6];case 5:Or(m)?(M=Is(o,n,[null,null,null]),RA=M[0],wt=M[1],mt=M[2],FA=M[3],tt=M[4],S=zo(m.angle,FA,tt),sA=S[0],W=S[1],V=S[2],mA=S[3],cA=S[4],tA=document.createElement("canvas"),tA.width=FA,tA.height=tt,D=tA.getContext("2d"),gA=D.createLinearGradient(W,mA,V,cA),Lr(m.stops,sA).forEach(function(Ar){return gA.addColorStop(Ar.stop,MA(Ar.color))}),D.fillStyle=gA,D.fillRect(0,0,FA,tt),FA>0&&tt>0&&(pA=l.ctx.createPattern(tA,"repeat"),l.renderRepeat(RA,pA,wt,mt))):Dr(m)&&(nA=Is(o,n,[null,null,null]),RA=nA[0],pt=nA[1],Ut=nA[2],FA=nA[3],tt=nA[4],Re=m.position.length===0?[he]:m.position,wt=UA(Re[0],FA),mt=UA(Re[Re.length-1],tt),Bi=xt(m,wt,mt,FA,tt),Ae=Bi[0],Ge=Bi[1],Ae>0&&Ge>0&&(gi=l.ctx.createRadialGradient(pt+wt,Ut+mt,0,pt+wt,Ut+mt,Ae),Lr(m.stops,Ae*2).forEach(function(Ar){return gi.addColorStop(Ar.stop,MA(Ar.color))}),l.path(RA),l.ctx.fillStyle=gi,Ae!==Ge?(pi=o.bounds.left+.5*o.bounds.width,pe=o.bounds.top+.5*o.bounds.height,wi=Ge/Ae,we=1/wi,l.ctx.save(),l.ctx.translate(pi,pe),l.ctx.transform(1,0,0,wi,0,0),l.ctx.translate(-pi,-pe),l.ctx.fillRect(pt,we*(Ut-pe)+pe,FA,tt*we),l.ctx.restore()):l.ctx.fill())),on.label=6;case 6:return n--,[2]}})},l=this,u=0,d=o.styles.backgroundImage.slice(0).reverse(),w.label=1;case 1:return u<d.length?(f=d[u],[5,a(f)]):[3,4];case 2:w.sent(),w.label=3;case 3:return u++,[3,1];case 4:return[2]}})})},e.prototype.renderSolidBorder=function(o,n,a){return B(this,void 0,void 0,function(){return g(this,function(l){return this.path(Ia(a,n)),this.ctx.fillStyle=MA(o),this.ctx.fill(),[2]})})},e.prototype.renderDoubleBorder=function(o,n,a,l){return B(this,void 0,void 0,function(){var u,d;return g(this,function(f){switch(f.label){case 0:return n<3?[4,this.renderSolidBorder(o,a,l)]:[3,2];case 1:return f.sent(),[2];case 2:return u=Mu(l,a),this.path(u),this.ctx.fillStyle=MA(o),this.ctx.fill(),d=Tu(l,a),this.path(d),this.ctx.fill(),[2]}})})},e.prototype.renderNodeBackgroundAndBorders=function(o){return B(this,void 0,void 0,function(){var n,a,l,u,d,f,w,m,_=this;return g(this,function(E){switch(E.label){case 0:return this.applyEffects(o.getEffects(2)),n=o.container.styles,a=!qA(n.backgroundColor)||n.backgroundImage.length,l=[{style:n.borderTopStyle,color:n.borderTopColor,width:n.borderTopWidth},{style:n.borderRightStyle,color:n.borderRightColor,width:n.borderRightWidth},{style:n.borderBottomStyle,color:n.borderBottomColor,width:n.borderBottomWidth},{style:n.borderLeftStyle,color:n.borderLeftColor,width:n.borderLeftWidth}],u=Zu(rn(n.backgroundClip,0),o.curves),a||n.boxShadow.length?(this.ctx.save(),this.path(u),this.ctx.clip(),qA(n.backgroundColor)||(this.ctx.fillStyle=MA(n.backgroundColor),this.ctx.fill()),[4,this.renderBackgroundImage(o.container)]):[3,2];case 1:E.sent(),this.ctx.restore(),n.boxShadow.slice(0).reverse().forEach(function(K){_.ctx.save();var M=oo(o.curves),S=K.inset?0:Ru,sA=Lu(M,-S+(K.inset?1:-1)*K.spread.number,(K.inset?1:-1)*K.spread.number,K.spread.number*(K.inset?-2:2),K.spread.number*(K.inset?-2:2));K.inset?(_.path(M),_.ctx.clip(),_.mask(sA)):(_.mask(M),_.ctx.clip(),_.path(sA)),_.ctx.shadowOffsetX=K.offsetX.number+S,_.ctx.shadowOffsetY=K.offsetY.number,_.ctx.shadowColor=MA(K.color),_.ctx.shadowBlur=K.blur.number,_.ctx.fillStyle=K.inset?MA(K.color):"rgba(0,0,0,1)",_.ctx.fill(),_.ctx.restore()}),E.label=2;case 2:d=0,f=0,w=l,E.label=3;case 3:return f<w.length?(m=w[f],m.style!==0&&!qA(m.color)&&m.width>0?m.style!==2?[3,5]:[4,this.renderDashedDottedBorder(m.color,m.width,d,o.curves,2)]:[3,11]):[3,13];case 4:return E.sent(),[3,11];case 5:return m.style!==3?[3,7]:[4,this.renderDashedDottedBorder(m.color,m.width,d,o.curves,3)];case 6:return E.sent(),[3,11];case 7:return m.style!==4?[3,9]:[4,this.renderDoubleBorder(m.color,m.width,d,o.curves)];case 8:return E.sent(),[3,11];case 9:return[4,this.renderSolidBorder(m.color,d,o.curves)];case 10:E.sent(),E.label=11;case 11:d++,E.label=12;case 12:return f++,[3,3];case 13:return[2]}})})},e.prototype.renderDashedDottedBorder=function(o,n,a,l,u){return B(this,void 0,void 0,function(){var d,f,w,m,_,E,K,M,S,sA,W,V,mA,cA,tA,D,tA,D;return g(this,function(gA){return this.ctx.save(),d=Pu(l,a),f=Ia(l,a),u===2&&(this.path(f),this.ctx.clip()),Lt(f[0])?(w=f[0].start.x,m=f[0].start.y):(w=f[0].x,m=f[0].y),Lt(f[1])?(_=f[1].end.x,E=f[1].end.y):(_=f[1].x,E=f[1].y),a===0||a===2?K=Math.abs(w-_):K=Math.abs(m-E),this.ctx.beginPath(),u===3?this.formatPath(d):this.formatPath(f.slice(0,2)),M=n<3?n*3:n*2,S=n<3?n*2:n,u===3&&(M=n,S=n),sA=!0,K<=M*2?sA=!1:K<=M*2+S?(W=K/(2*M+S),M*=W,S*=W):(V=Math.floor((K+S)/(M+S)),mA=(K-V*M)/(V-1),cA=(K-(V+1)*M)/V,S=cA<=0||Math.abs(S-mA)<Math.abs(S-cA)?mA:cA),sA&&(u===3?this.ctx.setLineDash([0,M+S]):this.ctx.setLineDash([M,S])),u===3?(this.ctx.lineCap="round",this.ctx.lineWidth=n):this.ctx.lineWidth=n*2+1.1,this.ctx.strokeStyle=MA(o),this.ctx.stroke(),this.ctx.setLineDash([]),u===2&&(Lt(f[0])&&(tA=f[3],D=f[0],this.ctx.beginPath(),this.formatPath([new O(tA.end.x,tA.end.y),new O(D.start.x,D.start.y)]),this.ctx.stroke()),Lt(f[1])&&(tA=f[1],D=f[2],this.ctx.beginPath(),this.formatPath([new O(tA.end.x,tA.end.y),new O(D.start.x,D.start.y)]),this.ctx.stroke())),this.ctx.restore(),[2]})})},e.prototype.render=function(o){return B(this,void 0,void 0,function(){var n;return g(this,function(a){switch(a.label){case 0:return this.options.backgroundColor&&(this.ctx.fillStyle=MA(this.options.backgroundColor),this.ctx.fillRect(this.options.x,this.options.y,this.options.width,this.options.height)),n=bu(o),[4,this.renderStack(n)];case 1:return a.sent(),this.applyEffects([]),[2,this.canvas]}})})},e}(La),Vu=function(i){return i instanceof ta||i instanceof Aa?!0:i instanceof ps&&i.type!==jr&&i.type!==$r},Zu=function(i,e){switch(i){case 0:return oo(e);case 2:return yu(e);case 1:default:return so(e)}},zu=function(i){switch(i){case 1:return"center";case 2:return"right";case 0:default:return"left"}},Wu=["-apple-system","system-ui"],Xu=function(i){return/iPhone OS 15_(0|1)/.test(window.navigator.userAgent)?i.filter(function(e){return Wu.indexOf(e)===-1}):i},Ju=function(i){N(e,i);function e(o,n){var a=i.call(this,o,n)||this;return a.canvas=n.canvas?n.canvas:document.createElement("canvas"),a.ctx=a.canvas.getContext("2d"),a.options=n,a.canvas.width=Math.floor(n.width*n.scale),a.canvas.height=Math.floor(n.height*n.scale),a.canvas.style.width=n.width+"px",a.canvas.style.height=n.height+"px",a.ctx.scale(a.options.scale,a.options.scale),a.ctx.translate(-n.x,-n.y),a.context.logger.debug("EXPERIMENTAL ForeignObject renderer initialized ("+n.width+"x"+n.height+" at "+n.x+","+n.y+") with scale "+n.scale),a}return e.prototype.render=function(o){return B(this,void 0,void 0,function(){var n,a;return g(this,function(l){switch(l.label){case 0:return n=ds(this.options.width*this.options.scale,this.options.height*this.options.scale,this.options.scale,this.options.scale,o),[4,Yu(n)];case 1:return a=l.sent(),this.options.backgroundColor&&(this.ctx.fillStyle=MA(this.options.backgroundColor),this.ctx.fillRect(0,0,this.options.width*this.options.scale,this.options.height*this.options.scale)),this.ctx.drawImage(a,-this.options.x*this.options.scale,-this.options.y*this.options.scale),[2,this.canvas]}})})},e}(La),Yu=function(i){return new Promise(function(e,o){var n=new Image;n.onload=function(){e(n)},n.onerror=o,n.src="data:image/svg+xml;charset=utf-8,"+encodeURIComponent(new XMLSerializer().serializeToString(i))})},qu=function(){function i(e){var o=e.id,n=e.enabled;this.id=o,this.enabled=n,this.start=Date.now()}return i.prototype.debug=function(){for(var e=[],o=0;o<arguments.length;o++)e[o]=arguments[o];this.enabled&&(typeof window<"u"&&window.console&&typeof console.debug=="function"?console.debug.apply(console,Q([this.id,this.getTime()+"ms"],e)):this.info.apply(this,e))},i.prototype.getTime=function(){return Date.now()-this.start},i.prototype.info=function(){for(var e=[],o=0;o<arguments.length;o++)e[o]=arguments[o];this.enabled&&typeof window<"u"&&window.console&&typeof console.info=="function"&&console.info.apply(console,Q([this.id,this.getTime()+"ms"],e))},i.prototype.warn=function(){for(var e=[],o=0;o<arguments.length;o++)e[o]=arguments[o];this.enabled&&(typeof window<"u"&&window.console&&typeof console.warn=="function"?console.warn.apply(console,Q([this.id,this.getTime()+"ms"],e)):this.info.apply(this,e))},i.prototype.error=function(){for(var e=[],o=0;o<arguments.length;o++)e[o]=arguments[o];this.enabled&&(typeof window<"u"&&window.console&&typeof console.error=="function"?console.error.apply(console,Q([this.id,this.getTime()+"ms"],e)):this.info.apply(this,e))},i.instances={},i}(),$u=function(){function i(e,o){var n;this.windowBounds=o,this.instanceName="#"+i.instanceCount++,this.logger=new qu({id:this.instanceName,enabled:e.logging}),this.cache=(n=e.cache)!==null&&n!==void 0?n:new wu(this,e)}return i.instanceCount=1,i}(),ju=function(i,e){return e===void 0&&(e={}),Ah(i,e)};typeof window<"u"&&Qa.setContext(window);var Ah=function(i,e){return B(void 0,void 0,void 0,function(){var o,n,a,l,u,d,f,w,m,_,E,K,M,S,sA,W,V,mA,cA,tA,gA,D,gA,pA,nA,RA,pt,Ut,FA,tt,Re,wt,mt,Bi,Ae,Ge,gi,pi,pe,wi;return g(this,function(we){switch(we.label){case 0:if(!i||typeof i!="object")return[2,Promise.reject("Invalid element provided as first argument")];if(o=i.ownerDocument,!o)throw new Error("Element is not attached to a Document");if(n=o.defaultView,!n)throw new Error("Document is not attached to a Window");return a={allowTaint:(pA=e.allowTaint)!==null&&pA!==void 0?pA:!1,imageTimeout:(nA=e.imageTimeout)!==null&&nA!==void 0?nA:15e3,proxy:e.proxy,useCORS:(RA=e.useCORS)!==null&&RA!==void 0?RA:!1},l=I({logging:(pt=e.logging)!==null&&pt!==void 0?pt:!0,cache:e.cache},a),u={windowWidth:(Ut=e.windowWidth)!==null&&Ut!==void 0?Ut:n.innerWidth,windowHeight:(FA=e.windowHeight)!==null&&FA!==void 0?FA:n.innerHeight,scrollX:(tt=e.scrollX)!==null&&tt!==void 0?tt:n.pageXOffset,scrollY:(Re=e.scrollY)!==null&&Re!==void 0?Re:n.pageYOffset},d=new C(u.scrollX,u.scrollY,u.windowWidth,u.windowHeight),f=new $u(l,d),w=(wt=e.foreignObjectRendering)!==null&&wt!==void 0?wt:!1,m={allowTaint:(mt=e.allowTaint)!==null&&mt!==void 0?mt:!1,onclone:e.onclone,ignoreElements:e.ignoreElements,inlineImages:w,copyStyles:w},f.logger.debug("Starting document clone with size "+d.width+"x"+d.height+" scrolled to "+-d.left+","+-d.top),_=new Ca(f,i,m),E=_.clonedReferenceElement,E?[4,_.toIFrame(o,d)]:[2,Promise.reject("Unable to find element in cloned iframe")];case 1:return K=we.sent(),M=ms(E)||tu(E)?x(E.ownerDocument):T(f,E),S=M.width,sA=M.height,W=M.left,V=M.top,mA=th(f,E,e.backgroundColor),cA={canvas:e.canvas,backgroundColor:mA,scale:(Ae=(Bi=e.scale)!==null&&Bi!==void 0?Bi:n.devicePixelRatio)!==null&&Ae!==void 0?Ae:1,x:((Ge=e.x)!==null&&Ge!==void 0?Ge:0)+W,y:((gi=e.y)!==null&&gi!==void 0?gi:0)+V,width:(pi=e.width)!==null&&pi!==void 0?pi:Math.ceil(S),height:(pe=e.height)!==null&&pe!==void 0?pe:Math.ceil(sA)},w?(f.logger.debug("Document cloned, using foreign object rendering"),gA=new Ju(f,cA),[4,gA.render(E)]):[3,3];case 2:return tA=we.sent(),[3,5];case 3:return f.logger.debug("Document cloned, element located at "+W+","+V+" with size "+S+"x"+sA+" using computed rendering"),f.logger.debug("Starting DOM parsing"),D=na(f,E),mA===D.styles.backgroundColor&&(D.styles.backgroundColor=_t.TRANSPARENT),f.logger.debug("Starting renderer for element at "+cA.x+","+cA.y+" with size "+cA.width+"x"+cA.height),gA=new Gu(f,cA),[4,gA.render(D)];case 4:tA=we.sent(),we.label=5;case 5:return(!((wi=e.removeContainer)!==null&&wi!==void 0)||wi)&&(Ca.destroy(K)||f.logger.error("Cannot detach cloned iframe as it is not in the DOM anymore")),f.logger.debug("Finished rendering"),[2,tA]}})})},th=function(i,e,o){var n=e.ownerDocument,a=n.documentElement?$t(i,getComputedStyle(n.documentElement).backgroundColor):_t.TRANSPARENT,l=n.body?$t(i,getComputedStyle(n.body).backgroundColor):_t.TRANSPARENT,u=typeof o=="string"?$t(i,o):o===null?_t.TRANSPARENT:4294967295;return e===n.documentElement?qA(a)?qA(l)?u:l:a:u};return ju})});var $=Ma(nc());function eh(v){v.CapacitorUtils.Synapse=new Proxy({},{get(N,I){return new Proxy({},{get(B,g){return(Q,C,T)=>{let x=v.Capacitor.Plugins[I];if(x===void 0){T(new Error(`Capacitor plugin ${I} not found`));return}if(typeof x[g]!="function"){T(new Error(`Method ${g} not found in Capacitor plugin ${I}`));return}HA(this,null,function*(){try{let b=yield x[g](Q);C(b)}catch(b){T(b)}})}}})}})}function ih(v){v.CapacitorUtils.Synapse=new Proxy({},{get(N,I){return v.cordova.plugins[I]}})}function rc(v=!1){window.CapacitorUtils=window.CapacitorUtils||{},window.Capacitor!==void 0&&!v?eh(window):window.cordova!==void 0&&ih(window)}var te=Co("Geolocation",{web:()=>import("./chunk-ONS33CAL.js").then(v=>new v.GeolocationWeb)});rc();var oc=Co("Motion",{android:()=>import("./chunk-AZ2XQKTE.js").then(v=>new v.MotionWeb),ios:()=>import("./chunk-AZ2XQKTE.js").then(v=>new v.MotionWeb),web:()=>import("./chunk-AZ2XQKTE.js").then(v=>new v.MotionWeb)});function nh(v,N){v&1&&(G(0,"div",8),wA(1,"ion-spinner",9),G(2,"p"),hA(3,"Calculating travel times..."),Y()())}function rh(v,N){if(v&1&&wA(0,"img",29),v&2){let I=yA(2);tr("alt",I.center.name)}}function oh(v,N){if(v&1&&wA(0,"img",30),v&2){let I=yA(2);tr("alt",I.center.name)}}function sh(v,N){if(v&1&&wA(0,"img",31),v&2){let I=yA(2);tr("alt",I.center.name)}}function ah(v,N){if(v&1&&wA(0,"img",32),v&2){let I=yA(2);tr("alt",I.center.name)}}function ch(v,N){if(v&1&&(G(0,"ion-chip",18),wA(1,"ion-icon",33),G(2,"ion-label"),hA(3),Y()()),v&2){let I=yA(2);uA("color",I.getStatusColor(I.center.status)),J(3),Mt(I.center.status)}}function lh(v,N){if(v&1&&(G(0,"div",21)(1,"ion-item",22),wA(2,"ion-icon",34),G(3,"ion-label")(4,"h2"),hA(5,"Capacity"),Y(),G(6,"p"),hA(7),Y()()()()),v&2){let I=yA(2);J(7),Tt("",I.center.capacity," people")}}function uh(v,N){if(v&1){let I=Ve();G(0,"ion-card",35),at("click",function(){let g=me(I).$implicit,Q=yA(2);return Ce(Q.selectTravelMode(g.mode))}),G(1,"ion-card-header"),wA(2,"ion-icon",36),G(3,"ion-card-title"),hA(4),Y()(),G(5,"ion-card-content")(6,"div",37)(7,"div",38),wA(8,"ion-icon",39),G(9,"span"),hA(10),Y()(),G(11,"div",40),wA(12,"ion-icon",41),G(13,"span"),hA(14),Y()()(),G(15,"ion-button",42),at("click",function(){let g=me(I).$implicit,Q=yA(2);return Ce(Q.selectTravelMode(g.mode))}),hA(16," Select "),wA(17,"ion-icon",43),Y()()()}if(v&2){let I=N.$implicit,B=yA(2);J(2),uA("name",I.icon)("color",I.color),J(2),Tt(" ",I.mode==="foot-walking"?"Walking":I.mode==="cycling-regular"?"Cycling":"Driving"," "),J(6),Mt(B.formatTime(I.time)),J(4),Mt(B.formatDistance(I.distance)),J(),uA("color",I.color)}}function hh(v,N){if(v&1&&(G(0,"div",10)(1,"div",11),Ct(2,rh,1,1,"img",12)(3,oh,1,1,"img",13)(4,sh,1,1,"img",14)(5,ah,1,1,"img",15),Y(),G(6,"h1",16),hA(7),Y(),G(8,"div",17)(9,"ion-chip",18),wA(10,"ion-icon",19),G(11,"ion-label"),hA(12),Y()(),Ct(13,ch,4,2,"ion-chip",20),Y(),G(14,"div",21)(15,"ion-item",22),wA(16,"ion-icon",23),G(17,"ion-label")(18,"h2"),hA(19,"Address"),Y(),G(20,"p"),hA(21),Y()()()(),G(22,"div",21)(23,"ion-item",22),wA(24,"ion-icon",24),G(25,"ion-label")(26,"h2"),hA(27,"Contact"),Y(),G(28,"p"),hA(29),Y()()()(),Ct(30,lh,8,1,"div",25),G(31,"div",26)(32,"h2"),hA(33,"Travel Time Estimates"),Y(),G(34,"div",27),Ct(35,uh,18,6,"ion-card",28),Y()()()),v&2){let I=yA();J(2),uA("ngIf",I.center.disaster_type&&I.center.disaster_type.toLowerCase().includes("earthquake")),J(),uA("ngIf",I.center.disaster_type&&I.center.disaster_type.toLowerCase().includes("flood")),J(),uA("ngIf",I.center.disaster_type&&I.center.disaster_type.toLowerCase().includes("typhoon")),J(),uA("ngIf",!I.center.disaster_type||!I.center.disaster_type.toLowerCase().includes("earthquake")&&!I.center.disaster_type.toLowerCase().includes("flood")&&!I.center.disaster_type.toLowerCase().includes("typhoon")),J(2),Mt(I.center.name),J(2),uA("color",I.getStatusColor(I.center.status)),J(),uA("name",I.getDisasterTypeIcon(I.center.disaster_type)),J(2),Mt(I.center.disaster_type||"General"),J(),uA("ngIf",I.center.status),J(8),Mt(I.center.address||"No address available"),J(8),Mt(I.center.contact||"No contact information available"),J(),uA("ngIf",I.center.capacity),J(5),uA("ngForOf",I.travelEstimates)}}var lc=(()=>{let N=class N{constructor(B,g,Q){this.modalCtrl=B,this.http=g,this.toastCtrl=Q,this.travelEstimates=[],this.selectedMode="foot-walking",this.isLoading=!0}ngOnInit(){return HA(this,null,function*(){this.isLoading=!0,yield this.calculateTravelTimes(),this.isLoading=!1})}calculateTravelTimes(){return HA(this,null,function*(){let B=[{id:"foot-walking",name:"Walking",icon:"walk-outline",color:"primary"},{id:"cycling-regular",name:"Cycling",icon:"bicycle-outline",color:"success"},{id:"driving-car",name:"Driving",icon:"car-outline",color:"danger"}];this.travelEstimates=[];let g=Number(this.center.latitude),Q=Number(this.center.longitude);if(console.log("Calculating travel times with coordinates:",{userLat:this.userLat,userLng:this.userLng,centerLat:g,centerLng:Q}),isNaN(g)||isNaN(Q)||isNaN(this.userLat)||isNaN(this.userLng)){console.error("Invalid coordinates for travel time calculations:",{userLat:this.userLat,userLng:this.userLng,centerLat:g,centerLng:Q}),this.toastCtrl.create({message:"Invalid coordinates. Using estimated travel times.",duration:3e3,color:"warning",position:"bottom"}).then(C=>C.present()),this.useFallbackCalculations(B);return}for(let C of B)try{let T=yield this.getTravelTimeEstimate(this.userLat,this.userLng,g,Q,C.id);this.travelEstimates.push({mode:C.id,time:T.time,distance:T.distance,icon:C.icon,color:C.color})}catch(T){if(console.error(`Error calculating ${C.name} time:`,T),this.travelEstimates.length===0){let Z="Using estimated travel times due to connection issues";T.message&&(T.message.includes("Invalid coordinates")?Z="Invalid coordinates. Using estimated travel times.":T.message.includes("API Error")&&(Z=`${T.message}. Using estimated travel times.`)),this.toastCtrl.create({message:Z,duration:3e3,color:"warning",position:"bottom"}).then(R=>R.present())}let x=this.calculateStraightLineDistance(this.userLat,this.userLng,g,Q),b;switch(C.id){case"foot-walking":b=5e3/3600;break;case"cycling-regular":b=15e3/3600;break;case"driving-car":b=4e4/3600;break;default:b=5e3/3600}let F=x/b;this.travelEstimates.push({mode:C.id,time:F,distance:x,icon:C.icon,color:C.color})}})}useFallbackCalculations(B){let g=Number(this.center.latitude),Q=Number(this.center.longitude),C=isNaN(this.userLat)?10.3157:this.userLat,T=isNaN(this.userLng)?123.8854:this.userLng,x=isNaN(g)?10.3257:g,b=isNaN(Q)?123.8954:Q,F=this.calculateStraightLineDistance(C,T,x,b);console.log(`Using fallback calculation with distance: ${F} meters`);for(let Z of B){let R;switch(Z.id){case"foot-walking":R=5e3/3600;break;case"cycling-regular":R=15e3/3600;break;case"driving-car":R=4e4/3600;break;default:R=5e3/3600}let X=F/R;this.travelEstimates.push({mode:Z.id,time:X,distance:F,icon:Z.icon,color:Z.color})}}getTravelTimeEstimate(B,g,Q,C,T){return HA(this,null,function*(){let x=`https://api.openrouteservice.org/v2/directions/${T}/json`;if([B,g,Q,C].some(F=>typeof F!="number"||isNaN(F)))throw console.error("Invalid coordinates for travel time estimate:",{startLat:B,startLng:g,endLat:Q,endLng:C}),new Error("Invalid coordinates");if(Math.abs(B)>90||Math.abs(Q)>90||Math.abs(g)>180||Math.abs(C)>180)throw console.error("Coordinates out of range for travel time estimate:",{startLat:B,startLng:g,endLat:Q,endLng:C}),new Error("Coordinates out of range");let b={coordinates:[[g,B],[C,Q]]};console.log(`Calculating route from [${B}, ${g}] to [${Q}, ${C}] using mode: ${T}`),console.log("Request body:",b);try{let F=yield sn(this.http.post(x,b,{headers:{Authorization:Ze.orsApiKey,"Content-Type":"application/json"}}));if(console.log(`Received response for ${T} route:`,F),F&&F.routes&&F.routes[0]&&F.routes[0].summary)return{time:F.routes[0].summary.duration,distance:F.routes[0].summary.distance};throw console.error("Invalid response format:",F),new Error("Invalid response format")}catch(F){throw console.error(`Failed to fetch ${T} route from OpenRouteService:`,F),F.error&&F.error.error?(console.error("API error details:",F.error),new Error(`API Error: ${F.error.error.message||F.error.error}`)):F}})}calculateStraightLineDistance(B,g,Q,C){let x=B*Math.PI/180,b=Q*Math.PI/180,F=(Q-B)*Math.PI/180,Z=(C-g)*Math.PI/180,R=Math.sin(F/2)*Math.sin(F/2)+Math.cos(x)*Math.cos(b)*Math.sin(Z/2)*Math.sin(Z/2);return 6371e3*(2*Math.atan2(Math.sqrt(R),Math.sqrt(1-R)))}formatTime(B){let g=Math.round(B/60);if(g<60)return`${g} min`;{let Q=Math.floor(g/60),C=g%60;return`${Q} hr ${C} min`}}formatDistance(B){return B<1e3?`${Math.round(B)} m`:`${(B/1e3).toFixed(2)} km`}selectTravelMode(B){this.selectedMode=B,this.dismiss(B)}dismiss(B){this.modalCtrl.dismiss({selectedMode:B||null})}getDisasterTypeIcon(B){if(!B)return"alert-circle-outline";let g=B.toLowerCase();return g.includes("earthquake")||g.includes("quake")?"earth-outline":g.includes("flood")||g.includes("flash")?"water-outline":g.includes("typhoon")||g.includes("storm")?"thunderstorm-outline":g.includes("fire")?"flame-outline":"alert-circle-outline"}getStatusColor(B){if(!B)return"medium";let g=B.toLowerCase();return g.includes("active")||g.includes("open")?"success":g.includes("inactive")||g.includes("closed")?"warning":g.includes("full")?"danger":"medium"}};N.\u0275fac=function(g){return new(g||N)(ho(wo),ho(Bo),ho(mo))},N.\u0275cmp=an({type:N,selectors:[["app-evacuation-center-details"]],inputs:{center:"center",userLat:"userLat",userLng:"userLng"},decls:14,vars:2,consts:[[1,"ion-no-border"],["slot","start"],[3,"click"],["name","chevron-back-outline","slot","icon-only"],[1,"ion-padding"],["class","loading-container",4,"ngIf"],["class","details-container",4,"ngIf"],["expand","block","color","medium",3,"click"],[1,"loading-container"],["name","circles"],[1,"details-container"],[1,"center-image"],["src","assets/earthquake.png",3,"alt",4,"ngIf"],["src","assets/flood.png",3,"alt",4,"ngIf"],["src","assets/typhoon.png",3,"alt",4,"ngIf"],["src","assets/ALERTO.png",3,"alt",4,"ngIf"],[1,"center-name"],[1,"center-type"],[3,"color"],[3,"name"],[3,"color",4,"ngIf"],[1,"info-section"],["lines","none"],["name","location-outline","slot","start","color","primary"],["name","call-outline","slot","start","color","primary"],["class","info-section",4,"ngIf"],[1,"travel-section"],[1,"travel-cards"],["class","travel-card",3,"click",4,"ngFor","ngForOf"],["src","assets/earthquake.png",3,"alt"],["src","assets/flood.png",3,"alt"],["src","assets/typhoon.png",3,"alt"],["src","assets/ALERTO.png",3,"alt"],["name","information-circle-outline"],["name","people-outline","slot","start","color","primary"],[1,"travel-card",3,"click"],[1,"travel-icon",3,"name","color"],[1,"travel-info"],[1,"travel-time"],["name","time-outline"],[1,"travel-distance"],["name","navigate-outline"],["expand","block","fill","clear",3,"click","color"],["name","arrow-forward-outline","slot","end"]],template:function(g,Q){g&1&&(G(0,"ion-header",0)(1,"ion-toolbar")(2,"ion-buttons",1)(3,"ion-button",2),at("click",function(){return Q.dismiss()}),wA(4,"ion-icon",3),Y()(),G(5,"ion-title"),hA(6,"Evacuation Center"),Y()()(),G(7,"ion-content",4),Ct(8,nh,4,0,"div",5)(9,hh,36,13,"div",6),Y(),G(10,"ion-footer",0)(11,"ion-toolbar")(12,"ion-button",7),at("click",function(){return Q.dismiss()}),hA(13," Back to Map "),Y()()()),g&2&&(J(8),uA("ngIf",Q.isLoading),J(),uA("ngIf",!Q.isLoading))},dependencies:[dn,un,ka,Na,Ra,Ga,Va,Za,go,Xa,Ja,hn,po,fn,$a,ja,Ac,ln,fo,cn],styles:["ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]{--background: transparent;--border-color: transparent}.loading-container[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;justify-content:center;height:200px}.loading-container[_ngcontent-%COMP%]   ion-spinner[_ngcontent-%COMP%]{width:48px;height:48px;margin-bottom:16px}.loading-container[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{color:var(--ion-color-medium)}.details-container[_ngcontent-%COMP%]{padding-bottom:20px}.center-image[_ngcontent-%COMP%]{width:100%;height:180px;border-radius:12px;overflow:hidden;margin-bottom:16px;background-color:#f5f5f5;display:flex;justify-content:center;align-items:center}.center-image[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{max-width:100%;max-height:100%;object-fit:contain;padding:16px}.center-name[_ngcontent-%COMP%]{font-size:24px;font-weight:700;margin:0 0 8px;color:var(--ion-color-dark)}.center-type[_ngcontent-%COMP%]{display:flex;gap:8px;margin-bottom:16px;flex-wrap:wrap}.info-section[_ngcontent-%COMP%]{margin-bottom:16px}.info-section[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{--padding-start: 0;--inner-padding-end: 0;--background: transparent}.info-section[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:24px}.info-section[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{font-size:16px;font-weight:600;margin-bottom:4px}.info-section[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{font-size:14px;color:var(--ion-color-medium)}.travel-section[_ngcontent-%COMP%]{margin-top:24px}.travel-section[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{font-size:18px;font-weight:600;margin-bottom:16px;color:var(--ion-color-dark)}.travel-section[_ngcontent-%COMP%]   .travel-cards[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:12px}.travel-section[_ngcontent-%COMP%]   .travel-cards[_ngcontent-%COMP%]   .travel-card[_ngcontent-%COMP%]{margin:0;border-radius:12px;box-shadow:0 4px 12px #00000014;transition:transform .2s ease,box-shadow .2s ease}.travel-section[_ngcontent-%COMP%]   .travel-cards[_ngcontent-%COMP%]   .travel-card[_ngcontent-%COMP%]:active{transform:scale(.98);box-shadow:0 2px 8px #0000001a}.travel-section[_ngcontent-%COMP%]   .travel-cards[_ngcontent-%COMP%]   .travel-card[_ngcontent-%COMP%]   ion-card-header[_ngcontent-%COMP%]{display:flex;align-items:center;padding:12px 16px 0}.travel-section[_ngcontent-%COMP%]   .travel-cards[_ngcontent-%COMP%]   .travel-card[_ngcontent-%COMP%]   ion-card-header[_ngcontent-%COMP%]   .travel-icon[_ngcontent-%COMP%]{font-size:28px;margin-right:12px}.travel-section[_ngcontent-%COMP%]   .travel-cards[_ngcontent-%COMP%]   .travel-card[_ngcontent-%COMP%]   ion-card-header[_ngcontent-%COMP%]   ion-card-title[_ngcontent-%COMP%]{font-size:18px;font-weight:600}.travel-section[_ngcontent-%COMP%]   .travel-cards[_ngcontent-%COMP%]   .travel-card[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]{padding:12px 16px 16px}.travel-section[_ngcontent-%COMP%]   .travel-cards[_ngcontent-%COMP%]   .travel-card[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]   .travel-info[_ngcontent-%COMP%]{display:flex;justify-content:space-between;margin-bottom:12px}.travel-section[_ngcontent-%COMP%]   .travel-cards[_ngcontent-%COMP%]   .travel-card[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]   .travel-info[_ngcontent-%COMP%]   .travel-time[_ngcontent-%COMP%], .travel-section[_ngcontent-%COMP%]   .travel-cards[_ngcontent-%COMP%]   .travel-card[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]   .travel-info[_ngcontent-%COMP%]   .travel-distance[_ngcontent-%COMP%]{display:flex;align-items:center}.travel-section[_ngcontent-%COMP%]   .travel-cards[_ngcontent-%COMP%]   .travel-card[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]   .travel-info[_ngcontent-%COMP%]   .travel-time[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%], .travel-section[_ngcontent-%COMP%]   .travel-cards[_ngcontent-%COMP%]   .travel-card[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]   .travel-info[_ngcontent-%COMP%]   .travel-distance[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:18px;margin-right:6px;color:var(--ion-color-medium)}.travel-section[_ngcontent-%COMP%]   .travel-cards[_ngcontent-%COMP%]   .travel-card[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]   .travel-info[_ngcontent-%COMP%]   .travel-time[_ngcontent-%COMP%]   span[_ngcontent-%COMP%], .travel-section[_ngcontent-%COMP%]   .travel-cards[_ngcontent-%COMP%]   .travel-card[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]   .travel-info[_ngcontent-%COMP%]   .travel-distance[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{font-size:15px;font-weight:500;color:var(--ion-color-dark)}.travel-section[_ngcontent-%COMP%]   .travel-cards[_ngcontent-%COMP%]   .travel-card[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%]{margin-top:8px;font-weight:500}ion-footer[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]{--background: transparent;--border-color: transparent;padding:0 16px 16px}"]});let v=N;return v})();function fh(v,N){if(v&1&&(G(0,"p"),hA(1),Y()),v&2){let I=yA();J(),Oa(" ",I.formatDistance(I.totalDistance)," \u2022 ",I.formatTime(I.totalDuration)," ")}}function dh(v,N){if(v&1&&(G(0,"span"),hA(1),Y()),v&2){let I=yA(2).$implicit,B=yA();J(),Tt(" \u2022 ",B.formatTime(I.duration),"")}}function Bh(v,N){if(v&1&&(G(0,"p"),hA(1),Ct(2,dh,2,1,"span",4),Y()),v&2){let I=yA().$implicit,B=yA();J(),Tt(" ",B.formatDistance(I.distance)," "),J(),uA("ngIf",I.duration>0)}}function gh(v,N){if(v&1&&(G(0,"ion-item",9),wA(1,"ion-icon",3),G(2,"ion-label"),wA(3,"h3",10),Ct(4,Bh,3,2,"p",4),Y(),G(5,"ion-note",11),hA(6),Y()()),v&2){let I=N.$implicit,B=N.index,g=yA();J(),uA("name",g.getDirectionIcon(I.type))("color",g.getTravelModeColor()),J(2),uA("innerHTML",I.instruction,Pa),J(),uA("ngIf",I.distance>0),J(2),Mt(B+1)}}var uc=(()=>{let N=class N{constructor(){this.directions=[],this.travelMode="foot-walking",this.totalDistance=null,this.totalDuration=null,this.close=new Ta}getTravelModeName(){switch(this.travelMode){case"foot-walking":return"Walking";case"cycling-regular":return"Cycling";case"driving-car":return"Driving";default:return"Traveling"}}getTravelModeIcon(){switch(this.travelMode){case"foot-walking":return"walk-outline";case"cycling-regular":return"bicycle-outline";case"driving-car":return"car-outline";default:return"navigate-outline"}}getTravelModeColor(){switch(this.travelMode){case"foot-walking":return"primary";case"cycling-regular":return"success";case"driving-car":return"danger";default:return"medium"}}formatTime(B){let g=Math.round(B/60);if(g<60)return`${g} min`;{let Q=Math.floor(g/60),C=g%60;return`${Q} hr ${C} min`}}formatDistance(B){return B<1e3?`${Math.round(B)} m`:`${(B/1e3).toFixed(2)} km`}closePanel(){this.close.emit()}getDirectionIcon(B){switch(B){case 0:return"arrow-forward-outline";case 1:return"arrow-forward-outline";case 2:return"arrow-forward-outline";case 3:return"arrow-forward-outline";case 4:return"arrow-back-outline";case 5:return"arrow-back-outline";case 6:return"arrow-back-outline";case 7:return"arrow-back-outline";case 8:return"arrow-down-outline";case 9:return"flag-outline";case 10:return"arrow-up-outline";case 11:return"arrow-forward-outline";case 12:return"arrow-forward-outline";case 13:return"arrow-forward-outline";case 14:return"arrow-forward-outline";case 15:return"flag-outline";default:return"navigate-outline"}}};N.\u0275fac=function(g){return new(g||N)},N.\u0275cmp=an({type:N,selectors:[["app-directions-panel"]],inputs:{directions:"directions",travelMode:"travelMode",totalDistance:"totalDistance",totalDuration:"totalDuration"},outputs:{close:"close"},decls:13,vars:5,consts:[[1,"directions-panel"],[1,"directions-header"],["lines","none"],["slot","start",3,"name","color"],[4,"ngIf"],["fill","clear","slot","end",3,"click"],["name","close-outline","slot","icon-only"],[1,"directions-list"],["lines","full",4,"ngFor","ngForOf"],["lines","full"],[3,"innerHTML"],["slot","end"]],template:function(g,Q){g&1&&(G(0,"div",0)(1,"div",1)(2,"ion-item",2),wA(3,"ion-icon",3),G(4,"ion-label")(5,"h2"),hA(6),Y(),Ct(7,fh,2,2,"p",4),Y(),G(8,"ion-button",5),at("click",function(){return Q.closePanel()}),wA(9,"ion-icon",6),Y()()(),G(10,"div",7)(11,"ion-list"),Ct(12,gh,7,5,"ion-item",8),Y()()()),g&2&&(J(3),uA("name",Q.getTravelModeIcon())("color",Q.getTravelModeColor()),J(3),Tt("",Q.getTravelModeName()," Directions"),J(),uA("ngIf",Q.totalDistance&&Q.totalDuration),J(5),uA("ngForOf",Q.directions))},dependencies:[dn,un,hn,po,fn,Ya,qa,ln,fo,cn],styles:[".directions-panel[_ngcontent-%COMP%]{position:absolute;bottom:0;left:0;right:0;background-color:#fff;border-top-left-radius:15px;border-top-right-radius:15px;box-shadow:0 -2px 10px #0000001a;max-height:50vh;overflow-y:auto;z-index:1000}.directions-header[_ngcontent-%COMP%]{padding:10px 0;border-bottom:1px solid #eee;position:sticky;top:0;background-color:#fff;z-index:1001}.directions-list[_ngcontent-%COMP%]{max-height:calc(50vh - 60px);overflow-y:auto}ion-item[_ngcontent-%COMP%]{--padding-start: 16px;--inner-padding-end: 16px}ion-icon[_ngcontent-%COMP%]{font-size:24px}h2[_ngcontent-%COMP%]{font-weight:600;margin:0}h3[_ngcontent-%COMP%]{font-size:14px;font-weight:500;margin:0}p[_ngcontent-%COMP%]{font-size:12px;color:var(--ion-color-medium);margin:4px 0 0}ion-note[_ngcontent-%COMP%]{font-size:12px;padding:4px 8px;border-radius:50%;background-color:var(--ion-color-light);color:var(--ion-color-dark);display:flex;align-items:center;justify-content:center;min-width:24px;min-height:24px}"]});let v=N;return v})();var fc=Ma(hc());function ph(v,N){if(v&1){let I=Ve();G(0,"div",16)(1,"ion-button",17),at("click",function(){me(I);let g=yA();return Ce(g.requestLocationExplicitly())}),wA(2,"ion-icon",18),hA(3," Enable Location Access "),Y(),G(4,"p",19),hA(5,"Tap the button above to enable location access"),Y()()}}function wh(v,N){v&1&&(G(0,"div",20),wA(1,"ion-icon",21),G(2,"p"),hA(3,"Showing your current location"),Y(),G(4,"small"),hA(5,"Search for evacuation centers or select a disaster type to see routes"),Y()())}function mh(v,N){if(v&1){let I=Ve();G(0,"div",22),at("click",function(){me(I);let g=yA();return Ce(g.showDirectionsPanel=!0)}),wA(1,"ion-icon",23),G(2,"div",24)(3,"strong"),hA(4),Y(),hA(5),G(6,"div",25),hA(7),Y()(),wA(8,"ion-icon",26),Y()}if(v&2){let I=yA();J(),uA("name",I.travelMode==="foot-walking"?"walk-outline":I.travelMode==="cycling-regular"?"bicycle-outline":"car-outline")("color",I.travelMode==="foot-walking"?"primary":I.travelMode==="cycling-regular"?"success":"danger"),J(3),Tt("",(I.routeTime/60).toFixed(0)," min"),J(),Tt(" \u2022 ",(I.routeDistance/1e3).toFixed(2)," km "),J(2),Mt(I.getTravelModeName())}}function Ch(v,N){if(v&1){let I=Ve();G(0,"app-directions-panel",27),at("close",function(){me(I);let g=yA();return Ce(g.showDirectionsPanel=!1)}),Y()}if(v&2){let I=yA();uA("directions",I.currentDirections)("travelMode",I.travelMode)("totalDistance",I.routeDistance)("totalDuration",I.routeTime)}}function vh(v,N){if(v&1&&(G(0,"div",28),wA(1,"ion-icon",7),G(2,"span"),hA(3),Y()()),v&2){let I=yA();J(),uA("name",I.currentDisasterType.toLowerCase().includes("earthquake")?"earth-outline":I.currentDisasterType.toLowerCase().includes("typhoon")?"thunderstorm-outline":I.currentDisasterType.toLowerCase().includes("flood")?"water-outline":"alert-circle-outline"),J(2),Tt("",I.currentDisasterType," Evacuation Centers")}}function Qh(v,N){if(v&1){let I=Ve();G(0,"ion-fab",29)(1,"ion-fab-button",30),at("click",function(){me(I);let g=yA();return Ce(g.routeToTwoNearestCenters())}),wA(2,"ion-icon",31),Y(),G(3,"ion-label",11),hA(4,"Route to Nearest Centers"),Y()()}}function _h(v,N){if(v&1){let I=Ve();G(0,"ion-fab",32)(1,"ion-fab-button",33),at("click",function(){me(I);let g=yA();return Ce(g.showDirectionsPanel=!0)}),wA(2,"ion-icon",34),Y(),G(3,"ion-label",11),hA(4,"Show Directions"),Y()()}}var af=(()=>{let N=class N{getTravelModeName(){switch(this.travelMode){case"foot-walking":return"Walking";case"cycling-regular":return"Cycling";case"driving-car":return"Driving";default:return"Traveling"}}findTwoNearestCenters(B,g,Q){if(!Q.length)return[];let C=Q;if(this.currentDisasterType&&this.currentDisasterType!=="all"){console.log(`Filtering centers by disaster type: ${this.currentDisasterType}`);let x=this.currentDisasterType.toLowerCase();if(C=Q.filter(b=>{if(!b.disaster_type)return!1;let F=b.disaster_type.toLowerCase();return x==="earthquake"||x==="earthquakes"?F.includes("earthquake")||F.includes("quake"):x==="typhoon"||x==="typhoons"?F.includes("typhoon")||F.includes("storm")||F.includes("hurricane"):x==="flood"||x==="floods"?F.includes("flood")||F.includes("flash"):F===x}),console.log(`Filtered to ${C.length} centers for disaster type: ${this.currentDisasterType}`),C.length===0)return console.log(`No centers found for disaster type: ${this.currentDisasterType}`),[]}return[...C].sort((x,b)=>{let F=this.calculateDistance(B,g,Number(x.latitude),Number(x.longitude)),Z=this.calculateDistance(B,g,Number(b.latitude),Number(b.longitude));return F-Z}).slice(0,2)}updateRoute(){this.routeToTwoNearestCenters()}requestLocationExplicitly(){return HA(this,null,function*(){console.log("User explicitly requested location access via button click"),this.showLocationRequestButton=!1,yield this.loadingService.showLoading("Getting your location...");try{try{let C=yield te.checkPermissions();if(console.log("Permission status:",C),C.location!=="granted"){console.log("Requesting permissions explicitly...");let T=yield te.requestPermissions();if(console.log("Permission request result:",T),T.location!=="granted")throw new Error("Location permission denied")}}catch(C){console.log("Permission check failed, might be in browser:",C)}let B=yield te.getCurrentPosition({enableHighAccuracy:!0,timeout:3e4,maximumAge:0});console.log("Successfully got position:",B);let g=B.coords.latitude,Q=B.coords.longitude;yield this.loadingService.dismissLoading(),this.toastCtrl.create({message:"Location access successful!",duration:2e3,color:"success"}).then(C=>C.present()),this.gpsEnabled=!0,this.map?(this.userMarker?(this.userMarker.setLatLng([g,Q]),this.map.setView([g,Q],15)):this.updateUserMarker(g,Q),this.startWatchingPosition()):this.initializeMap(g,Q)}catch(B){console.error("Error getting location:",B),yield this.loadingService.dismissLoading(),this.showLocationRequestButton=!0,yield(yield this.alertCtrl.create({header:"Location Access Failed",message:"We couldn't access your location. Would you like to see help on enabling location access?",buttons:[{text:"Show Help",handler:()=>{this.showLocationHelp()}},{text:"Try Again",handler:()=>{this.requestLocationExplicitly()}},{text:"Cancel",role:"cancel"}]})).present()}})}showLocationHelp(){return HA(this,null,function*(){let B="To use location services:";navigator.userAgent.includes("Chrome")?B+='<br><br><b>Chrome:</b><br>1. Click the lock/info icon in the address bar<br>2. Select "Site settings"<br>3. Change Location permission to "Allow"<br>':navigator.userAgent.includes("Firefox")?B+='<br><br><b>Firefox:</b><br>1. Click the lock icon in the address bar<br>2. Select "Site Permissions"<br>3. Change "Access Your Location" to "Allow"<br>':navigator.userAgent.includes("Safari")?B+='<br><br><b>Safari:</b><br>1. Open Safari settings<br>2. Go to Websites > Location<br>3. Ensure this website is set to "Allow"<br>':B+="<br><br>Please enable location access for this website in your browser settings.",B+="<br><br>On mobile devices, also ensure that:<br>1. Your device location/GPS is turned on<br>2. The app has permission to access your location",yield(yield this.alertCtrl.create({header:"Location Services Help",message:B,buttons:[{text:"Try Again",handler:()=>{this.requestLocationExplicitly()}},{text:"OK",role:"cancel"}]})).present()})}routeToTwoNearestCenters(){return HA(this,null,function*(){try{if(!this.gpsEnabled){console.log("GPS is disabled, not calculating routes"),(yield this.toastCtrl.create({message:"Please enable GPS to see evacuation routes",duration:3e3,color:"warning"})).present();return}console.log("Forcing fresh GPS position check for routing...");try{let B=yield this.getCurrentPositionWithFallback(),g=B.coords.latitude,Q=B.coords.longitude;console.log(`Got fresh GPS position: [${g}, ${Q}]`),this.userMarker?(this.userMarker.setLatLng([g,Q]),this.map.setView([g,Q],15)):this.userMarker=$.marker([g,Q],{icon:$.icon({iconUrl:"assets/Location.png",iconSize:[32,32],iconAnchor:[16,32]})}).addTo(this.map);let C=g,T=Q;this.toastCtrl.create({message:"Using your current real-time location",duration:2e3,color:"success"}).then(b=>b.present()),console.log(`Using FRESH GPS coordinates for routing: [${C}, ${T}]`),(!this.evacuationCenters||this.evacuationCenters.length===0)&&(yield this.loadEvacuationCenters(C,T));let x=this.findTwoNearestCenters(C,T,this.evacuationCenters);if(x.length===0){(yield this.toastCtrl.create({message:"No evacuation centers found.",duration:3e3,color:"danger"})).present();return}console.log("Aggressively clearing ALL existing routes"),this.map.eachLayer(b=>{b instanceof $.GeoJSON&&(console.log("Removing existing route layer"),this.map.removeLayer(b))});for(let b of x){let F=Number(b.latitude),Z=Number(b.longitude);if(console.log(`Calculating route from [${C}, ${T}] to center: ${b.name} with disaster type: ${b.disaster_type}`),console.log(`Center coordinates: [${F}, ${Z}], types: [${typeof F}, ${typeof Z}]`),isNaN(F)||isNaN(Z)){console.error("Invalid center coordinates:",{centerLat:F,centerLng:Z,center:b});continue}yield this.getRealRoute(C,T,F,Z,this.travelMode,b.disaster_type)}if(this.userMarker){let b="You are here!.";x.forEach((F,Z)=>{let R=this.calculateDistance(C,T,Number(F.latitude),Number(F.longitude));b+=`<br> \u2022 <strong>${F.name}</strong> <br> Distance: ${(R/1e3).toFixed(2)} km`}),this.userMarker.bindPopup(b).openPopup()}}catch(B){console.error("Failed to get fresh GPS position:",B),(yield this.toastCtrl.create({message:"Could not get your current location. Please check your GPS settings.",duration:3e3,color:"danger"})).present();return}}catch(B){(yield this.toastCtrl.create({message:"Failed to get your location or route.",duration:3e3,color:"danger"})).present(),console.error("Failed to route to two nearest centers",B)}})}constructor(){this.travelMode="foot-walking",this.routeTime=null,this.routeDistance=null,this.userMarker=null,this.directionIndicator=null,this.evacuationCenters=[],this.gpsEnabled=!0,this.loadingService=mi(ec),this.alertCtrl=mi(tc),this.toastCtrl=mi(mo),this.modalCtrl=mi(wo),this.http=mi(Bo),this.watchId=null,this.orientationListener=null,this.currentHeading=0,this.showCompass=!0,this.currentDisasterType="all",this.isFilterMode=!1,this.currentDirections=[],this.showDirectionsPanel=!1,this.showLocationRequestButton=!1,this.ORS_API_KEY=Ze.orsApiKey,this.route=mi(Da),this.pulsatingMarker=null,this.directionPointerLine=null,this.directionPointerCircle=null}toggleGps(B){return HA(this,null,function*(){if(console.log("GPS toggle:",B.detail.checked),this.gpsEnabled=B.detail.checked,this.gpsEnabled){console.log("Enabling GPS tracking...");try{let g=yield this.getCurrentPositionWithFallback();console.log("Position on toggle:",g);let Q=g.coords.latitude,C=g.coords.longitude;this.userMarker?(this.userMarker.setLatLng([Q,C]),this.userMarker.addTo(this.map)):this.updateUserMarker(Q,C),this.map.setView([Q,C],15),this.startWatchingPosition()}catch(g){console.error("Error enabling GPS:",g),this.gpsEnabled=!1,(yield this.toastCtrl.create({message:"Failed to enable GPS. Please check your location settings.",duration:3e3,color:"danger"})).present()}}else if(console.log("Disabling GPS tracking..."),this.userMarker&&this.userMarker.remove(),this.watchId){if(typeof this.watchId=="string")try{let g=this.watchId;te.clearWatch({id:g})}catch(g){console.log("Error clearing Capacitor watch:",g)}else if(typeof this.watchId=="number")try{navigator.geolocation.clearWatch(this.watchId)}catch(g){console.log("Error clearing browser watch:",g)}this.watchId=null}})}ngOnInit(){return HA(this,null,function*(){this.route.queryParams.subscribe(B=>{if(B.disasterType&&B.filterMode==="true"){let g=B.disasterType;this.currentDisasterType=g,this.isFilterMode=!0,console.log(`Navigated from home with disaster type filter: ${g}`),this.loadMapWithDisasterFilter(g);return}if(B.lat&&B.lng){let g=parseFloat(B.lat),Q=parseFloat(B.lng),C=B.name||"Selected Location",T=B.directions==="true",x=B.viewOnly==="true";if(console.log(`Navigated from search to coordinates: [${g}, ${Q}], name: ${C}`),console.log(`Get directions: ${T}, View only: ${x}`),!isNaN(g)&&!isNaN(Q)){this.loadMapWithSearchLocation(g,Q,C,T);return}}this.loadMapWithOnlyUserLocation()})})}loadMapWithSearchLocation(B,g,Q,C=!1){return HA(this,null,function*(){yield this.loadingService.showLoading("Loading selected location...");try{if(console.log(`Initializing map with search location: [${B}, ${g}], name: ${Q}`),this.isFilterMode=!1,this.currentDisasterType="all",this.initializeMap(B,g),this.map.eachLayer(x=>{x instanceof $.Marker&&x!==this.userMarker&&this.map.removeLayer(x),x instanceof $.GeoJSON&&this.map.removeLayer(x)}),$.marker([B,g],{icon:$.icon({iconUrl:"assets/Typhoons.png",iconSize:[32,32],iconAnchor:[16,32]})}).addTo(this.map).bindPopup(`<b>${Q}</b><br>Selected evacuation center`).openPopup(),this.gpsEnabled)try{let x=yield this.getCurrentPositionWithFallback(),b=x.coords.latitude,F=x.coords.longitude;if(this.updateUserMarker(b,F),C){this.map.eachLayer(R=>{R instanceof $.GeoJSON&&this.map.removeLayer(R)}),yield this.getRealRoute(b,F,B,g,this.travelMode),this.toastCtrl.create({message:`Showing directions to ${Q}`,duration:3e3,color:"success"}).then(R=>R.present());let Z=$.latLngBounds([[b,F],[B,g]]);this.map.fitBounds(Z,{padding:[50,50]})}else this.toastCtrl.create({message:`Showing ${Q} on map`,duration:2e3,color:"primary"}).then(Z=>Z.present())}catch(x){console.error("Error getting user location for routing:",x),C&&this.toastCtrl.create({message:"Could not get your location to calculate directions. Please check your GPS settings.",duration:3e3,color:"warning"}).then(b=>b.present())}else C&&this.toastCtrl.create({message:"Please enable GPS to get directions",duration:3e3,color:"warning"}).then(x=>x.present());yield this.loadingService.dismissLoading()}catch(T){console.error("Error loading search location:",T),yield this.loadingService.dismissLoading(),this.toastCtrl.create({message:"Failed to load selected location. Please try again.",duration:3e3,color:"danger"}).then(x=>x.present()),this.loadMapWithUserLocation()}})}getCurrentPositionWithFallback(){return HA(this,null,function*(){try{console.log("Trying Capacitor Geolocation...");try{let B=yield te.checkPermissions();if(console.log("Permission status:",B),B.location!=="granted"){console.log("Requesting permissions explicitly...");let g=yield te.requestPermissions();if(console.log("Permission request result:",g),g.location!=="granted")throw new Error("Location permission denied")}}catch(B){console.log("Permission check failed, might be in browser:",B)}try{return console.log("Getting current position via Capacitor..."),yield te.getCurrentPosition({enableHighAccuracy:!0,timeout:1e4})}catch(B){throw console.log("Capacitor Geolocation failed, trying browser fallback:",B),B}}catch{if(console.log("Trying browser geolocation fallback..."),navigator.geolocation)return new Promise((g,Q)=>{navigator.geolocation.getCurrentPosition(C=>{console.log("Browser geolocation succeeded:",C),g({coords:{latitude:C.coords.latitude,longitude:C.coords.longitude,accuracy:C.coords.accuracy,altitude:C.coords.altitude,altitudeAccuracy:C.coords.altitudeAccuracy,heading:C.coords.heading,speed:C.coords.speed},timestamp:C.timestamp})},C=>{if(console.error("Browser geolocation failed:",C),C.code===1&&C.message.includes("secure origins")){let T=new Error("Geolocation requires HTTPS. Please use a secure connection, run on a real device, or enable insecure origins in Chrome flags.");T.code=C.code,Q(T)}else Q(C)},{enableHighAccuracy:!0,timeout:1e4})});throw console.error("Geolocation not available in this browser"),new Error("Geolocation not available in this browser")}})}loadMapWithDisasterFilter(B){return HA(this,null,function*(){yield this.loadingService.showLoading(`Loading ${B==="all"?"all evacuation centers":B+" evacuation centers"}...`);try{console.log("Getting user location for disaster map...");try{let g=yield this.getCurrentPositionWithFallback();console.log("Position received:",g);let Q=g.coords.latitude,C=g.coords.longitude;console.log(`Initializing disaster map with real GPS coordinates: [${Q}, ${C}]`),this.initializeMap(Q,C),this.startWatchingPosition(),yield this.loadEvacuationCentersFiltered(Q,C,B),this.toastCtrl.create({message:`Showing ${B==="all"?"all evacuation centers":B+" evacuation centers"} near you`,duration:3e3,color:"primary"}).then(T=>T.present()),yield this.loadingService.dismissLoading();return}catch(g){console.error("Failed to get GPS position for disaster map:",g),yield this.loadingService.dismissLoading(),yield(yield this.alertCtrl.create({header:"GPS Required",message:"We need your location to show nearby evacuation centers. Please enable GPS and try again.",buttons:[{text:"Enable GPS",handler:()=>{this.loadMapWithDisasterFilter(B)}},{text:"Cancel",role:"cancel"}]})).present()}}catch(g){console.error("Error loading disaster map:",g),yield this.loadingService.dismissLoading(),this.toastCtrl.create({message:"Failed to load evacuation centers. Please try again.",duration:3e3,color:"danger"}).then(Q=>Q.present())}})}loadMapWithOnlyUserLocation(){return HA(this,null,function*(){yield this.loadingService.showLoading("Loading map...");try{console.log("Getting user location for map tab...");try{let B=yield this.getCurrentPositionWithFallback();console.log("Position received:",B);let g=B.coords.latitude,Q=B.coords.longitude;console.log(`Initializing map with only user location: [${g}, ${Q}]`),this.isFilterMode=!1,this.currentDisasterType="all",this.evacuationCenters=[],this.initializeMap(g,Q),this.map.eachLayer(C=>{C instanceof $.Marker&&C!==this.userMarker&&this.map.removeLayer(C),C instanceof $.GeoJSON&&this.map.removeLayer(C)}),this.startWatchingPosition(),this.userMarker&&this.userMarker.bindPopup("You are here!").openPopup(),this.toastCtrl.create({message:"Showing your current location",duration:2e3,color:"success"}).then(C=>C.present()),yield this.loadingService.dismissLoading();return}catch(B){console.error("Failed to get GPS position for map tab:",B),yield this.loadingService.dismissLoading(),yield(yield this.alertCtrl.create({header:"Location Required",message:"We need your location to show the map. Please enable GPS and try again.",buttons:[{text:"Enable GPS",handler:()=>{this.loadMapWithOnlyUserLocation()}},{text:"Cancel",role:"cancel"}]})).present();return}}catch(B){console.error("Error loading map:",B),yield this.loadingService.dismissLoading(),this.toastCtrl.create({message:"Failed to load map. Please try again.",duration:3e3,color:"danger"}).then(g=>g.present())}})}loadMapWithUserLocation(){return HA(this,null,function*(){yield this.loadingService.showLoading("Loading map...");try{console.log("Getting user location...");try{let B=yield this.getCurrentPositionWithFallback();console.log("Position received:",B);let g=B.coords.latitude,Q=B.coords.longitude;console.log(`Initializing map with real GPS coordinates: [${g}, ${Q}]`),this.isFilterMode||(this.currentDisasterType="all"),this.initializeMap(g,Q),this.startWatchingPosition(),this.toastCtrl.create({message:"Using your real-time location",duration:2e3,color:"success"}).then(C=>C.present()),yield this.loadingService.dismissLoading();return}catch(B){throw console.error("Failed to get GPS position, showing alert:",B),B}}catch(B){console.error("Error getting location",B);let g="Unable to access your location. ";B.code===1?navigator.userAgent.includes("Chrome")?g+='Location permission denied. Please click the lock icon in the address bar, select "Site settings", and change Location permission to "Allow".':navigator.userAgent.includes("Firefox")?g+='Location permission denied. Please click the lock icon in the address bar, select "Site Permissions", and change "Access Your Location" to "Allow".':navigator.userAgent.includes("Safari")?g+='Location permission denied. Please check Safari settings > Websites > Location and ensure this website is set to "Allow".':g+="Location permission denied. Please enable location access for this website in your browser settings.":B.code===2?g+="Position unavailable. Your GPS signal might be weak or unavailable.":B.code===3?g+="Location request timed out. Please try again.":g+="Please enable GPS or try again. "+(B.message||""),yield(yield this.alertCtrl.create({header:"Location Error",message:g,buttons:[{text:"Retry",handler:()=>{this.loadMapWithUserLocation()}},{text:"Load Default Map",role:"cancel",handler:()=>{this.initializeMap(10.3157,123.8854)}}]})).present()}yield this.loadingService.dismissLoading()})}stopWatchingPosition(){if(this.watchId){if(console.log("Stopping position watch..."),typeof this.watchId=="string")try{let B=this.watchId;te.clearWatch({id:B})}catch(B){console.log("Error clearing Capacitor watch:",B)}else if(typeof this.watchId=="number")try{navigator.geolocation.clearWatch(this.watchId)}catch(B){console.log("Error clearing browser watch:",B)}this.watchId=null}}startWatchingPosition(){this.stopWatchingPosition(),console.log("Starting position watch...");try{this.watchId=te.watchPosition({enableHighAccuracy:!0,timeout:1e4},(B,g)=>{B&&this.gpsEnabled&&(console.log("Capacitor watch position update:",B),this.updateUserMarker(B.coords.latitude,B.coords.longitude)),g&&(console.error("Error watching position:",g),this.toastCtrl.create({message:"GPS signal lost or weak. Please check your location settings.",duration:3e3,color:"warning"}).then(Q=>Q.present()))}),console.log("Capacitor watch started with ID:",this.watchId)}catch(B){console.log("Capacitor watch failed, trying browser fallback:",B),navigator.geolocation?(this.watchId=navigator.geolocation.watchPosition(g=>{this.gpsEnabled&&(console.log("Browser watch position update:",g),this.updateUserMarker(g.coords.latitude,g.coords.longitude))},g=>{console.error("Browser watch error:",g),this.toastCtrl.create({message:"GPS signal lost or weak. Please check your location settings.",duration:3e3,color:"warning"}).then(Q=>Q.present())},{enableHighAccuracy:!0,timeout:1e4}),console.log("Browser watch started with ID:",this.watchId)):console.error("Geolocation watching not available")}}initializeMap(B,g){console.log(`Initializing map with coordinates: [${B}, ${g}]`),(isNaN(B)||isNaN(g)||Math.abs(B)>90||Math.abs(g)>180)&&(console.error("Invalid coordinates for map initialization:",{lat:B,lng:g}),B=12.8797,g=121.774,console.log(`Using fallback coordinates for Philippines: [${B}, ${g}]`)),this.map&&(console.log("Removing existing map"),this.map.remove()),this.map=$.map("map").setView([B,g],15),console.log("Map initialized"),$.tileLayer("https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png",{attribution:"OpenStreetMap contributors"}).addTo(this.map),console.log("Tile layer added"),this.gpsEnabled?(console.log("GPS is enabled, adding user marker"),this.userMarker?(this.userMarker.setLatLng([B,g]),this.userMarker.addTo(this.map),console.log("Updated existing user marker")):(this.userMarker=$.marker([B,g],{icon:$.icon({iconUrl:"assets/Location.png",iconSize:[32,32],iconAnchor:[16,32]})}).addTo(this.map).bindPopup("You are here!").openPopup(),console.log("Created new user marker")),this.startWatchingOrientation(),this.toastCtrl.create({message:"Using your real-time GPS location",duration:2e3,color:"success"}).then(Q=>Q.present())):console.log("GPS is disabled, not adding user marker"),this.isFilterMode||this.evacuationCenters.length>0?(console.log("Loading evacuation centers"),this.loadEvacuationCenters(B,g)):console.log("Skipping evacuation centers - showing only user location")}updateUserMarker(B,g){if(console.log(`Updating user marker to: [${B}, ${g}]`),isNaN(B)||isNaN(g)||Math.abs(B)>90||Math.abs(g)>180){console.error("Invalid coordinates for user marker update:",{lat:B,lng:g});return}if(this.userMarker){let Q=this.userMarker.getLatLng();this.userMarker.setLatLng([B,g]),this.map.setView([B,g]),console.log("Updated existing user marker position"),this.showCompass&&this.directionIndicator&&this.updateDirectionIndicator();let C=this.calculateDistance(Q.lat,Q.lng,B,g);console.log(`User moved ${C.toFixed(2)} meters from previous position`),C>20&&(console.log(`Significant movement detected (${C.toFixed(2)}m), recalculating routes`),this.map.eachLayer(T=>{T instanceof $.GeoJSON&&(console.log("Removing existing route layer"),this.map.removeLayer(T))}),this.evacuationCenters&&this.evacuationCenters.length>0&&(console.log("Recalculating routes to nearest evacuation centers"),this.routeToTwoNearestCenters()))}else this.userMarker=$.marker([B,g],{icon:$.icon({iconUrl:"assets/Location.png",iconSize:[32,32],iconAnchor:[16,32]})}).addTo(this.map).bindPopup("You are here!").openPopup(),console.log("Created new user marker"),this.evacuationCenters&&this.evacuationCenters.length>0&&(console.log("Calculating initial routes with real GPS data"),this.routeToTwoNearestCenters())}loadEvacuationCentersFiltered(B,g,Q){return HA(this,null,function*(){try{if(console.log(`Loading evacuation centers for disaster type: ${Q}`),console.log(`User coordinates: [${B}, ${g}]`),this.pulsatingMarker&&(this.map.removeLayer(this.pulsatingMarker),this.pulsatingMarker=null),isNaN(B)||isNaN(g)||Math.abs(B)>90||Math.abs(g)>180){console.error("Invalid user coordinates for loading evacuation centers:",{userLat:B,userLng:g}),(yield this.toastCtrl.create({message:"Invalid location coordinates. Please check your GPS settings.",duration:3e3,color:"danger"})).present();return}this.currentDisasterType=Q,console.log("Fetching evacuation centers from:",`${Ze.apiUrl}/evacuation-centers`);let C=yield sn(this.http.get(`${Ze.apiUrl}/evacuation-centers`));console.log("Received centers from API:",C),this.map.eachLayer(F=>{F instanceof $.Marker&&F!==this.userMarker&&this.map.removeLayer(F)}),this.map.eachLayer(F=>{F instanceof $.GeoJSON&&this.map.removeLayer(F)});let T=C||[];if(Q.toLowerCase()!=="all"){let F=Q.toLowerCase();T=T.filter(Z=>{if(!Z.disaster_type)return!1;let R=Z.disaster_type.toLowerCase();return F==="earthquake"||F==="earthquakes"?R.includes("earthquake")||R.includes("quake"):F==="typhoon"||F==="typhoons"?R.includes("typhoon")||R.includes("storm")||R.includes("hurricane"):F==="flood"||F==="floods"?R.includes("flood")||R.includes("flash"):R===F}),console.log(`Filtered to ${T.length} centers for disaster type: ${Q}`)}if(this.evacuationCenters=T,this.evacuationCenters.length===0){this.alertCtrl.create({header:"No Evacuation Centers Found",message:`There are no evacuation centers stored for ${Q==="all"?"any disaster type":Q}. Please contact your administrator to add evacuation centers.`,buttons:["OK"]}).then(F=>F.present()),this.userMarker&&this.userMarker.bindPopup("You are here!").openPopup(),this.map.setView([B,g],15);return}let x=this.findNearestCenter(B,g,this.evacuationCenters),b=null;if(this.evacuationCenters.forEach(F=>{let Z=Number(F.latitude),R=Number(F.longitude);if(console.log(`Processing center: ${F.name}, coordinates: [${Z}, ${R}]`),!isNaN(Z)&&!isNaN(R)){let X="assets/forTyphoon.png",IA="#008000";if(F.disaster_type){let fA=F.disaster_type.toLowerCase();fA.includes("earthquake")||fA.includes("quake")?(X="assets/forEarthquake.png",IA="#ffa500"):fA.includes("flood")||fA.includes("flash")?(X="assets/forFlood.png",IA="#0000ff"):(fA.includes("typhoon")||fA.includes("storm")||fA.includes("hurricane"))&&(X="assets/forTyphoon.png",IA="#008000")}let j=$.marker([Z,R],{icon:$.icon({iconUrl:X,iconSize:[40,40],iconAnchor:[20,40],popupAnchor:[0,-40]})}),PA=`
            <div class="evacuation-popup">
              <h3>${F.name||"Evacuation Center"}</h3>
              <p><strong>Type:</strong> ${F.disaster_type||"General"}</p>
              <p><strong>Distance:</strong> ${(this.calculateDistance(B,g,Z,R)/1e3).toFixed(2)} km</p>
              <p><button class="popup-button">View Details</button></p>
            </div>
          `;if(j.bindPopup(PA),j.on("click",()=>{setTimeout(()=>{j.closePopup(),this.showEvacuationCenterDetails(F,B,g)},300)}),j.addTo(this.map),console.log(`Added marker for center: ${F.name}`),x&&F.name===x.name&&F.latitude===x.latitude&&F.longitude===x.longitude){b=j;let fA=$.divIcon({html:`<div class="marker-pulse" style="background-color: ${IA};"></div>`,className:"marker-pulse-container",iconSize:[60,60],iconAnchor:[30,30]});this.pulsatingMarker&&this.map.removeLayer(this.pulsatingMarker),console.log(`Creating pulsating marker for ${F.name} at [${Z}, ${R}] with color ${IA}`),this.pulsatingMarker=$.marker([Z,R],{icon:fA,zIndexOffset:500}).addTo(this.map),console.log(`Added pulsating effect to nearest center: ${F.name} with color ${IA}`)}}else console.error(`Invalid coordinates for center: ${F.name}`)}),this.gpsEnabled&&this.userMarker&&this.evacuationCenters.length>0){console.log("GPS enabled and user marker exists, finding nearest centers");let F=this.findTwoNearestCenters(B,g,this.evacuationCenters);if(F.length>0){for(let X of F){let IA=Number(X.latitude),j=Number(X.longitude);if(console.log(`Calculating route to center: ${X.name}`),console.log(`Center coordinates: [${IA}, ${j}], types: [${typeof IA}, ${typeof j}]`),isNaN(IA)||isNaN(j)){console.error("Invalid center coordinates:",{centerLat:IA,centerLng:j,center:X});continue}yield this.getRealRoute(B,g,IA,j,this.travelMode,X.disaster_type)}let Z="You are here!.";F.forEach((X,IA)=>{let j=this.calculateDistance(B,g,Number(X.latitude),Number(X.longitude));Z+=`<br> \u2022 <strong>${IA+1}: ${X.name} </strong> <br> Distance: ${(j/1e3).toFixed(2)} km`}),this.userMarker.bindPopup(Z).openPopup();let R=$.latLngBounds([]);R.extend([B,g]),F.forEach(X=>{R.extend([Number(X.latitude),Number(X.longitude)])}),this.map.fitBounds(R,{padding:[50,50]})}else console.log("No nearest centers found"),this.map.setView([B,g],15)}else console.log("GPS disabled, no user marker, or no centers found, skipping route calculation"),this.map.setView([B,g],15)}catch(C){console.error("Error loading filtered evacuation centers:",C),this.toastCtrl.create({message:"Failed to load evacuation centers. Please try again.",duration:3e3,color:"danger"}).then(T=>T.present())}})}loadEvacuationCenters(B,g){return HA(this,null,function*(){try{if(console.log(`Loading evacuation centers with user coordinates: [${B}, ${g}]`),this.pulsatingMarker&&(this.map.removeLayer(this.pulsatingMarker),this.pulsatingMarker=null),isNaN(B)||isNaN(g)||Math.abs(B)>90||Math.abs(g)>180){console.error("Invalid user coordinates for loading evacuation centers:",{userLat:B,userLng:g}),(yield this.toastCtrl.create({message:"Invalid location coordinates. Please check your GPS settings.",duration:3e3,color:"danger"})).present();return}console.log("Fetching evacuation centers from:",`${Ze.apiUrl}/evacuation-centers`);let Q=yield sn(this.http.get(`${Ze.apiUrl}/evacuation-centers`));console.log("Received centers from API:",Q),this.evacuationCenters=Q||[],this.map.eachLayer(x=>{x instanceof $.Marker&&x!==this.userMarker&&this.map.removeLayer(x)});let C=this.findNearestCenter(B,g,this.evacuationCenters),T=null;if(this.evacuationCenters.forEach(x=>{let b=Number(x.latitude),F=Number(x.longitude);if(console.log(`Processing center: ${x.name}, coordinates: [${b}, ${F}]`),!isNaN(b)&&!isNaN(F)){let Z="assets/forTyphoon.png",R="#008000";if(x.disaster_type){let j=x.disaster_type.toLowerCase();j.includes("earthquake")||j.includes("quake")?(Z="assets/forEarthquake.png",R="#ffa500"):j.includes("flood")||j.includes("flash")?(Z="assets/forFlood.png",R="#0000ff"):(j.includes("typhoon")||j.includes("storm")||j.includes("hurricane"))&&(Z="assets/forTyphoon.png",R="#008000")}let X=$.marker([b,F],{icon:$.icon({iconUrl:Z,iconSize:[40,40],iconAnchor:[20,40],popupAnchor:[0,-40]})}),IA=`
            <div class="evacuation-popup">
              <h3>${x.name||"Evacuation Center"}</h3>
              <p><strong>Distance:</strong> ${(this.calculateDistance(B,g,b,F)/1e3).toFixed(2)} km</p>
              <p><button class="popup-button">View Details</button></p>
            </div>
          `;if(X.bindPopup(IA),X.on("click",()=>{setTimeout(()=>{X.closePopup(),this.showEvacuationCenterDetails(x,B,g)},300)}),X.addTo(this.map),console.log(`Added marker for center: ${x.name}`),C&&x.name===C.name&&x.latitude===C.latitude&&x.longitude===C.longitude){T=X;let j=$.divIcon({html:`<div class="marker-pulse" style="background-color: ${R};"></div>`,className:"marker-pulse-container",iconSize:[60,60],iconAnchor:[30,30]});this.pulsatingMarker&&this.map.removeLayer(this.pulsatingMarker),console.log(`Creating pulsating marker for ${x.name} at [${b}, ${F}] with color ${R}`),this.pulsatingMarker=$.marker([b,F],{icon:j,zIndexOffset:500}).addTo(this.map),console.log(`Added pulsating effect to nearest center: ${x.name} with color ${R}`)}}else console.error(`Invalid coordinates for center: ${x.name}`)}),this.gpsEnabled&&this.userMarker){console.log("GPS enabled and user marker exists, finding nearest centers");let x=this.findTwoNearestCenters(B,g,this.evacuationCenters);if(x.length>0){this.map.eachLayer(F=>{F instanceof $.GeoJSON&&this.map.removeLayer(F)});for(let F of x){let Z=Number(F.latitude),R=Number(F.longitude);if(console.log(`Calculating route to center: ${F.name}`),console.log(`Center coordinates: [${Z}, ${R}], types: [${typeof Z}, ${typeof R}]`),isNaN(Z)||isNaN(R)){console.error("Invalid center coordinates:",{centerLat:Z,centerLng:R,center:F});continue}yield this.getRealRoute(B,g,Z,R,this.travelMode,F.disaster_type)}let b="You are here!.";x.forEach((F,Z)=>{let R=this.calculateDistance(B,g,Number(F.latitude),Number(F.longitude));b+=`<br> \u2022${Z+1}: ${F.name} <br> Distance: ${(R/1e3).toFixed(2)} km`}),this.userMarker.bindPopup(b).openPopup()}else console.log("No nearest centers found"),this.map.setView([B,g],15)}else console.log("GPS disabled or no user marker, skipping route calculation"),this.map.setView([B,g],15)}catch(Q){console.error("Failed to load evacuation centers",Q)}})}getRealRoute(b,F,Z,R){return HA(this,arguments,function*(B,g,Q,C,T=this.travelMode,x){let X=`https://api.openrouteservice.org/v2/directions/${T}/geojson`;if(console.log("Clearing all existing routes before calculating new route"),this.map.eachLayer(j=>{j instanceof $.GeoJSON&&(console.log("Removing existing route layer"),this.map.removeLayer(j))}),console.log("Requesting route with coordinates:",{startLat:B,startLng:g,endLat:Q,endLng:C,travelMode:T}),[B,g,Q,C].some(j=>typeof j!="number"||isNaN(j))){(yield this.toastCtrl.create({message:"Invalid route coordinates. Cannot request directions.",duration:3e3,color:"danger"})).present();return}if(Math.abs(B)>90||Math.abs(Q)>90||Math.abs(g)>180||Math.abs(C)>180){(yield this.toastCtrl.create({message:"Route coordinates out of range. Cannot request directions.",duration:3e3,color:"danger"})).present();return}let IA={coordinates:[[g,B],[C,Q]]};console.log("Route request body:",IA);try{let PA=yield sn(this.http.post(X,IA,{headers:{Authorization:this.ORS_API_KEY,"Content-Type":"application/json"}})),fA="#3388ff";if(x){let KA=x.toLowerCase();KA.includes("earthquake")||KA.includes("quake")?fA="#ffa500":KA.includes("flood")||KA.includes("flash")?fA="#0000ff":(KA.includes("typhoon")||KA.includes("storm")||KA.includes("hurricane"))&&(fA="#008000")}if(console.log(`Route calculation - Disaster type: "${x}", Normalized type: "${x?x.toLowerCase():"none"}", Selected color: ${fA}`),this.currentDisasterType&&this.currentDisasterType!=="all"){let KA=this.currentDisasterType.toLowerCase();KA==="earthquake"||KA==="earthquakes"?fA="#ffa500":KA==="typhoon"||KA==="typhoons"?fA="#008000":(KA==="flood"||KA==="floods")&&(fA="#0000ff"),console.log(`Filter mode active: ${this.currentDisasterType}, forcing route color to: ${fA}`)}console.log(`Using route color: ${fA} for disaster type: ${x||"unknown"}`);let ve=$.geoJSON(PA,{style:{color:fA,weight:5,opacity:.8}}).addTo(this.map);PA&&PA.features&&PA.features[0]&&PA.features[0].properties&&PA.features[0].properties.summary?(this.routeTime=PA.features[0].properties.summary.duration,this.routeDistance=PA.features[0].properties.summary.distance):(this.routeTime=null,this.routeDistance=null),this.map.fitBounds(ve.getBounds(),{padding:[50,50]})}catch(j){console.error("Failed to fetch route from OpenRouteService",j);let PA="Failed to fetch route. Please check your internet connection or try again later.";j.error&&j.error.error?(PA=`Routing error: ${j.error.error.message||j.error.error}`,console.error("API error details:",j.error)):j.message&&(PA=`Routing error: ${j.message}`);let fA=T==="foot-walking"?"walking":T==="cycling-regular"?"cycling":T==="driving-car"?"driving":T;(yield this.toastCtrl.create({message:`Failed to fetch ${fA} route. ${PA}`,duration:3e3,color:"danger"})).present()}})}findNearestCenter(B,g,Q){if(!Q.length)return console.log("No centers available to find nearest"),null;let C=Q[0],T=this.calculateDistance(B,g,Number(C.latitude),Number(C.longitude));for(let x of Q){let b=this.calculateDistance(B,g,Number(x.latitude),Number(x.longitude));b<T&&(T=b,C=x)}return console.log(`Found nearest center: ${C.name} at distance ${(T/1e3).toFixed(2)} km with disaster type: ${C.disaster_type}`),C}calculateDistance(B,g,Q,C){let x=B*Math.PI/180,b=Q*Math.PI/180,F=(Q-B)*Math.PI/180,Z=(C-g)*Math.PI/180,R=Math.sin(F/2)*Math.sin(F/2)+Math.cos(x)*Math.cos(b)*Math.sin(Z/2)*Math.sin(Z/2);return 6371e3*(2*Math.atan2(Math.sqrt(R),Math.sqrt(1-R)))}downloadMap(){return HA(this,null,function*(){try{yield this.loadingService.showLoading("Capturing map...");let B=document.getElementById("map");if(!B)throw new Error("Map element not found");console.log("Capturing map as image...");let g=yield(0,fc.default)(B,{useCORS:!0,allowTaint:!0,scrollX:0,scrollY:0,windowWidth:document.documentElement.offsetWidth,windowHeight:document.documentElement.offsetHeight,scale:1});yield this.loadingService.dismissLoading();let Q=g.toDataURL("image/png"),x=`evacuation-map-${new Date().toISOString().replace(/[:.]/g,"-").substring(0,19)}.png`;yield(yield this.alertCtrl.create({header:"Map Captured",message:"Your map has been captured. What would you like to do with it?",buttons:[{text:"Download",handler:()=>{this.downloadImage(Q,x)}},{text:"Share",handler:()=>{this.shareImage(Q,x)}},{text:"Cancel",role:"cancel"}]})).present()}catch(B){console.error("Error capturing map:",B),yield this.loadingService.dismissLoading(),(yield this.toastCtrl.create({message:"Failed to capture map. Please try again.",duration:3e3,color:"danger"})).present()}})}downloadImage(B,g){let Q=document.createElement("a");Q.href=B,Q.download=g,document.body.appendChild(Q),Q.click(),document.body.removeChild(Q),this.toastCtrl.create({message:"Map downloaded successfully",duration:2e3,color:"success"}).then(C=>C.present())}shareImage(B,g){return HA(this,null,function*(){try{if(navigator.share){let Q=yield(yield fetch(B)).blob(),C=new File([Q],g,{type:"image/png"});yield navigator.share({title:"Evacuation Map",text:"Here is my evacuation map with routes to the nearest evacuation centers",files:[C]}),console.log("Map shared successfully")}else console.log("Web Share API not supported"),(yield this.toastCtrl.create({message:"Sharing not supported on this device. The map has been downloaded instead.",duration:3e3,color:"warning"})).present(),this.downloadImage(B,g)}catch(Q){console.error("Error sharing map:",Q),(yield this.toastCtrl.create({message:"Failed to share map. The map has been downloaded instead.",duration:3e3,color:"warning"})).present(),this.downloadImage(B,g)}})}showEvacuationCenterDetails(B,g,Q){return HA(this,null,function*(){console.log("Showing evacuation center details for:",B.name);let C=yield this.modalCtrl.create({component:lc,componentProps:{center:B,userLat:g,userLng:Q},cssClass:"evacuation-details-modal",breakpoints:[0,.5,.75,1],initialBreakpoint:.75});yield C.present();let{data:T}=yield C.onDidDismiss();if(T&&T.selectedMode&&(console.log("Selected travel mode:",T.selectedMode),this.travelMode=T.selectedMode,console.log("Clearing all existing routes before calculating new route"),this.map.eachLayer(x=>{x instanceof $.GeoJSON&&(console.log("Removing existing route layer"),this.map.removeLayer(x))}),this.userMarker)){let x=this.userMarker.getLatLng(),b=Number(B.latitude),F=Number(B.longitude);if(console.log("Recalculating route with new travel mode:",{userLat:x.lat,userLng:x.lng,centerLat:b,centerLng:F,travelMode:this.travelMode}),isNaN(b)||isNaN(F)){console.error("Invalid center coordinates:",{centerLat:b,centerLng:F}),(yield this.toastCtrl.create({message:"Invalid evacuation center coordinates. Cannot calculate route.",duration:3e3,color:"danger"})).present();return}this.toastCtrl.create({message:`Showing ${this.getTravelModeName().toLowerCase()} route to ${B.name}`,duration:2e3,color:"primary"}).then(Z=>Z.present()),yield this.getRealRoute(x.lat,x.lng,b,F,this.travelMode,B.disaster_type)}})}ngOnDestroy(){console.log("Map page destroyed, cleaning up resources"),this.stopWatchingPosition();try{this.orientationListener&&(this.orientationListener.remove(),this.orientationListener=null)}catch(B){console.error("Error removing motion listeners:",B)}this.map&&this.clearDirectionIndicators(),this.pulsatingMarker&&this.map&&(this.map.removeLayer(this.pulsatingMarker),this.pulsatingMarker=null),this.map&&this.map.remove()}startWatchingOrientation(){return HA(this,null,function*(){try{this.showCompass=!0;try{let B=DeviceMotionEvent;if(typeof B<"u"&&typeof B.requestPermission=="function"&&(yield B.requestPermission())!=="granted")throw new Error("Motion permission not granted")}catch(B){console.log("Permission request failed or not needed:",B)}this.orientationListener=yield oc.addListener("orientation",B=>{this.currentHeading=B.alpha,this.updateDirectionIndicator(),console.log("Orientation changed:",B.alpha)}),console.log("Compass watching started")}catch(B){console.error("Error starting compass watch:",B),this.showCompass=!1,this.toastCtrl.create({message:"Compass not available on this device. Direction indicator disabled.",duration:3e3,color:"warning"}).then(g=>g.present())}})}checkOrientationAvailable(){return HA(this,null,function*(){try{return!0}catch(B){return console.error("Device orientation not available:",B),!1}})}updateDirectionIndicator(){if(!this.map||!this.userMarker||!this.showCompass)return;let B=this.userMarker.getLatLng();this.clearDirectionIndicators(),this.directionIndicator=$.circleMarker(B,{radius:8,fillColor:"#4285F4",color:"#FFFFFF",weight:2,opacity:1,fillOpacity:1}).addTo(this.map);let g=this.calculatePointerPosition(B,this.currentHeading,15);this.directionPointerLine=$.polyline([B,g],{color:"#4285F4",weight:3,opacity:1}).addTo(this.map),this.directionPointerCircle=$.circleMarker(g,{radius:4,fillColor:"#4285F4",color:"#FFFFFF",weight:1,opacity:1,fillOpacity:1}).addTo(this.map)}clearDirectionIndicators(){this.directionIndicator&&(this.map.removeLayer(this.directionIndicator),this.directionIndicator=null),this.directionPointerLine&&(this.map.removeLayer(this.directionPointerLine),this.directionPointerLine=null),this.directionPointerCircle&&(this.map.removeLayer(this.directionPointerCircle),this.directionPointerCircle=null)}calculatePointerPosition(B,g,Q){let C=(90-g)*Math.PI/180,T=6378137,x=Q*2,b=Math.asin(Math.sin(B.lat*Math.PI/180)*Math.cos(x/T)+Math.cos(B.lat*Math.PI/180)*Math.sin(x/T)*Math.cos(C))*180/Math.PI,F=B.lng+Math.atan2(Math.sin(C)*Math.sin(x/T)*Math.cos(B.lat*Math.PI/180),Math.cos(x/T)-Math.sin(B.lat*Math.PI/180)*Math.sin(b*Math.PI/180))*180/Math.PI;return $.latLng(b,F)}};N.\u0275fac=function(g){return new(g||N)},N.\u0275cmp=an({type:N,selectors:[["app-map"]],decls:21,vars:13,consts:[["id","map"],["class","location-request-container",4,"ngIf"],["class","map-default-message",4,"ngIf"],["class","route-summary-card",3,"click",4,"ngIf"],[3,"directions","travelMode","totalDistance","totalDuration","close",4,"ngIf"],["vertical","top","horizontal","end","slot","fixed"],["size","small",3,"click","color"],[3,"name"],["vertical","top","horizontal","start","slot","fixed"],["size","small","color","success",3,"click"],["name","download-outline"],[1,"fab-label"],[1,"gps-status",3,"click"],["class","disaster-type-indicator",4,"ngIf"],["vertical","bottom","horizontal","end","slot","fixed",4,"ngIf"],["vertical","bottom","horizontal","start","slot","fixed",4,"ngIf"],[1,"location-request-container"],["expand","block","color","primary",3,"click"],["name","locate","slot","start"],[1,"location-help-text"],[1,"map-default-message"],["name","information-circle-outline"],[1,"route-summary-card",3,"click"],[3,"name","color"],[1,"summary-text"],[1,"travel-mode"],["name","chevron-up",1,"expand-icon"],[3,"close","directions","travelMode","totalDistance","totalDuration"],[1,"disaster-type-indicator"],["vertical","bottom","horizontal","end","slot","fixed"],["color","primary",3,"click"],["name","navigate-outline"],["vertical","bottom","horizontal","start","slot","fixed"],["color","tertiary",3,"click"],["name","list-outline"]],template:function(g,Q){g&1&&(G(0,"ion-content"),wA(1,"div",0),Ct(2,ph,6,0,"div",1)(3,wh,6,0,"div",2)(4,mh,9,5,"div",3)(5,Ch,1,4,"app-directions-panel",4),G(6,"ion-fab",5)(7,"ion-fab-button",6),at("click",function(){return Q.toggleGps({detail:{checked:!Q.gpsEnabled}})}),wA(8,"ion-icon",7),Y()(),G(9,"ion-fab",8)(10,"ion-fab-button",9),at("click",function(){return Q.downloadMap()}),wA(11,"ion-icon",10),Y(),G(12,"ion-label",11),hA(13,"Save Map"),Y()(),G(14,"div",12),at("click",function(){return Q.showLocationHelp()}),wA(15,"ion-icon",7),G(16,"span"),hA(17),Y()(),Ct(18,vh,4,2,"div",13)(19,Qh,5,0,"ion-fab",14)(20,_h,5,0,"ion-fab",15),Y()),g&2&&(J(2),uA("ngIf",Q.showLocationRequestButton),J(),uA("ngIf",!Q.isFilterMode&&Q.evacuationCenters.length===0),J(),uA("ngIf",Q.routeTime&&Q.routeDistance),J(),uA("ngIf",Q.showDirectionsPanel&&Q.currentDirections.length>0),J(2),uA("color",Q.gpsEnabled?"primary":"medium"),J(),uA("name",Q.gpsEnabled?"locate":"locate-outline"),J(6),Sa("active",Q.gpsEnabled),J(),uA("name",Q.gpsEnabled?"location":"location-outline"),J(2),Tt("GPS ",Q.gpsEnabled?"Active":"Inactive",""),J(),uA("ngIf",Q.isFilterMode&&Q.currentDisasterType!=="all"),J(),uA("ngIf",Q.isFilterMode||Q.evacuationCenters.length>0),J(),uA("ngIf",Q.currentDirections.length>0&&!Q.showDirectionsPanel))},dependencies:[dn,un,go,za,Wa,hn,fn,ln,cn,Ka,uc],styles:['#map[_ngcontent-%COMP%]{width:100%;height:100%}.mode-segment[_ngcontent-%COMP%]{position:absolute;left:50%;transform:translate(-50%);top:10px;z-index:1000;background:#ffffffe6;border-radius:20px;padding:4px;width:90%;max-width:400px;box-shadow:0 2px 8px #0000001a}.mode-segment[_ngcontent-%COMP%]   ion-segment-button[_ngcontent-%COMP%]{--background: transparent;--background-checked: var(--ion-color-light);--indicator-color: transparent;--border-radius: 16px;min-height:40px}.mode-segment[_ngcontent-%COMP%]   ion-segment-button[_ngcontent-%COMP%]   .segment-icon[_ngcontent-%COMP%]{width:24px;height:24px;display:block;margin:0 auto 4px}.mode-segment[_ngcontent-%COMP%]   ion-segment-button[_ngcontent-%COMP%]   .segment-label[_ngcontent-%COMP%]{font-size:12px;font-weight:500}.route-summary-card[_ngcontent-%COMP%]{position:absolute;left:50%;transform:translate(-50%);top:70px;background:#fffffff2;border-radius:16px;box-shadow:0 2px 8px #0000001a;padding:12px 16px;display:flex;align-items:center;gap:12px;z-index:1000;cursor:pointer;transition:all .2s ease}.route-summary-card[_ngcontent-%COMP%]:hover{background:#fff;box-shadow:0 4px 12px #00000026;transform:translate(-50%) translateY(-2px)}.route-summary-card[_ngcontent-%COMP%]:active{transform:translate(-50%) translateY(0)}.route-summary-card[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:24px}.route-summary-card[_ngcontent-%COMP%]   .summary-text[_ngcontent-%COMP%]{line-height:1.3}.route-summary-card[_ngcontent-%COMP%]   .summary-text[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%]{font-size:16px}.route-summary-card[_ngcontent-%COMP%]   .summary-text[_ngcontent-%COMP%]   .travel-mode[_ngcontent-%COMP%]{font-size:12px;opacity:.8;margin-top:2px}.route-summary-card[_ngcontent-%COMP%]   .expand-icon[_ngcontent-%COMP%]{font-size:18px;margin-left:8px;color:var(--ion-color-medium)}.fab-label[_ngcontent-%COMP%]{position:absolute;right:80px;bottom:30px;background:#fffffff2;padding:8px 16px;border-radius:20px;font-size:14px;color:var(--ion-color-primary);z-index:1000;box-shadow:0 2px 8px #0000001a;font-weight:500}ion-fab-button[activated][_ngcontent-%COMP%]{--background: var(--ion-color-primary);--color: white}.gps-status[_ngcontent-%COMP%]{position:absolute;top:10px;left:70px;background:#ffffffe6;border-radius:20px;padding:8px 12px;display:flex;align-items:center;gap:6px;z-index:1000;box-shadow:0 2px 8px #0000001a;font-size:14px;color:var(--ion-color-medium);cursor:pointer;transition:all .2s ease}.gps-status[_ngcontent-%COMP%]:hover{background:#fff;box-shadow:0 4px 12px #00000026;transform:translateY(-2px)}.gps-status[_ngcontent-%COMP%]:active{transform:translateY(0)}.gps-status.active[_ngcontent-%COMP%]{color:var(--ion-color-primary);background:rgba(var(--ion-color-primary-rgb),.1)}.gps-status.active[_ngcontent-%COMP%]:hover{background:rgba(var(--ion-color-primary-rgb),.2)}.gps-status.active[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_pulse 1.5s infinite}.gps-status[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:18px}.gps-status[_ngcontent-%COMP%]:after{content:"?";display:inline-block;width:16px;height:16px;line-height:16px;text-align:center;background:var(--ion-color-medium);color:#fff;border-radius:50%;font-size:12px;margin-left:6px;opacity:.7}.disaster-type-indicator[_ngcontent-%COMP%]{position:absolute;top:10px;right:80px;background:#ffffffe6;border-radius:20px;padding:8px 12px;display:flex;align-items:center;gap:6px;z-index:1000;box-shadow:0 2px 8px #0000001a;font-size:14px;font-weight:500;color:var(--ion-color-dark)}.disaster-type-indicator[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:18px;color:var(--ion-color-primary)}.location-request-container[_ngcontent-%COMP%]{position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);background:#fffffff2;border-radius:16px;box-shadow:0 4px 16px #00000026;padding:20px;text-align:center;max-width:300px;width:90%;z-index:1001;animation:_ngcontent-%COMP%_fadeIn .5s ease-out}.location-request-container[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%]{margin:10px 0;--border-radius: 10px;--box-shadow: 0 4px 8px rgba(var(--ion-color-primary-rgb), .3);font-weight:600;height:48px}.location-request-container[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%]:active{--box-shadow: 0 2px 4px rgba(var(--ion-color-primary-rgb), .2);transform:translateY(2px)}.location-request-container[_ngcontent-%COMP%]   .location-help-text[_ngcontent-%COMP%]{margin:10px 0 0;font-size:14px;color:var(--ion-color-medium);line-height:1.4}@keyframes _ngcontent-%COMP%_fadeIn{0%{opacity:0;transform:translate(-50%,-40%)}to{opacity:1;transform:translate(-50%,-50%)}}.map-default-message[_ngcontent-%COMP%]{position:absolute;bottom:30px;left:50%;transform:translate(-50%);background:#fffffff2;border-radius:16px;box-shadow:0 2px 8px #0000001a;padding:12px 16px;text-align:center;max-width:300px;z-index:1000}.map-default-message[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:24px;color:var(--ion-color-primary);margin-bottom:8px}.map-default-message[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:0 0 5px;font-weight:500;font-size:16px;color:var(--ion-color-dark)}.map-default-message[_ngcontent-%COMP%]   small[_ngcontent-%COMP%]{color:var(--ion-color-medium);font-size:13px;display:block;line-height:1.4}@keyframes _ngcontent-%COMP%_pulse{0%{opacity:.6}50%{opacity:1}to{opacity:.6}}@keyframes _ngcontent-%COMP%_pulsate{0%{transform:scale(.8);opacity:.8}50%{transform:scale(1.5);opacity:.4}to{transform:scale(.8);opacity:.8}}.marker-pulse-container[_ngcontent-%COMP%]{position:relative}.marker-pulse[_ngcontent-%COMP%]{position:absolute;top:50%;left:50%;width:50px;height:50px;margin-top:-25px;margin-left:-25px;border-radius:50%;z-index:100;pointer-events:none;animation:_ngcontent-%COMP%_pulsate 1.5s ease-out infinite;box-shadow:0 0 10px #00000080}[_nghost-%COMP%]     .popup-button{background-color:var(--ion-color-primary);color:#fff;border:none;border-radius:4px;padding:6px 12px;font-size:14px;cursor:pointer;margin-top:8px;transition:background-color .2s}[_nghost-%COMP%]     .popup-button:hover{background-color:var(--ion-color-primary-shade)}[_nghost-%COMP%]     .evacuation-popup h3{margin:0 0 8px;font-size:16px;font-weight:600}[_nghost-%COMP%]     .evacuation-popup p{margin:4px 0;font-size:14px}.evacuation-details-modal[_ngcontent-%COMP%]{--border-radius: 16px 16px 0 0;--backdrop-opacity: .4}']});let v=N;return v})();export{af as MapPage};
