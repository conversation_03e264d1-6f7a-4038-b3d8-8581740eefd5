.tab-selected {
  border-bottom: 3px solid #2196f3;
  border-radius: 0;
  background: rgba(33, 150, 243, 0.08);
}

ion-tab-bar {
  --background: #fff;
  box-shadow: 0 -2px 12px rgba(0,0,0,0.08);
}

ion-tab-button {
  transition: background 0.2s;
  border-radius: 12px 12px 0 0;
  margin: 0 2px;
  padding-bottom: 2px;
}

ion-tab-button.tab-selected {
  color: #2196f3;
  font-weight: bold;
}

ion-tab-button img {
  filter: grayscale(0.3);
}

ion-tab-button.tab-selected img {
  filter: none;
}

ion-tab-button.tab-selected ion-label {
  text-decoration: underline;
}
