@extends('layout.app')

@section('content')


<div class="flex flex-col min-h-screen ">
    <div class="flex-1">

        <div class="flex space-x-8 mb-8">
            <div class=" px-4 bg-white rounded-2xl shadow p-6 flex flex-col items-center w-1/4">
                <div class="text-lg font-semibold">Active Centers</div>
                <div class="w-24 h-24 rounded-full border-8 border-blue-400 flex items-center justify-center text-3xl font-bold mb-2">{{ $activeCenters }}</div>
            </div>
            <div class="bg-white rounded-2xl shadow p-6 flex flex-col items-center w-1/4">
                <div class="text-lg font-semibold">Emergency Alerts</div>
                <div class="w-24 h-24 rounded-full border-8 border-yellow-400 flex items-center justify-center text-3xl font-bold mb-2">{{ $totalAlerts }}</div>
            </div>
            <div class="bg-white rounded-2xl shadow p-6 flex-1">
                <h3 class="text-lg font-semibold mb-4">Recent Alerts</h3>
                @if(count($recentAlerts) > 0)
                    <ul class="space-y-2">
                        @foreach($recentAlerts as $alert)
                            <li class="flex items-center 
                                @if($alert->type == 'Flood') bg-blue-100 
                                @elseif($alert->type == 'Fire') bg-red-100 
                                @elseif($alert->type == 'Earthquake') bg-yellow-100 
                                @else bg-gray-100 @endif 
                                rounded px-3 py-2">
                                <span class="mr-2 
                                    @if($alert->type == 'Flood') text-blue-500 
                                    @elseif($alert->type == 'Fire') text-red-500 
                                    @elseif($alert->type == 'Earthquake') text-yellow-500 
                                    @else text-gray-500 @endif 
                                    text-xl">
                                    @if($alert->type == 'Flood') 💧
                                    @elseif($alert->type == 'Fire') 🔥
                                    @elseif($alert->type == 'Earthquake') ⚡
                                    @else ℹ️ @endif
                                </span>
                                <span class="font-semibold">{{ $alert->message }}</span>
                                <small class="ml-auto text-gray-500">{{ $alert->time_ago }}</small>
                            </li>
                        @endforeach
                    </ul>
                @else
                    <p class="text-gray-500 italic">No recent alerts</p>
                @endif
            </div>
        </div>
       
    </div>
    <div class="container mx-auto py-10">
        <div class="flex items-center mb-4 gap-2">
            <input
                id="searchInput"
                type="text"
                placeholder="Search for a location"
                class="w-80 px-4 py-2 border border-blue-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 rounded-lg shadow-sm transition placeholder-gray-400"
            >
            <button
                id="searchBtn"
                class="bg-gradient-to-r from-blue-600 to-blue-500 hover:from-blue-700 hover:to-blue-600 text-white font-semibold px-6 py-2 rounded-lg shadow transition duration-200 flex items-center gap-2"
            >
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-4.35-4.35m0 0A7.5 7.5 0 104.5 4.5a7.5 7.5 0 0012.15 12.15z" />
                </svg>
                Search
            </button>
        </div>
        <div id="map" class="w-full h-96 rounded"></div>
    </div>
</div>
@endsection

@section('scripts')


<script>
document.addEventListener('DOMContentLoaded', function () {
    // Initialize the map
    var map = L.map('map').setView([10.3157, 123.8854], 13);

    // Add OpenStreetMap tiles
    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
        maxZoom: 19,
        attribution: '© OpenStreetMap'
    }).addTo(map);

    // Add a marker
    var marker = L.marker([10.3157, 123.8854]).addTo(map)
        .bindPopup('Cebu City')
        .openPopup();

    // Add the geocoder control to the map
    var geocoder = L.Control.geocoder({
        defaultMarkGeocode: false
    })
    .on('markgeocode', function(e) {
        var center = e.geocode.center;
        map.setView(center, 15);
        marker.setLatLng(center)
            .bindPopup(e.geocode.name)
            .openPopup();
    })
    .addTo(map);

    // Custom search input integration
    document.getElementById('searchBtn').addEventListener('click', function() {
        var query = document.getElementById('searchInput').value;
        if (query) {
            geocoder.options.geocoder.geocode(query, function(results) {
                if (results.length > 0) {
                    var result = results[0];
                    map.setView(result.center, 15);
                    marker.setLatLng(result.center)
                        .bindPopup(result.name)
                        .openPopup();
                } else {
                    alert('Location not found!');
                }
            });
        }
    });

    // Optional: allow pressing Enter to search
    document.getElementById('searchInput').addEventListener('keydown', function(e) {
        if (e.key === 'Enter') {
            e.preventDefault();
            document.getElementById('searchBtn').click();
        }
    });
});
</script>
@endsection


