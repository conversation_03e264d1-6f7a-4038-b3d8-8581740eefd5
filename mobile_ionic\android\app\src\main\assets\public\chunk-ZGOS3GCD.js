import{a as D}from"./chunk-X3ALRMID.js";import{a as B,b as q}from"./chunk-2IZDSB5C.js";import{a as M}from"./chunk-FNGMIKCE.js";import{B as i,Ba as L,C as l,D as d,Ea as F,F as h,Ha as A,I as s,M as u,N as _,Na as E,O as C,Ta as j,U as w,Ua as z,Xa as W,Z as P,aa as b,ba as O,ca as v,da as T,ea as S,fa as y,ga as I,lb as R,pb as N,u as f,v as a,va as x,w as k}from"./chunk-7LSN6DH6.js";import"./chunk-EFGZ4QZO.js";import"./chunk-VKNYRYKJ.js";import"./chunk-NETZAO6G.js";import"./chunk-YLGV66M3.js";import"./chunk-VD4VFVTQ.js";import"./chunk-HC6MZPB3.js";import"./chunk-IP3IRUG3.js";import"./chunk-UKIOCGZG.js";import"./chunk-RMJ7PCZJ.js";import"./chunk-K54AU7WQ.js";import"./chunk-VBU7NMPV.js";import"./chunk-JIZJ6CAM.js";import"./chunk-MCRJI3T3.js";import"./chunk-YNIR5NDL.js";import"./chunk-NO26UXQI.js";import"./chunk-SELJIRKY.js";import"./chunk-ODNE7IRY.js";import"./chunk-F4H6ZFEG.js";import"./chunk-JWIEPCRG.js";import"./chunk-7WD7LEC6.js";import"./chunk-WTCPO44B.js";import"./chunk-SV7S5NYR.js";import"./chunk-L5T6STQ3.js";import"./chunk-3EJRMEWO.js";import"./chunk-GNOVVPTF.js";import"./chunk-BAKMWPBW.js";import"./chunk-5OMUW5VI.js";import"./chunk-OBXDPQ3V.js";import{h as m}from"./chunk-LNJ3S2LQ.js";var te=(()=>{let c=class c{goToRegister(){this.router.navigate(["/register"])}constructor(e,t,n,r,o,g,U){this.router=e,this.authService=t,this.fcm=n,this.http=r,this.alertController=o,this.platform=g,this.fcmService=U,this.credentials={email:"",password:""},this.errorMessage="",this.fcmToken="",this.getFCMToken()}getFCMToken(){if(!this.platform.is("cordova")&&!this.platform.is("capacitor")){console.log("Running in browser, using mock FCM token"),this.fcmToken="browser-mock-token-"+Math.random().toString(36).substring(2,15),console.log("Mock FCM Token:",this.fcmToken);return}this.fcm.getToken().then(e=>{console.log("FCM Token:",e),this.fcmToken=e}).catch(e=>{console.error("Error getting FCM token:",e),this.fcmService.getToken().then(t=>{console.log("FCM Token from service:",t),this.fcmToken=t}).catch(t=>{console.error("Error getting FCM token from service:",t)})})}registerTokenWithEndpoint(e,t,n,r){if(localStorage.getItem("fcm_token")===this.fcmToken){console.log("Token already registered, skipping registration"),n&&n();return}if(localStorage.getItem("fcm_token_registering")==="true"){console.log("Token registration already in progress, skipping"),n&&n();return}localStorage.setItem("fcm_token_registering","true"),this.http.post(e,t).subscribe({next:g=>{console.log(`FCM token registered with ${e}:`,g),localStorage.setItem("fcm_token",this.fcmToken),localStorage.removeItem("fcm_token_registering"),n&&n()},error:g=>{console.error(`Error registering token with ${e}:`,g),localStorage.removeItem("fcm_token_registering"),r&&r()}})}onLogin(){return m(this,null,function*(){if(!this.credentials.email||!this.credentials.password){yield this.presentAlert("Login Failed","Please enter both email and password.");return}console.log("Login clicked",this.credentials),this.authService.login(this.credentials).subscribe({next:e=>m(this,null,function*(){if(yield this.presentSuccessAlert("Login Successful","Welcome, "+e.user.full_name),localStorage.setItem("token",e.token),this.fcmToken){console.log("Registering FCM token with backend:",this.fcmToken);let t={token:this.fcmToken,device_type:this.platform.is("ios")?"ios":"android",project_id:M.firebase.projectId};e.user&&e.user.id&&(t.user_id=e.user.id),console.log("Token registration payload:",t),console.log("API URL:",`${M.apiUrl}/device-token`),this.fcmService.registerTokenWithBackend(this.fcmToken,e.user.id),this.router.navigate(["/welcome"])}else console.warn("No FCM token available to register"),this.router.navigate(["/welcome"])}),error:e=>{var t;console.error("Login error:",e),this.errorMessage=((t=e.error)==null?void 0:t.message)||"Login failed",e.status===401?this.presentAlert("Login Failed","Invalid email or password. Please try again."):this.presentAlert("Login Error","An error occurred during login. Please try again later.")}})})}presentAlert(e,t){return m(this,null,function*(){yield(yield this.alertController.create({header:e,message:t,buttons:["OK"],cssClass:"login-alert"})).present()})}presentSuccessAlert(e,t){return m(this,null,function*(){yield(yield this.alertController.create({header:e,message:t,buttons:["OK"],cssClass:"login-success-alert"})).present()})}};c.\u0275fac=function(t){return new(t||c)(a(P),a(D),a(B),a(w),a(R),a(x),a(q))},c.\u0275cmp=k({type:c,selectors:[["app-login"]],decls:27,vars:2,consts:[[1,"ion-padding"],[1,"login-container"],[1,"login-wrapper"],["src","assets/ALERTO.png","alt","App Logo",1,"login-logo"],[1,"login-title"],[1,"login-form",3,"ngSubmit"],["position","floating"],["type","email","name","email","required","",3,"ngModelChange","ngModel"],["type","password","name","password","required","",3,"ngModelChange","ngModel"],["expand","block","type","submit",1,"login-btn"],[1,"login-link"],[3,"click"]],template:function(t,n){t&1&&(i(0,"ion-content",0)(1,"div",1)(2,"ion-card-content")(3,"div",2),d(4,"img",3),i(5,"h1",4),s(6,"Log In Here!"),l(),d(7,"p"),i(8,"form",5),h("ngSubmit",function(){return n.onLogin()}),i(9,"ion-item")(10,"ion-label",6),s(11,"Email:"),l(),i(12,"ion-input",7),C("ngModelChange",function(o){return _(n.credentials.email,o)||(n.credentials.email=o),o}),l()(),i(13,"ion-item")(14,"ion-label",6),s(15,"Password:"),l(),i(16,"ion-input",8),C("ngModelChange",function(o){return _(n.credentials.password,o)||(n.credentials.password=o),o}),l()(),d(17,"br")(18,"br"),i(19,"ion-button",9),s(20,"Log In"),l()(),i(21,"div",10),s(22," Don't have an account? "),i(23,"a",11),h("click",function(){return n.goToRegister()}),i(24,"strong")(25,"u"),s(26,"Sign Up"),l()()()()()()()()),t&2&&(f(12),u("ngModel",n.credentials.email),f(4),u("ngModel",n.credentials.password))},dependencies:[N,F,A,E,j,z,W,L,I,S,b,O,y,T,v],styles:[".login-container[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center;height:80vh}.login-wrapper[_ngcontent-%COMP%]{width:100%;max-width:420px;padding:32px 28px;margin:0 auto;display:flex;flex-direction:column;align-items:center}.login-logo[_ngcontent-%COMP%]{width:300px;height:300px}.login-title[_ngcontent-%COMP%]{font-size:2.2rem;font-weight:700}.login-desc[_ngcontent-%COMP%]{font-size:1.1rem;color:#888}.login-form[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{font-size:1.1rem;border-radius:16px}.login-btn[_ngcontent-%COMP%]{--border-radius: 25px;font-size:1.2rem;height:48px}.login-link[_ngcontent-%COMP%]{margin-top:20px;font-size:1.1rem}.login-link[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]{font-size:1.1rem;font-weight:700}.login-alert[_ngcontent-%COMP%]{--backdrop-opacity: .8}.login-alert[_ngcontent-%COMP%]   .alert-wrapper[_ngcontent-%COMP%]{border-radius:15px;box-shadow:0 4px 16px #0003}.login-alert[_ngcontent-%COMP%]   .alert-head[_ngcontent-%COMP%]{padding-bottom:10px}.login-alert[_ngcontent-%COMP%]   .alert-title[_ngcontent-%COMP%]{font-size:1.2rem;font-weight:600;color:#d9534f}.login-alert[_ngcontent-%COMP%]   .alert-message[_ngcontent-%COMP%]{font-size:1rem;color:#333}.login-alert[_ngcontent-%COMP%]   .alert-button[_ngcontent-%COMP%]{color:#3880ff;font-weight:500}.login-success-alert[_ngcontent-%COMP%]{--backdrop-opacity: .8}.login-success-alert[_ngcontent-%COMP%]   .alert-wrapper[_ngcontent-%COMP%]{border-radius:15px;box-shadow:0 4px 16px #0003}.login-success-alert[_ngcontent-%COMP%]   .alert-head[_ngcontent-%COMP%]{padding-bottom:10px}.login-success-alert[_ngcontent-%COMP%]   .alert-title[_ngcontent-%COMP%]{font-size:1.2rem;font-weight:600;color:#5cb85c}.login-success-alert[_ngcontent-%COMP%]   .alert-message[_ngcontent-%COMP%]{font-size:1rem;color:#333}.login-success-alert[_ngcontent-%COMP%]   .alert-button[_ngcontent-%COMP%]{color:#3880ff;font-weight:500}"]});let p=c;return p})();export{te as LoginPage};
