<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\Notification;
use App\Models\DeviceToken;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\RateLimiter;

class FCMControllerTest extends TestCase
{
    use RefreshDatabase;

    public function test_send_test_notification_success()
    {
        // Create test device token
        $token = DeviceToken::create([
            'token' => 'test_token_123',
            'device_type' => 'android',
            'project_id' => 'test_project',
            'is_active' => true
        ]);

        $response = $this->postJson('/api/fcm/test', [
            'title' => 'Test Notification',
            'message' => 'This is a test notification',
            'severity' => 'normal',
            'category' => 'test'
        ]);

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => 'Test notification sent successfully'
            ]);

        $this->assertDatabaseHas('notifications', [
            'title' => 'Test Notification',
            'message' => 'This is a test notification',
            'severity' => 'normal',
            'category' => 'test',
            'sent' => true
        ]);
    }

    public function test_send_test_notification_with_rate_limit()
    {
        // Create test device token
        $token = DeviceToken::create([
            'token' => 'test_token_123',
            'device_type' => 'android',
            'project_id' => 'test_project',
            'is_active' => true
        ]);

        // Send multiple notifications to trigger rate limit
        for ($i = 0; $i < 11; $i++) {
            $response = $this->postJson('/api/fcm/test', [
                'title' => 'Test Notification',
                'message' => 'This is a test notification',
                'severity' => 'normal',
                'category' => 'test'
            ]);
        }

        $response->assertStatus(429)
            ->assertJson([
                'success' => false,
                'message' => 'Too many test notifications. Please try again later.'
            ]);
    }

    public function test_send_bulk_notifications_success()
    {
        // Create test device token
        $token = DeviceToken::create([
            'token' => 'test_token_123',
            'device_type' => 'android',
            'project_id' => 'test_project',
            'is_active' => true
        ]);

        $response = $this->postJson('/api/fcm/bulk', [
            'notifications' => [
                [
                    'title' => 'Test Notification 1',
                    'message' => 'This is test notification 1',
                    'severity' => 'normal',
                    'category' => 'test'
                ],
                [
                    'title' => 'Test Notification 2',
                    'message' => 'This is test notification 2',
                    'severity' => 'high',
                    'category' => 'test'
                ]
            ]
        ]);

        $response->assertStatus(200)
            ->assertJson([
                'success' => true
            ])
            ->assertJsonStructure([
                'data' => [
                    'success_count',
                    'failure_count',
                    'results'
                ]
            ]);

        $this->assertDatabaseHas('notifications', [
            'title' => 'Test Notification 1',
            'message' => 'This is test notification 1',
            'severity' => 'normal',
            'category' => 'test',
            'sent' => true
        ]);

        $this->assertDatabaseHas('notifications', [
            'title' => 'Test Notification 2',
            'message' => 'This is test notification 2',
            'severity' => 'high',
            'category' => 'test',
            'sent' => true
        ]);
    }

    public function test_send_bulk_notifications_with_rate_limit()
    {
        // Create test device token
        $token = DeviceToken::create([
            'token' => 'test_token_123',
            'device_type' => 'android',
            'project_id' => 'test_project',
            'is_active' => true
        ]);

        // Send multiple bulk notifications to trigger rate limit
        for ($i = 0; $i < 61; $i++) {
            $response = $this->postJson('/api/fcm/bulk', [
                'notifications' => [
                    [
                        'title' => 'Test Notification',
                        'message' => 'This is a test notification',
                        'severity' => 'normal',
                        'category' => 'test'
                    ]
                ]
            ]);
        }

        $response->assertStatus(429)
            ->assertJson([
                'success' => false,
                'message' => 'Too many bulk notifications. Please try again later.'
            ]);
    }

    public function test_get_notification_status()
    {
        // Create test notification
        $notification = Notification::create([
            'title' => 'Test Notification',
            'message' => 'This is a test notification',
            'severity' => 'normal',
            'category' => 'test',
            'sent' => true
        ]);

        $response = $this->getJson("/api/fcm/notification/{$notification->id}");

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'data' => [
                    'id' => $notification->id,
                    'title' => 'Test Notification',
                    'message' => 'This is a test notification',
                    'sent' => true
                ]
            ]);
    }

    public function test_get_notification_status_not_found()
    {
        $response = $this->getJson('/api/fcm/notification/999');

        $response->assertStatus(404);
    }

    public function test_send_test_notification_validation()
    {
        $response = $this->postJson('/api/fcm/test', [
            'title' => '',
            'message' => '',
            'severity' => 'invalid'
        ]);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['title', 'message', 'severity']);
    }

    public function test_send_bulk_notifications_validation()
    {
        $response = $this->postJson('/api/fcm/bulk', [
            'notifications' => [
                [
                    'title' => '',
                    'message' => '',
                    'severity' => 'invalid'
                ]
            ]
        ]);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['notifications.0.title', 'notifications.0.message', 'notifications.0.severity']);
    }
} 