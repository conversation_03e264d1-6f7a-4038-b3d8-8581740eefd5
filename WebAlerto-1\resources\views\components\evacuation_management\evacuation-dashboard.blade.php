@extends('layout.app')

@section('title', 'Evacuation Management Dashboard')

@section('content')
@php
function highlight($text, $keyword) {
    if (!$keyword) return $text;

    return preg_replace(
        "/(" . preg_quote($keyword) . ")/i",
        '<span class="bg-yellow-300 text-black font-semibold px-1 rounded">$1</span>',
        $text
    );
}
@endphp
<!-- Grid Cards -->
<div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
    <!-- Total Centers -->
    <a href="{{ route('components.evacuation_management.centers-list', ['type' => '']) }}"
       class="bg-white rounded shadow-sm p-4 text-center cursor-pointer hover:bg-gray-100 transition-all duration-200">
        <h6 class="text-gray-600 font-bold text-lg">Total Centers</h6>
        <h2 class="text-2xl font-bold">{{ $centers->total() }}</h2>
    </a>

    <!-- Active Centers -->
    <a href="{{ route('components.evacuation_management.centers-list', ['type' => 'active']) }}" 
       class="bg-white rounded shadow-sm p-4 text-center cursor-pointer hover:bg-gray-100 transition-all duration-200">
        <h6 class="text-gray-600 font-bold text-lg">Active Centers</h6>
        <h2 class="text-2xl font-bold text-green-600">{{ $activeCount }}</h2>
    </a>

    <!-- Inactive Centers -->
    <a href="{{ route('components.evacuation_management.centers-list', ['type' => 'inactive']) }}" 
       class="bg-white rounded shadow-sm p-4 text-center cursor-pointer hover:bg-gray-100 transition-all duration-200">
        <h6 class="text-gray-600 font-bold text-lg">Inactive Centers</h6>
        <h2 class="text-2xl font-bold text-red-600">{{ $inactiveCount }}</h2>
    </a>
</div>

<!-- Modal for Centers List -->
<div id="centersListModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden">
    <div class="relative top-20 mx-auto p-5 border w-11/12 max-w-2xl shadow-lg rounded-md bg-white">
        <div class="flex justify-between items-center mb-4">
            <h3 class="text-lg font-semibold" id="modalTitle">Centers List</h3>
            <button onclick="closeCentersList()" class="text-gray-500 hover:text-gray-700">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                </svg>
            </button>
        </div>
        
        <!-- Search input for modal -->
        <div class="mb-4">
            <input type="text" 
                   id="modalSearch" 
                   class="w-full px-3 py-2 border border-gray-300 rounded-md"
                   placeholder="Search centers...">
        </div>

        <!-- Centers list with pagination -->
        <div class="max-h-96 overflow-y-auto">
            <table class="min-w-full">
                <thead class="bg-gray-50 sticky top-0">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Name</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Address</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Status</th>
                    </tr>
                </thead>
                <tbody id="centersListBody" class="bg-white divide-y divide-gray-200">
                    <!-- Will be populated by JavaScript -->
                </tbody>
            </table>
        </div>

        <div class="mt-4 flex justify-end">
            <button onclick="closeCentersList()" class="px-4 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200">
                Close
            </button>
        </div>
    </div>
</div>

<script>
let allCenters = @json($centers);

function showCentersList(type) {
    const modal = document.getElementById('centersListModal');
    const modalTitle = document.getElementById('modalTitle');
    const centersListBody = document.getElementById('centersListBody');
    
    // Filter centers based on type
    let filteredCenters = allCenters;
    const typeLower = type.toLowerCase();
    
    if (typeLower === 'active') {
        modalTitle.textContent = 'Active Centers';
        filteredCenters = allCenters.filter(center => center.status === 'Active');
    } else if (typeLower === 'inactive') {
        modalTitle.textContent = 'Inactive Centers';
        filteredCenters = allCenters.filter(center => center.status === 'Inactive');
    } else {
        modalTitle.textContent = 'All Centers';
    }

    // Populate table
    centersListBody.innerHTML = filteredCenters.map(center => `
        <tr class="hover:bg-gray-50">
            <td class="px-6 py-4 whitespace-nowrap">${center.name}</td>
            <td class="px-6 py-4 whitespace-nowrap">${center.street_name}, ${center.barangay}, ${center.city}, ${center.province}</td>
            <td class="px-6 py-4 whitespace-nowrap">
                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full 
                    ${center.status === 'Active' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}">
                    ${center.status}
                </span>
            </td>
        </tr>
    `).join('');

    // Show modal
    modal.classList.remove('hidden');

    // Setup search functionality
    const searchInput = document.getElementById('modalSearch');
    searchInput.value = '';
    searchInput.addEventListener('input', function() {
        const searchTerm = this.value.toLowerCase();
        const rows = centersListBody.getElementsByTagName('tr');
        
        Array.from(rows).forEach(row => {
            const text = row.textContent.toLowerCase();
            row.style.display = text.includes(searchTerm) ? '' : 'none';
        });
    });
}

function closeCentersList() {
    const modal = document.getElementById('centersListModal');
    modal.classList.add('hidden');
}

// Close modal when clicking outside
window.onclick = function(event) {
    const modal = document.getElementById('centersListModal');
    if (event.target === modal) {
        closeCentersList();
    }
}
</script>

    <!-- Filters -->
    <div class="flex justify-between items-center mb-4 gap-4">
    <a href="{{ route('components.evacuation_management.add-evacuation-center') }}"
       class="bg-blue-500 text-white px-4 py-2 rounded shadow hover:bg-blue-600">
       Add New
    </a>
    <form action="{{ route('components.evacuation_management.evacuation-dashboard') }}" method="GET" class="flex items-center w-full md:w-auto">
        <div class="relative">
            <input 
                type="text" 
                id="searchInput"
                name="search"
                placeholder="Search evacuation centers..." 
                value="{{ request('search') }}"
                class="border border-gray-300 rounded-lg p-2 pl-10 w-full md:w-72">
            <!-- Search Icon -->
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 absolute left-3 top-3 text-gray-400" fill="currentColor" viewBox="0 0 24 24">
                <path d="M10 2a8 8 0 105.293 14.707l4.707 4.707a1 1 0 001.414-1.414l-4.707-4.707A8 8 0 0010 2zm0 2a6 6 0 110 12A6 6 0 0110 4z"/>
            </svg>
        </div>
        <button type="submit" class="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 ml-2">
            Search
        </button>
    </form>
</div>

    <!-- Table Section -->
    <div class="overflow-x-auto mt-4">
        <table class="min-w-full bg-white border border-gray-200 rounded-lg">
            <thead>
                <tr class="bg-gray-200 text-gray-600 uppercase text-sm leading-normal">
                    <th class="py-3 px-6 text-left">Center Name</th>
                    <th class="py-3 px-6 text-left">Address</th>
                    <th class="py-3 px-6 text-center">Capacity</th>
                    <th class="py-3 px-6 text-center">Contact Info</th>
                    <th class="py-3 px-6 text-center">Disaster Type</th>
                    <th class="py-3 px-6 text-center">Status</th>
                    <th class="py-3 px-6 text-center">Actions</th>
                </tr>
            </thead>
            <tbody class="text-gray-600 text-sm font-light" id="evacuationTableBody">
                @forelse ($centers as $center)
                    <tr class="evacuation-row border-b border-gray-200 hover:bg-gray-100" 
                        data-name="{{ strtolower($center->name) }}"
                        data-address="{{ strtolower($center->street_name . ' ' . $center->barangay . ' ' . $center->city . ' ' . $center->province) }}">
                        <td class="py-3 px-6">
                            <div class="flex flex-col">
                                <span class="font-medium">{{ $center->name }}</span>
                            </div>
                        </td>
                        <td class="py-3 px-6 text-left">
                            {{ $center->street_name }}, 
                            {{ $center->barangay }}, 
                            {{ $center->city }}, 
                            {{ $center->province }} 
                        </td>
                        <td class="py-3 px-6 text-center">{{ $center->capacity }}</td>
                        <td class="py-3 px-6 text-center">{{ $center->contact }}</td>
                        <td class="py-3 px-6 text-center">
                            <span class="px-2 py-1 rounded-full text-xs font-medium
                                @if($center->disaster_type === 'Typhoon')
                                    bg-red-100 text-red-800
                                @elseif($center->disaster_type === 'Flood')
                                    bg-green-100 text-green-800
                                @elseif($center->disaster_type === 'Earthquake')
                                    bg-orange-100 text-orange-800
                                @endif">
                                {{ $center->disaster_type }}
                            </span>
                        </td>
                        <td class="py-3 px-6 text-center">
                            <span class="px-2 py-1 rounded-full text-xs font-medium
                                {{ $center->status === 'Active' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }}">
                                {{ $center->status }}
                            </span>
                        </td>
                        <td class="py-3 px-6 text-center">
                            <div class="flex justify-center space-x-2">
                                <a href="{{ route('components.evacuation_management.edit-evacuation-center', $center->id) }}" 
                                   class="bg-yellow-500 text-white px-2 py-1 rounded text-xs hover:bg-yellow-600">
                                    Edit
                                </a>
                                <button onclick="openDeleteModal({{ $center->id }}, '{{ $center->name }}')" 
                                        class="bg-red-500 text-white px-2 py-1 rounded text-xs hover:bg-red-600">
                                    Delete
                                </button>
                                <button onclick="openViewModal({{ $center->id }})" 
                                        class="bg-blue-500 text-white px-2 py-1 rounded text-xs hover:bg-blue-600">
                                    View
                                </button>
                            </div>
                        </td>
                    </tr>
                @empty
                    <tr>
                        <td colspan="7" class="py-3 px-6 text-center">
                            No evacuation centers found
                        </td>
                    </tr>
                @endforelse
            </tbody>
        </table>
    </div>

    <!-- Add this right after your table -->
    <div class="mt-4 flex justify-end items-center">
        @if ($centers->count() > 0)
            <div class="flex items-center space-x-3">
                @if ($centers->onFirstPage())
                    <span class="text-gray-400 cursor-not-allowed">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
                        </svg>
                    </span>
                @else
                    <a href="{{ $centers->previousPageUrl() }}" class="text-gray-600 hover:text-blue-600 transition">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
                        </svg>
                    </a>
                @endif

                <span class="text-gray-600 text-sm">
                    page {{ $centers->currentPage() }} of {{ $centers->lastPage() }}
                </span>

                @if ($centers->hasMorePages())
                    <a href="{{ $centers->nextPageUrl() }}" class="text-gray-600 hover:text-blue-600 transition">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                        </svg>
                    </a>
                @else
                    <span class="text-gray-400 cursor-not-allowed">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                        </svg>
                    </span>
                @endif
            </div>
        @endif
    </div>

    <!-- No Results Message -->
    <div id="noResultsMessage" class="hidden mt-4 text-center text-gray-600">
        No matching centers found for "<span id="searchTerm"></span>"
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const searchInput = document.getElementById('searchInput');
    const tableBody = document.getElementById('evacuationTableBody');
    const rows = tableBody.getElementsByClassName('evacuation-row');
    const form = searchInput.closest('form');
    const noResultsMessage = document.getElementById('noResultsMessage');
    const searchTermSpan = document.getElementById('searchTerm');

    function performSearch(searchTerm) {
        searchTerm = searchTerm.toLowerCase().trim();
        let hasResults = false;
        
        Array.from(rows).forEach(row => {
            const name = row.getAttribute('data-name');
            const address = row.getAttribute('data-address');
            
            // Check if search term matches center name (starts with)
            const nameMatch = name.startsWith(searchTerm);
            
            // Check if search term matches any part of the address
            const addressMatch = address.includes(searchTerm);
            
            // Show row if either name or address matches
            const shouldShow = (nameMatch || addressMatch);
            row.style.display = shouldShow ? '' : 'none';
            
            if (shouldShow) {
                hasResults = true;
            }
        });

        // Show/hide no results message
        if (searchTerm && !hasResults) {
            searchTermSpan.textContent = searchTerm;
            noResultsMessage.classList.remove('hidden');
        } else {
            noResultsMessage.classList.add('hidden');
        }
    }

    // Real-time search as you type
    searchInput.addEventListener('input', function() {
        performSearch(this.value);
    });

    // Handle form submission
    form.addEventListener('submit', function(e) {
        e.preventDefault();
        performSearch(searchInput.value);
    });
});

function openDeleteModal(centerId, centerName) {
    const modal = document.getElementById('deleteModal');
    const deleteForm = document.getElementById('deleteForm');
    const centerNameSpan = document.getElementById('centerNameToDelete');
    
    // Set the center name in the modal
    centerNameSpan.textContent = centerName;
    
    // Set the form action
    deleteForm.action = `/evacuation/${centerId}`;
    
    // Show the modal
    modal.classList.remove('hidden');
}

function closeDeleteModal() {
    const modal = document.getElementById('deleteModal');
    modal.classList.add('hidden');
}

// Close modals when clicking outside
window.onclick = function(event) {
    const deleteModal = document.getElementById('deleteModal');
    if (event.target === deleteModal) {
        closeDeleteModal();
    }
}
</script>

<!-- Add these modals at the bottom of your blade file (outside the table) -->
<!-- Delete Confirmation Modal -->
<div id="deleteModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
        <div class="mt-3 text-center">
            <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100">
                <svg class="h-6 w-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"/>
                </svg>
            </div>
            <h3 class="text-lg leading-6 font-medium text-gray-900 mt-4">Delete Confirmation</h3>
            <div class="mt-2 px-7 py-3">
                <p class="text-sm text-gray-500">
                    Are you sure you want to delete <span id="centerNameToDelete" class="font-medium"></span>?
                </p>
                <p class="text-sm text-gray-500 mt-1">
                    This action cannot be undone.
                </p>
            </div>
            <div class="flex justify-center gap-4 mt-4">
                <form id="deleteForm" method="POST">
                    @csrf
                    @method('DELETE')
                    <button type="submit" 
                            class="px-4 py-2 bg-red-600 text-white text-base font-medium rounded-md shadow-sm hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500">
                        Delete
                    </button>
                </form>
                <button onclick="closeDeleteModal()" 
                        class="px-4 py-2 bg-gray-100 text-gray-700 text-base font-medium rounded-md shadow-sm hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-500">
                    Cancel
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Add New Center Modal -->
<div id="addModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
        <div class="mt-3 text-center">
            <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-blue-100">
                <svg class="h-6 w-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
                </svg>
            </div>
            <h3 class="text-lg leading-6 font-medium text-gray-900 mt-4">Add New Evacuation Center</h3>
            <div class="mt-2 px-7 py-3">
                <p class="text-sm text-gray-500">
                    Do you want to add a new evacuation center?
                </p>
            </div>
            <div class="flex justify-center gap-4 mt-4">
                <a href="{{ route('components.evacuation_management.add-evacuation-center') }}"
                   class="px-4 py-2 bg-blue-600 text-white text-base font-medium rounded-md shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500">
                    Continue
                </a>
                <button onclick="closeAddModal()" 
                        class="px-4 py-2 bg-gray-100 text-gray-700 text-base font-medium rounded-md shadow-sm hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-500">
                    Cancel
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Add View Modal -->
<div id="viewModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
        <div class="mt-3">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-semibold text-gray-900">Evacuation Center Details</h3>
                <button onclick="closeViewModal()" class="text-gray-500 hover:text-gray-700">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                    </svg>
                </button>
            </div>
            
            <div class="space-y-4">
                <div>
                    <h4 class="text-sm font-medium text-gray-500">Center Name</h4>
                    <p id="viewName" class="mt-1 text-gray-900"></p>
                </div>
                <div>
                    <h4 class="text-sm font-medium text-gray-500">Address</h4>
                    <p id="viewAddress" class="mt-1 text-gray-900"></p>
                </div>
                <div>
                    <h4 class="text-sm font-medium text-gray-500">Capacity</h4>
                    <p id="viewCapacity" class="mt-1 text-gray-900"></p>
                </div>
                <div>
                    <h4 class="text-sm font-medium text-gray-500">Contact Info</h4>
                    <p id="viewContact" class="mt-1 text-gray-900"></p>
                </div>
                <div>
                    <h4 class="text-sm font-medium text-gray-500">Disaster Type</h4>
                    <p id="viewDisasterType" class="mt-1">
                        <span id="viewDisasterTypeBadge" class="px-2 py-1 rounded-full text-xs font-medium"></span>
                    </p>
                </div>
                <div>
                    <h4 class="text-sm font-medium text-gray-500">Status</h4>
                    <p id="viewStatus" class="mt-1">
                        <span id="viewStatusBadge" class="px-2 py-1 rounded-full text-xs font-medium"></span>
                    </p>
                </div>
                <div>
                    <h4 class="text-sm font-medium text-gray-500">Location</h4>
                    <div id="viewMap" class="mt-1 h-48 w-full rounded-lg"></div>
                </div>
            </div>

            <div class="mt-6 flex justify-end">
                <button onclick="closeViewModal()" 
                        class="px-4 py-2 bg-gray-100 text-gray-700 text-base font-medium rounded-md shadow-sm hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-500">
                    Close
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Add this JavaScript -->
<script>
function openAddModal() {
    const modal = document.getElementById('addModal');
    modal.classList.remove('hidden');
}

function closeAddModal() {
    const modal = document.getElementById('addModal');
    modal.classList.add('hidden');
}

// Close modals when clicking outside
window.onclick = function(event) {
    const addModal = document.getElementById('addModal');
    
    if (event.target === addModal) {
        closeAddModal();
    }
}

let viewMap;

function openViewModal(centerId) {
    const modal = document.getElementById('viewModal');
    modal.classList.remove('hidden');

    // Fetch center details
    fetch(`/api/evacuation-centers/${centerId}`)
        .then(response => response.json())
        .then(center => {
            // Update modal content
            document.getElementById('viewName').textContent = center.name;
            
            // Format and display address
            const addressParts = [
                center.street_name,
                center.barangay,
                center.city,
                center.province
            ].filter(part => part && part.trim() !== '');
            
            document.getElementById('viewAddress').textContent = addressParts.join(', ');
            document.getElementById('viewCapacity').textContent = `Capacity: ${center.capacity}`;
            document.getElementById('viewContact').textContent = `Contact: ${center.contact}`;

            // Update disaster type badge
            const disasterTypeBadge = document.getElementById('viewDisasterTypeBadge');
            disasterTypeBadge.textContent = center.disaster_type;
            disasterTypeBadge.className = 'px-2 py-1 rounded-full text-xs font-medium ' + 
                (center.disaster_type === 'Typhoon' ? 'bg-red-100 text-red-800' :
                 center.disaster_type === 'Flood' ? 'bg-green-100 text-green-800' :
                 'bg-orange-100 text-orange-800');

            // Update status badge
            const statusBadge = document.getElementById('viewStatusBadge');
            statusBadge.textContent = center.status;
            statusBadge.className = 'px-2 py-1 rounded-full text-xs font-medium ' +
                (center.status === 'Active' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800');

            // Initialize map
            if (viewMap) {
                viewMap.remove();
            }
            viewMap = L.map('viewMap').setView([center.latitude, center.longitude], 15);
            L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
            }).addTo(viewMap);

            // Add marker
            const markerColor = center.disaster_type === 'Typhoon' ? '#FF0000' :
                              center.disaster_type === 'Flood' ? '#00FF00' : '#FFA500';
            
            const markerIcon = L.divIcon({
                html: `<div style="background-color: ${markerColor}; width: 24px; height: 24px; border-radius: 50%; border: 2px solid white;"></div>`,
                className: 'custom-div-icon',
                iconSize: [24, 24],
                iconAnchor: [12, 12]
            });

            L.marker([center.latitude, center.longitude], { icon: markerIcon })
                .addTo(viewMap)
                .bindPopup(center.name);
        })
        .catch(error => {
            console.error('Error fetching center details:', error);
            alert('Failed to load center details. Please try again.');
        });
}

function closeViewModal() {
    const modal = document.getElementById('viewModal');
    modal.classList.add('hidden');
    if (viewMap) {
        viewMap.remove();
        viewMap = null;
    }
}

// Close view modal when clicking outside
window.onclick = function(event) {
    const viewModal = document.getElementById('viewModal');
    if (event.target === viewModal) {
        closeViewModal();
    }
}
</script>
@endsection