<?php

namespace App\Http\Controllers;

use App\Models\DeviceToken;
use App\Models\Notification;
use App\Services\FCMService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class TestFCMController extends Controller
{
    protected $fcmService;

    public function __construct(FCMService $fcmService)
    {
        $this->fcmService = $fcmService;
    }
    /**
     * Show the test FCM form.
     *
     * @return \Illuminate\View\View
     */
    public function index()
    {
        $tokens = DeviceToken::orderBy('created_at', 'desc')->get();
        return view('test-fcm', compact('tokens'));
    }

    /**
     * Show the form to manually register a device token.
     *
     * @return \Illuminate\View\View
     */
    public function showRegisterTokenForm()
    {
        return view('components.test-fcm.register-token');
    }

    /**
     * Register a device token manually.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function registerToken(Request $request)
    {
        $request->validate([
            'token' => 'required|string',
            'device_type' => 'required|string|in:android,ios,web',
            'user_id' => 'nullable|integer',
        ]);

        try {
            // Check if user_id exists in the users table
            $userData = [];
            if ($request->filled('user_id')) {
                $userExists = \App\Models\User::where('id', $request->user_id)->exists();
                if ($userExists) {
                    $userData['user_id'] = $request->user_id;
                } else {
                    Log::warning('Attempted to register token with non-existent user_id', [
                        'user_id' => $request->user_id,
                        'token' => substr($request->token, 0, 10) . '...'
                    ]);
                }
            }

            // Create or update the device token
            $deviceToken = DeviceToken::updateOrCreate(
                [
                    'token' => $request->token,
                ],
                array_merge([
                    'device_type' => $request->device_type,
                    'is_active' => true,
                ], $userData)
            );

            Log::info('Device token manually registered', [
                'token' => substr($request->token, 0, 10) . '...',
                'device_type' => $request->device_type,
                'user_id' => $request->user_id
            ]);

            return redirect()->route('test-fcm')
                ->with('success', 'Device token registered successfully.');
        } catch (\Exception $e) {
            Log::error('Failed to register device token', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return back()->with('error', 'Failed to register device token: ' . $e->getMessage());
        }
    }

    /**
     * Send a test FCM notification.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function send(Request $request)
    {
        // Validate form
        $request->validate([
            'title' => 'required|string|max:255',
            'message' => 'required|string',
            'category' => 'required|string',
            'severity' => 'required|string|in:low,medium,high',
            'device_type' => 'required|string|in:all,android,ios,web',
            'token' => 'nullable|string', // Optional specific token
        ]);

        try {
            // Create notification in database first
            $notification = Notification::create([
                'title' => $request->title,
                'message' => $request->message,
                'category' => $request->category,
                'severity' => $request->severity,
                'sent' => false,
            ]);

            // Get tokens
            if ($request->filled('token')) {
                // Send to specific token
                $tokens = [$request->token];
            } else {
                // Query to get active tokens
                $query = DeviceToken::where('is_active', true);

                // Filter by device type if specified
                if ($request->device_type !== 'all') {
                    $query->where('device_type', $request->device_type);
                }

                $tokens = $query->pluck('token')->toArray();
            }

            if (empty($tokens)) {
                return redirect()->route('test-fcm')
                    ->with('error', 'No active device tokens found for the selected device type.');
            }

            // Send notification using FCM service
            try {
                $result = $this->fcmService->sendNotification($notification, $tokens);

                if (!$result['success']) {
                    // Check if the error is related to Firebase configuration
                    if (str_contains($result['message'], 'Firebase service account file')) {
                        // Show a more helpful error message with setup instructions
                        $setupInstructions = storage_path('FIREBASE_SETUP_INSTRUCTIONS.md');
                        $instructionsExist = file_exists($setupInstructions);

                        $errorMessage = $result['message'] . '<br><br>';
                        $errorMessage .= '<strong>To fix this issue:</strong><br>';
                        $errorMessage .= '1. Create a Firebase project at <a href="https://console.firebase.google.com/" target="_blank">https://console.firebase.google.com/</a><br>';
                        $errorMessage .= '2. Generate a service account key from Project Settings > Service Accounts<br>';
                        $errorMessage .= '3. Save the key as <code>firebase-service-account.json</code> in the <code>storage</code> directory<br>';

                        if ($instructionsExist) {
                            $errorMessage .= '<br>Detailed setup instructions are available in <code>storage/FIREBASE_SETUP_INSTRUCTIONS.md</code>';
                        }

                        return redirect()->route('test-fcm')
                            ->with('error', $errorMessage);
                    }

                    // If FCM service fails for other reasons, try simulation mode
                    $notificationData = [
                        'title' => $request->title,
                        'body' => $request->message,
                        'category' => $request->category,
                        'severity' => $request->severity,
                        'token' => $request->filled('token') ? $request->token : null,
                        'time' => now()->toIso8601String(),
                    ];

                    try {
                        // Call the simulated notification endpoint
                        $client = new \GuzzleHttp\Client();
                        $response = $client->post(url('/api/simulated-notification'), [
                            'json' => $notificationData,
                            'headers' => [
                                'Accept' => 'application/json',
                                'Content-Type' => 'application/json',
                            ],
                        ]);

                        // Mark notification as sent
                        $notification->sent = true;
                        $notification->save();

                        // Get the response data
                        $responseData = json_decode($response->getBody(), true);

                        // Log the response
                        Log::info('Simulated notification sent', [
                            'response' => $responseData,
                            'notification_id' => $notification->id
                        ]);

                        return redirect()->route('test-fcm')
                            ->with('warning', 'Firebase service is not available. Using simulation mode. Check the Ionic app for the notification.');
                    } catch (\Exception $e) {
                        Log::error('Failed to send simulated notification', [
                            'error' => $e->getMessage(),
                            'trace' => $e->getTraceAsString()
                        ]);

                        return redirect()->route('test-fcm')
                            ->with('error', 'Failed to send notification: ' . $result['message'] . ' and simulation failed: ' . $e->getMessage());
                    }
                }

                // Mark notification as sent (should already be done by FCM service, but just to be sure)
                $notification->sent = true;
                $notification->save();

                Log::info('Test notification process completed', [
                    'id' => $notification->id,
                    'sent' => $notification->sent,
                    'success_count' => $result['success_count'],
                    'failure_count' => $result['failure_count'],
                    'invalid_tokens' => $result['invalid_tokens']
                ]);

                return redirect()->route('test-fcm')
                    ->with('success', "Test notification sent to {$result['success_count']} devices" .
                        ($result['failure_count'] > 0 ? " ({$result['failure_count']} failures)" : ""));
            } catch (\Exception $e) {
                Log::error('Failed to send notification via FCM service', [
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString()
                ]);

                return redirect()->route('test-fcm')
                    ->with('error', 'Failed to send notification: ' . $e->getMessage());
            }
        } catch (\Exception $e) {
            Log::error('Failed to send test notification', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return redirect()->route('test-fcm')
                ->with('error', 'Failed to send notification: ' . $e->getMessage());
        }
    }
}
