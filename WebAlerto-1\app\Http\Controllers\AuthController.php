<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\UserMobile;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\ValidationException;

class AuthController extends Controller
{
    // Register
    public function signup(Request $request)
    {
        $request->validate([
            'full_name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:alertoss',
            'password' => 'required|string|min:8',
        ]);

        $user = UserMobile::create([
            'full_name' => $request->full_name,
            'email' => $request->email,
            'password' => Hash::make($request->password),
        ]);

        return response()->json([
            'message' => 'User created successfully',
            'user' => $user,
        ], 201);
    }

    // Login
    public function login(Request $request)
    {
        $request->validate([
            'email' => 'required|email',
            'password' => 'required',
        ]);

        $user = UserMobile::where('email', $request->email)->first();

        if (!$user || !Hash::check($request->password, $user->password)) {
            return response()->json([
                'message' => 'Invalid credentials',
                'errors' => ['email' => ['The provided credentials are incorrect.']]
            ], 401);
        }

        // Create a token for the user
        $token = $user->createToken('auth-token')->plainTextToken;

        // Return user data without sensitive information
        $userData = [
            'id' => $user->id,
            'full_name' => $user->full_name,
            'email' => $user->email,
        ];

        return response()->json([
            'token' => $token,
            'user' => $userData,
            'message' => 'Login successful'
        ]);
    }
}
