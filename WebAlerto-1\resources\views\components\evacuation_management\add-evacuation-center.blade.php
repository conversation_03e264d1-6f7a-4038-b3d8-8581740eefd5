@extends('layout.app')

@section('title', 'Add Evacuation Center')

@section('content')
<!-- Add loading overlay -->
<div id="mapLoadingOverlay" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
    <div class="bg-white p-6 rounded-lg shadow-xl text-center">
        <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
        <p class="text-gray-700">Loading map...</p>
    </div>
</div>

<!-- Add error message container -->
<div id="mapError" class="hidden bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-4" role="alert">
    <strong class="font-bold">Error!</strong>
    <span class="block sm:inline">Failed to load the map. Please check your internet connection and try again.</span>
    <button onclick="retryMapLoad()" class="absolute top-0 bottom-0 right-0 px-4 py-3">
        <svg class="fill-current h-6 w-6 text-red-500" role="button" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20">
            <title>Retry</title>
            <path d="M14.66 15.66A8 8 0 1 1 17 10h-2a6 6 0 1 0-1.76 4.24l1.42 1.42zM12 10h8l-4 4-4-4z"/>
        </svg>
    </button>
</div>

<div class="container mx-auto p-6">
    <!-- Step 1: Basic Information -->
    <div id="step1" class="relative bg-white p-6 rounded shadow-md">
        <a href="{{ route('components.evacuation_management.evacuation-dashboard') }}"
            class="absolute top-6 right-6 text-gray-500 hover:text-red-500 text-4xl font-bold leading-none">
            &times;
        </a>

        <h2 class="text-2xl font-bold mb-4">Add Evacuation Center - Step 1</h2>
        <p class="text-gray-600 mb-4">Enter the basic information for the evacuation center.</p>

        <form id="evacuationForm">
            <div class="mb-4">
                <label for="name" class="block text-gray-700 font-bold mb-2">Center Name</label>
                <input type="text" id="name" name="name" class="border border-gray-300 rounded px-4 py-2 w-full" required>
            </div>
            <div class="mb-4">
                <label for="capacity" class="block text-gray-700 font-bold mb-2">Capacity</label>
                <input type="number" id="capacity" name="capacity" class="border border-gray-300 rounded px-4 py-2 w-full" required>
            </div>
            <div class="mb-4">
                <label for="contact" class="block text-gray-700 font-bold mb-2">Contact Number</label>
                <input type="text" id="contact" name="contact" class="border border-gray-300 rounded px-4 py-2 w-full" required>
            </div>
            <div class="mb-4">
                <label for="disaster_type" class="block text-gray-700 font-bold mb-2">Disaster Type</label>
                <select id="disaster_type" name="disaster_type" class="border border-gray-300 rounded px-4 py-2 w-full" required>
                    <option value="Typhoon">Typhoon (Red Marker)</option>
                    <option value="Flood">Flood (Green Marker)</option>
                    <option value="Earthquake">Earthquake (Yellow-Orange Marker)</option>
                </select>
            </div>
            <div class="mb-4">
                <label for="status" class="block text-gray-700 font-bold mb-2">Status</label>
                <select id="status" name="status" class="border border-gray-300 rounded px-4 py-2 w-full" required>
                    <option value="Active">Active</option>
                    <option value="Inactive">Inactive</option>
                </select>
            </div>
            <div class="flex justify-end gap-4">
                <a href="{{ route('components.evacuation_management.evacuation-dashboard') }}"
                   class="bg-gray-500 text-white px-4 py-2 rounded hover:bg-gray-600">Cancel</a>
                <button type="button" id="nextStep" class="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600">Next: Confirm Location</button>
            </div>
        </form>
    </div>

    <!-- Step 2: Location Confirmation -->
    <div id="step2" class="relative bg-white p-6 rounded shadow-md mt-6 hidden">
        <h2 class="text-2xl font-bold mb-4">Confirm Location - Step 2</h2>
        <p class="text-gray-600 mb-4">Search for the location and confirm the marker position on the map.</p>

        <div class="mb-4">
            <label for="search" class="block text-gray-700 font-bold mb-2">Search Location</label>
            <div class="relative">
                <input type="text" id="search" class="border border-gray-300 rounded px-4 py-2 w-full" placeholder="Search for a location in Cebu City...">
                <div id="searchResults" class="absolute z-10 w-full bg-white border border-gray-300 rounded-b mt-1 hidden"></div>
            </div>
        </div>

        <!-- Map Preview with loading state -->
        <div class="bg-white p-6 rounded shadow-md">
            <div id="map" class="h-96 w-full rounded-lg relative">
                <!-- Add loading indicator inside map container -->
                <div id="mapLoadingIndicator" class="absolute inset-0 bg-gray-100 flex items-center justify-center">
                    <div class="text-center">
                        <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
                        <p class="text-gray-600">Loading map...</p>
                    </div>
                </div>
            </div>
            <div class="mt-4 text-sm text-gray-600">
                <p>• Search for a location or click on the map to set the center's position</p>
                <p>• Drag the marker to adjust the location if needed</p>
                <p>• Confirm the location is correct before saving</p>
            </div>
        </div>

        <!-- Location Details -->
        <div class="mt-4 p-4 bg-gray-50 rounded-lg">
            <h3 class="font-bold text-gray-700 mb-2">Selected Location Details:</h3>
            <p id="selectedAddress" class="text-gray-600">No location selected yet</p>
            <p id="selectedCoordinates" class="text-gray-600 text-sm mt-1"></p>
        </div>

        <div class="flex justify-end gap-4 mt-6">
            <button type="button" id="prevStep" class="bg-gray-500 text-white px-4 py-2 rounded hover:bg-gray-600">Back</button>
            <form id="finalForm" action="{{ route('components.evacuation_management.store') }}" method="POST" class="inline">
                @csrf
                <input type="hidden" id="latitude" name="latitude" required>
                <input type="hidden" id="longitude" name="longitude" required>
                <input type="hidden" id="street_name" name="street_name" required>
                <input type="hidden" id="province" name="province" required>
                <input type="hidden" id="city" name="city" required>
                <input type="hidden" id="postal_code" name="postal_code" required>
                <input type="hidden" id="barangay" name="barangay" required>
                <input type="hidden" id="finalName" name="name">
                <input type="hidden" id="finalCapacity" name="capacity">
                <input type="hidden" id="finalContact" name="contact">
                <input type="hidden" id="finalDisasterType" name="disaster_type">
                <input type="hidden" id="finalStatus" name="status">
                <button type="submit" id="saveButton" class="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600" disabled>Save Center</button>
            </form>
        </div>
    </div>
</div>

<!-- Add Leaflet CSS and JS with integrity checks -->
<link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css"
      integrity="sha256-p4NxAoJBhIIN+hmNHrzRCf9tD/miZyoHS5obTRR9BMY="
      crossorigin=""/>
<script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"
        integrity="sha256-20nQCchB9co0qIjJZRGuk2/Z9VM+kNiyxNV1lvTlZBo="
        crossorigin=""></script>

<script>
let map;
let marker;
let searchTimeout;
let mapLoadAttempts = 0;
const MAX_LOAD_ATTEMPTS = 3;

// Function to initialize map
function initializeMap() {
    try {
        // Show loading overlay
        document.getElementById('mapLoadingOverlay').classList.remove('hidden');
        
        // Initialize map centered on Cebu City
        map = L.map('map', {
            center: [10.3157, 123.8854],
            zoom: 13,
            zoomControl: true,
            attributionControl: true
        });

        // Add OpenStreetMap tiles with error handling
        L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
            attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors',
            maxZoom: 19,
            subdomains: 'abc'
        }).addTo(map);

        // Hide loading indicators when map is ready
        map.on('load', function() {
            document.getElementById('mapLoadingOverlay').classList.add('hidden');
            document.getElementById('mapLoadingIndicator').classList.add('hidden');
        });

        // Handle map load errors
        map.on('error', function(e) {
            console.error('Map error:', e);
            showMapError();
        });

        // Add marker on map click
        map.on('click', function(e) {
            updateMarker(e.latlng.lat, e.latlng.lng);
        });

        // Hide loading indicator after a timeout (in case load event doesn't fire)
        setTimeout(() => {
            document.getElementById('mapLoadingOverlay').classList.add('hidden');
            document.getElementById('mapLoadingIndicator').classList.add('hidden');
        }, 5000);

    } catch (error) {
        console.error('Error initializing map:', error);
        showMapError();
    }
}

// Function to show map error
function showMapError() {
    document.getElementById('mapLoadingOverlay').classList.add('hidden');
    document.getElementById('mapLoadingIndicator').classList.add('hidden');
    document.getElementById('mapError').classList.remove('hidden');
}

// Function to retry map loading
function retryMapLoad() {
    if (mapLoadAttempts < MAX_LOAD_ATTEMPTS) {
        mapLoadAttempts++;
        document.getElementById('mapError').classList.add('hidden');
        document.getElementById('mapLoadingOverlay').classList.remove('hidden');
        initializeMap();
    } else {
        alert('Failed to load map after multiple attempts. Please refresh the page or try again later.');
    }
}

// Initialize map when step 2 becomes visible
const observer = new MutationObserver((mutations) => {
    mutations.forEach((mutation) => {
        if (mutation.target.id === 'step2' && !mutation.target.classList.contains('hidden')) {
            if (!map) {
                initializeMap();
            }
        }
    });
});

observer.observe(document.getElementById('step2'), {
    attributes: true,
    attributeFilter: ['class']
});

// Step Navigation
document.getElementById('nextStep').addEventListener('click', function() {
    const form = document.getElementById('evacuationForm');
    if (form.checkValidity()) {
        document.getElementById('step1').classList.add('hidden');
        document.getElementById('step2').classList.remove('hidden');
    } else {
        form.reportValidity();
    }
});

document.getElementById('prevStep').addEventListener('click', function() {
    document.getElementById('step2').classList.add('hidden');
    document.getElementById('step1').classList.remove('hidden');
});

// Initialize Nominatim geocoder
const searchInput = document.getElementById('search');
const searchResults = document.getElementById('searchResults');
const selectedAddress = document.getElementById('selectedAddress');
const selectedCoordinates = document.getElementById('selectedCoordinates');
const saveButton = document.getElementById('saveButton');

// Handle search input
searchInput.addEventListener('input', function() {
    clearTimeout(searchTimeout);
    const query = this.value.trim();
    
    if (query.length < 3) {
        searchResults.classList.add('hidden');
        return;
    }

    searchTimeout = setTimeout(() => {
        // Show loading state
        searchResults.innerHTML = '<div class="p-2 text-gray-500">Searching...</div>';
        searchResults.classList.remove('hidden');

        // Geocode the address
        geocodeAddress(query);
    }, 500);
});

// Function to geocode address
function geocodeAddress(address) {
    fetch(`https://nominatim.openstreetmap.org/search?format=json&q=${encodeURIComponent(address + ', Cebu City')}&limit=5`)
        .then(response => {
            if (!response.ok) {
                throw new Error('Network response was not ok');
            }
            return response.json();
        })
        .then(data => {
            searchResults.innerHTML = '';
            if (data.length > 0) {
                data.forEach(result => {
                    const div = document.createElement('div');
                    div.className = 'p-2 hover:bg-gray-100 cursor-pointer';
                    div.textContent = result.display_name;
                    div.addEventListener('click', () => {
                        const lat = parseFloat(result.lat);
                        const lon = parseFloat(result.lon);
                        
                        // Update marker and map view
                        updateMarker(lat, lon);
                        
                        // Update search input with selected address
                        searchInput.value = result.display_name;
                        
                        // Hide search results
                        searchResults.classList.add('hidden');
                        
                        // Update form fields with address components
                        const address = result.address || {};
                        document.getElementById('street_name').value = address.road || address.pedestrian || address.path || '';
                        document.getElementById('province').value = address.state || 'Cebu';
                        document.getElementById('city').value = address.city || address.town || 'Cebu City';
                        document.getElementById('postal_code').value = address.postcode || '';
                        document.getElementById('barangay').value = address.suburb || 
                                                                  address.neighbourhood || 
                                                                  address.quarter || 
                                                                  address.hamlet || '';
                        
                        // Update display
                        selectedAddress.textContent = result.display_name;
                        selectedCoordinates.textContent = `Latitude: ${lat.toFixed(6)}, Longitude: ${lon.toFixed(6)}`;
                        
                        // Enable save button
                        saveButton.disabled = false;
                    });
                    searchResults.appendChild(div);
                });
                searchResults.classList.remove('hidden');
            } else {
                searchResults.innerHTML = '<div class="p-2 text-gray-500">No results found</div>';
                searchResults.classList.remove('hidden');
            }
        })
        .catch(error => {
            console.error('Error during geocoding:', error);
            searchResults.innerHTML = '<div class="p-2 text-red-500">Search failed. Please try again.</div>';
            searchResults.classList.remove('hidden');
        });
}

// Close search results when clicking outside
document.addEventListener('click', function(e) {
    if (!searchInput.contains(e.target) && !searchResults.contains(e.target)) {
        searchResults.classList.add('hidden');
    }
});

function updateMarker(lat, lng) {
    // Remove existing marker if it exists
    if (marker) {
        map.removeLayer(marker);
    }

    // Create new marker
    marker = L.marker([lat, lng], { 
        draggable: true,
        autoPan: true
    }).addTo(map);
    
    // Add drag end event listener
    marker.on('dragend', function(e) {
        const position = marker.getLatLng();
        reverseGeocode(position.lat, position.lng);
    });

    // Center map on marker
    map.setView([lat, lng], 16);
    
    // Update hidden form fields
    document.getElementById('latitude').value = lat;
    document.getElementById('longitude').value = lng;
    
    // Update selected location details
    selectedCoordinates.textContent = `Latitude: ${lat.toFixed(6)}, Longitude: ${lng.toFixed(6)}`;
    
    // Enable save button
    saveButton.disabled = false;
    
    // Reverse geocode to get address
    reverseGeocode(lat, lng);
}

function reverseGeocode(lat, lng) {
    fetch(`https://nominatim.openstreetmap.org/reverse?format=json&lat=${lat}&lon=${lng}`)
        .then(response => {
            if (!response.ok) {
                throw new Error('Network response was not ok');
            }
            return response.json();
        })
        .then(data => {
            console.log('Reverse geocoding response:', data); // Debug log
            
            // Parse address components
            const address = data.address;
            
            // Set street name
            document.getElementById('street_name').value = address.road || address.pedestrian || address.path || '';
            
            // Set province (default to Cebu if not found)
            document.getElementById('province').value = address.state || 'Cebu';
            
            // Set city (default to Cebu City if not found)
            document.getElementById('city').value = address.city || address.town || 'Cebu City';
            
            // Set postal code
            document.getElementById('postal_code').value = address.postcode || '';
            
            // Set barangay (try different possible fields)
            document.getElementById('barangay').value = address.suburb || 
                                                      address.neighbourhood || 
                                                      address.quarter || 
                                                      address.hamlet || 
                                                      '';
            
            // Log the values being set
            console.log('Setting form values:', {
                street_name: document.getElementById('street_name').value,
                province: document.getElementById('province').value,
                city: document.getElementById('city').value,
                postal_code: document.getElementById('postal_code').value,
                barangay: document.getElementById('barangay').value
            });
            
            // Update display
            selectedAddress.textContent = data.display_name;
        })
        .catch(error => {
            console.error('Error during reverse geocoding:', error);
            selectedAddress.textContent = 'Address lookup failed. Please try again.';
        });
}

// Handle final form submission
document.getElementById('finalForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    // Log all form values before validation
    console.log('Form values before validation:', {
        latitude: document.getElementById('latitude').value,
        longitude: document.getElementById('longitude').value,
        street_name: document.getElementById('street_name').value,
        province: document.getElementById('province').value,
        city: document.getElementById('city').value,
        postal_code: document.getElementById('postal_code').value,
        barangay: document.getElementById('barangay').value
    });
    
    // Validate required fields
    const requiredFields = ['latitude', 'longitude', 'street_name', 'province', 'city', 'postal_code', 'barangay'];
    let isValid = true;
    
    requiredFields.forEach(field => {
        const value = document.getElementById(field).value;
        if (!value) {
            isValid = false;
            console.log(`Missing value for ${field}:`, value); // Debug log
            alert(`Please select a location on the map to get the ${field.replace('_', ' ')}`);
        }
    });
    
    if (!isValid) {
        return;
    }
    
    // Copy values from step 1 to hidden fields
    document.getElementById('finalName').value = document.getElementById('name').value;
    document.getElementById('finalCapacity').value = document.getElementById('capacity').value;
    document.getElementById('finalContact').value = document.getElementById('contact').value;
    document.getElementById('finalDisasterType').value = document.getElementById('disaster_type').value;
    document.getElementById('finalStatus').value = document.getElementById('status').value;
    
    // Log final form data before submission
    const formData = new FormData(this);
    console.log('Final form data before submission:');
    for (let [key, value] of formData.entries()) {
        console.log(`${key}: ${value}`);
    }
    
    // Submit the form
    this.submit();
});
</script>
@endsection