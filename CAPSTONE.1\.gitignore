# Ignore everything at the root level except our project folders
/*
!/WebAlerto-1/
!/mobile_ionic/
!.gitignore
!README.md

# Node modules
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Laravel
/WebAlerto-1/vendor/
/WebAlerto-1/.env
/WebAlerto-1/storage/logs/
/WebAlerto-1/storage/framework/cache/
/WebAlerto-1/storage/framework/sessions/
/WebAlerto-1/storage/framework/views/
/WebAlerto-1/bootstrap/cache/

# Ionic/Angular
/mobile_ionic/dist/
/mobile_ionic/tmp/
/mobile_ionic/out-tsc/
/mobile_ionic/bazel-out/

# Android build files (these cause path length issues)
/mobile_ionic/android/app/build/
/mobile_ionic/android/capacitor-cordova-android-plugins/build/
/mobile_ionic/android/.gradle/
/mobile_ionic/android/build/
*.dex

# iOS build files
/mobile_ionic/ios/build/

# IDE files
.vscode/
.idea/
*.swp
*.swo

# OS files
.DS_Store
Thumbs.db

# Logs
*.log

# Environment files
.env
.env.local
.env.production
