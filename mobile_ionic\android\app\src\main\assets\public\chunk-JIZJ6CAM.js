import{a as c}from"./chunk-MCRJI3T3.js";import{a as d,f as u}from"./chunk-BAKMWPBW.js";import{h as l}from"./chunk-LNJ3S2LQ.js";var h=()=>d.get("experimentalCloseWatcher",!1)&&c!==void 0&&"CloseWatcher"in c,E=()=>{document.addEventListener("backbutton",()=>{})},k=()=>{let a=document,r=!1,s=()=>{if(r)return;let o=0,n=[],f=new CustomEvent("ionBackButton",{bubbles:!1,detail:{register(t,e){n.push({priority:t,handler:e,id:o++})}}});a.dispatchEvent(f);let p=t=>l(void 0,null,function*(){try{if(t!=null&&t.handler){let e=t.handler(i);e!=null&&(yield e)}}catch(e){u("[ion-app] - Exception in startHardwareBackButton:",e)}}),i=()=>{if(n.length>0){let t={priority:Number.MIN_SAFE_INTEGER,handler:()=>{},id:-1};n.forEach(e=>{e.priority>=t.priority&&(t=e)}),r=!0,n=n.filter(e=>e.id!==t.id),p(t).then(()=>r=!1)}};i()};if(h()){let o,n=()=>{o==null||o.destroy(),o=new c.CloseWatcher,o.onclose=()=>{s(),n()}};n()}else a.addEventListener("backbutton",s)},m=100,v=99;export{h as a,E as b,k as c,m as d,v as e};
