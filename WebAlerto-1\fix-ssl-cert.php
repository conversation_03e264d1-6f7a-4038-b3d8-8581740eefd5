<?php
// <PERSON><PERSON><PERSON> to fix SSL certificate issues for FCM

// Step 1: Download the latest cacert.pem
echo "Downloading latest CA certificates...\n";
$certUrl = 'https://curl.se/ca/cacert.pem';
$certDir = __DIR__ . '/storage/certs';
$certPath = $certDir . '/cacert.pem';

// Create directory if it doesn't exist
if (!is_dir($certDir)) {
    if (!mkdir($certDir, 0755, true)) {
        echo "Error: Failed to create directory {$certDir}\n";
        exit(1);
    }
}

// Download the certificate with SSL verification disabled (just for this download)
$ch = curl_init($certUrl);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
$certContent = curl_exec($ch);

if ($certContent === false) {
    echo "Error downloading certificate: " . curl_error($ch) . "\n";
    exit(1);
}

curl_close($ch);

// Save the certificate
if (file_put_contents($certPath, $certContent) === false) {
    echo "Error saving certificate to {$certPath}\n";
    exit(1);
}

echo "Certificate saved to {$certPath}\n";

// Step 2: Update the FCMService to use the certificate
echo "\nUpdating FCMService to use the certificate...\n";

$fcmServicePath = __DIR__ . '/app/Services/FCMService.php';
if (!file_exists($fcmServicePath)) {
    echo "Error: FCMService.php not found at {$fcmServicePath}\n";
    exit(1);
}

$fcmServiceContent = file_get_contents($fcmServicePath);
if ($fcmServiceContent === false) {
    echo "Error reading FCMService.php\n";
    exit(1);
}

// Check if we need to modify the constructor
if (strpos($fcmServiceContent, 'curl.cainfo') === false) {
    // Find the constructor
    $pattern = '/public function __construct\(\)\s*\{([^}]+)\}/s';
    if (preg_match($pattern, $fcmServiceContent, $matches)) {
        $constructorBody = $matches[1];
        $newConstructorBody = $constructorBody . "\n        // Set CA certificate path for cURL\n";
        $newConstructorBody .= "        if (!ini_get('curl.cainfo')) {\n";
        $newConstructorBody .= "            $certPathVar = storage_path('certs/cacert.pem');\n";
        $newConstructorBody .= "            if (file_exists($certPathVar)) {\n";
        $newConstructorBody .= "                ini_set('curl.cainfo', $certPathVar);\n";
        $newConstructorBody .= "                ini_set('openssl.cafile', $certPathVar);\n";
        $newConstructorBody .= "                Log::info('Set CA certificate path for cURL', ['path' => $certPathVar]);\n";
        $newConstructorBody .= "            } else {\n";
        $newConstructorBody .= "                Log::warning('CA certificate file not found', ['path' => $certPathVar]);\n";
        $newConstructorBody .= "            }\n";
        $newConstructorBody .= "        }";
        
        $fcmServiceContent = str_replace($constructorBody, $newConstructorBody, $fcmServiceContent);
        
        if (file_put_contents($fcmServicePath, $fcmServiceContent) === false) {
            echo "Error updating FCMService.php\n";
            exit(1);
        }
        
        echo "FCMService.php updated successfully\n";
    } else {
        echo "Could not find constructor in FCMService.php\n";
    }
} else {
    echo "FCMService.php already contains CA certificate configuration\n";
}

// Step 3: Create a test script to verify the fix
echo "\nCreating test script...\n";

$testScriptPath = __DIR__ . '/test-ssl-fix.php';
$testScriptContent = <<<'EOT'
<?php
require __DIR__.'/vendor/autoload.php';

// Get the application instance
$app = require_once __DIR__.'/bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

// Test connection to Google OAuth endpoint
echo "Testing connection to Google OAuth endpoint...\n";
$ch = curl_init('https://oauth2.googleapis.com/token');
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_HEADER, true);
curl_setopt($ch, CURLOPT_NOBODY, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 10);
curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, true);
curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 2);

// Execute the request
$response = curl_exec($ch);
$error = curl_error($ch);
$info = curl_getinfo($ch);
curl_close($ch);

if ($response === false) {
    echo "Error connecting to Google OAuth endpoint: " . $error . "\n";
    echo "The SSL certificate issue is still present.\n";
} else {
    echo "Connection successful!\n";
    echo "HTTP Status Code: " . $info['http_code'] . "\n";
    echo "The SSL certificate issue has been fixed.\n";
    
    // Now test sending a notification
    echo "\nTesting FCM notification...\n";
    $fcmService = app(\App\Services\FCMService::class);
    
    // Create a test notification
    $notification = new \App\Models\Notification([
        'title' => 'SSL Test Notification',
        'message' => 'Testing after SSL certificate fix',
        'category' => 'General',
        'severity' => 'medium',
        'sent' => false
    ]);
    $notification->save();
    
    // Get all active tokens
    $tokens = \App\Models\DeviceToken::where('is_active', true)->pluck('token')->toArray();
    
    if (empty($tokens)) {
        echo "No active device tokens found. Please register a device token first.\n";
    } else {
        echo "Found " . count($tokens) . " active device tokens.\n";
        
        // Send notification
        $result = $fcmService->sendNotification($notification, $tokens);
        
        echo "Result: " . ($result['success'] ? 'Success' : 'Failure') . "\n";
        echo "Message: " . $result['message'] . "\n";
        echo "Success count: " . $result['success_count'] . "\n";
        echo "Failure count: " . $result['failure_count'] . "\n";
        echo "Invalid tokens: " . $result['invalid_tokens'] . "\n";
    }
}
EOT;

if (file_put_contents($testScriptPath, $testScriptContent) === false) {
    echo "Error creating test script\n";
    exit(1);
}

echo "Test script created at {$testScriptPath}\n";

// Step 4: Instructions
echo "\n=== INSTRUCTIONS ===\n";
echo "1. The CA certificate has been downloaded to: {$certPath}\n";
echo "2. The FCMService has been updated to use this certificate\n";
echo "3. To test if the fix worked, run: php test-ssl-fix.php\n";
echo "4. If the issue persists, you may need to update your php.ini file manually:\n";
echo "   - Open: C:\\jsp6b30\\php\\php.ini\n";
echo "   - Add these lines:\n";
echo "     curl.cainfo = \"{$certPath}\"\n";
echo "     openssl.cafile = \"{$certPath}\"\n";
echo "   - Restart your web server\n";
echo "5. If you're still having issues, you can temporarily disable SSL verification in FCMService.php\n";
echo "   (NOT RECOMMENDED FOR PRODUCTION)\n";
echo "\nGood luck!\n";
