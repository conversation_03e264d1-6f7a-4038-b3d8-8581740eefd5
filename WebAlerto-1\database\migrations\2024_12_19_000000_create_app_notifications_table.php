<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('app_notifications', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('user_id')->nullable(); // null for broadcast notifications
            $table->enum('type', [
                'evacuation_center_added',
                'emergency_alert', 
                'system_update',
                'general'
            ])->default('general');
            $table->string('title');
            $table->text('message');
            $table->json('data')->nullable(); // Additional data like evacuation center details
            $table->boolean('read')->default(false);
            $table->integer('reactions')->default(0);
            $table->timestamp('sent_at')->nullable();
            $table->timestamps();

            // Indexes for performance
            $table->index(['user_id', 'read', 'created_at']);
            $table->index(['type', 'created_at']);
            $table->index('sent_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('app_notifications');
    }
};
