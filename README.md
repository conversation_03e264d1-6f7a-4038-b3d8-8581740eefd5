# CAPSTONE.1 - WebAlerto Disaster Management System

A comprehensive disaster management system consisting of a Laravel backend (WebAlerto-1) and an Ionic mobile application (mobile_ionic) for real-time disaster alerts and evacuation management.

## 🏗️ Project Structure

```
CAPSTONE.1/
├── WebAlerto-1/                 # Laravel Backend API
├── mobile_ionic/                # Ionic Mobile Application
├── android/                     # Android build files
├── android_native_examples/     # Native Android examples
├── ANDROID_SETUP_GUIDE.md      # Android setup instructions
└── setup_android_project.bat   # Android setup script
```

## 🚀 Features

### Backend (WebAlerto-1)
- **User Management**: Authentication and authorization system
- **Disaster Alerts**: Real-time alert creation and management
- **Evacuation Centers**: Management of evacuation center locations
- **Push Notifications**: Firebase Cloud Messaging (FCM) integration
- **API Endpoints**: RESTful API for mobile app communication
- **Dashboard**: Web-based admin dashboard

### Mobile App (mobile_ionic)
- **Real-time Alerts**: Receive disaster notifications instantly
- **Interactive Maps**: View evacuation centers and routes
- **User Registration**: Account creation and management
- **Offline Support**: Basic functionality without internet
- **Multi-platform**: iOS and Android support

## 🛠️ Technology Stack

### Backend
- **Framework**: Laravel 11
- **Database**: MySQL/SQLite
- **Authentication**: Laravel Sanctum
- **Push Notifications**: Firebase Cloud Messaging
- **Testing**: PHPUnit

### Mobile App
- **Framework**: Ionic 7 with Angular
- **Maps**: Leaflet.js
- **Push Notifications**: Capacitor Firebase
- **Build Tool**: Capacitor
- **Testing**: Jasmine/Karma

## 📋 Prerequisites

- **PHP** >= 8.1
- **Composer**
- **Node.js** >= 16
- **npm** or **yarn**
- **Android Studio** (for Android development)
- **MySQL** or **SQLite**

## 🔧 Installation

### Backend Setup (WebAlerto-1)

1. Navigate to the backend directory:
   ```bash
   cd WebAlerto-1
   ```

2. Install PHP dependencies:
   ```bash
   composer install
   ```

3. Copy environment file:
   ```bash
   cp .env.example .env
   ```

4. Generate application key:
   ```bash
   php artisan key:generate
   ```

5. Configure database in `.env` file

6. Run migrations:
   ```bash
   php artisan migrate
   ```

7. Start the development server:
   ```bash
   php artisan serve
   ```

### Mobile App Setup (mobile_ionic)

1. Navigate to the mobile app directory:
   ```bash
   cd mobile_ionic
   ```

2. Install dependencies:
   ```bash
   npm install
   ```

3. Build the app:
   ```bash
   ionic build
   ```

4. Add platforms:
   ```bash
   ionic capacitor add android
   ionic capacitor add ios
   ```

5. Run on device/emulator:
   ```bash
   ionic capacitor run android
   ```

## 🔥 Firebase Setup

1. Create a Firebase project
2. Enable Cloud Messaging
3. Download `google-services.json` for Android
4. Place in `mobile_ionic/android/app/`
5. Configure FCM in Laravel backend

## 📱 Android Development

See `ANDROID_SETUP_GUIDE.md` for detailed Android setup instructions.

## 🧪 Testing

### Backend Tests
```bash
cd WebAlerto-1
php artisan test
```

### Mobile App Tests
```bash
cd mobile_ionic
npm run test
```

## 📚 API Documentation

The backend provides RESTful API endpoints for:
- User authentication
- Disaster alerts management
- Evacuation centers
- Push notifications
- Device token management

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License.

## 👥 Team

CAPSTONE.1 Development Team

## 📞 Support

For support and questions, please open an issue in the GitHub repository.
