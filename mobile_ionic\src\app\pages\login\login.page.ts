import { Component } from '@angular/core';
import { IonicModule, AlertController, Platform } from '@ionic/angular';
import { FormsModule } from '@angular/forms';
import { Router } from '@angular/router';
import { AuthService } from '../../services/auth.service';
import { FCM } from '@awesome-cordova-plugins/fcm/ngx';
import { HttpClient } from '@angular/common/http';
import { environment } from '../../../environments/environment';
import { FcmService } from '../../services/fcm.service';

@Component({
  standalone: true,
  imports: [IonicModule, FormsModule],
  selector: 'app-login',
  templateUrl: './login.page.html',
  styleUrls: ['./login.page.scss'],
})
export class LoginPage {
  credentials = {
    email: '',
    password: ''
  };
  errorMessage = '';
  fcmToken: string = '';

  // Add the goToRegister method
  goToRegister() {
    this.router.navigate(['/register']);
  }

  constructor(
    private router: Router,
    private authService: AuthService,
    private fcm: FCM,
    private http: HttpClient,
    private alertController: AlertController,
    private platform: Platform,
    private fcmService: FcmService
  ) {
    // Get FCM token when the page loads
    this.getFCMToken();
  }

  getFCMToken() {
    // For browser testing, create a mock token
    if (!this.platform.is('cordova') && !this.platform.is('capacitor')) {
      console.log('Running in browser, using mock FCM token');
      this.fcmToken = 'browser-mock-token-' + Math.random().toString(36).substring(2, 15);
      console.log('Mock FCM Token:', this.fcmToken);
      return;
    }

    // For real devices, get the actual token
    this.fcm.getToken().then(token => {
      console.log('FCM Token:', token);
      this.fcmToken = token;
    }).catch(error => {
      console.error('Error getting FCM token:', error);
      // Try using the FCM service as a fallback
      this.fcmService.getToken().then(token => {
        console.log('FCM Token from service:', token);
        this.fcmToken = token;
      }).catch(err => {
        console.error('Error getting FCM token from service:', err);
      });
    });
  }

  /**
   * Helper method to register a token with a specific endpoint
   * @param endpoint The API endpoint to use
   * @param payload The token payload to send
   * @param onSuccess Callback for successful registration
   * @param onError Callback for failed registration
   */
  registerTokenWithEndpoint(endpoint: string, payload: any, onSuccess: () => void, onError: () => void) {
    // Check if this token is already registered
    const storedToken = localStorage.getItem('fcm_token');
    if (storedToken === this.fcmToken) {
      console.log('Token already registered, skipping registration');
      if (onSuccess) onSuccess();
      return;
    }

    // Check if we're currently registering this token
    if (localStorage.getItem('fcm_token_registering') === 'true') {
      console.log('Token registration already in progress, skipping');
      if (onSuccess) onSuccess();
      return;
    }

    // Set a flag to indicate we're currently registering this token
    localStorage.setItem('fcm_token_registering', 'true');

    this.http.post(endpoint, payload).subscribe({
      next: (res) => {
        console.log(`FCM token registered with ${endpoint}:`, res);
        // Store the token in localStorage for potential recovery
        localStorage.setItem('fcm_token', this.fcmToken);
        // Clear the registering flag
        localStorage.removeItem('fcm_token_registering');
        if (onSuccess) onSuccess();
      },
      error: (err) => {
        console.error(`Error registering token with ${endpoint}:`, err);
        // Clear the registering flag
        localStorage.removeItem('fcm_token_registering');
        if (onError) onError();
      }
    });
  }

  async onLogin() {
    // Validate inputs
    if (!this.credentials.email || !this.credentials.password) {
      await this.presentAlert('Login Failed', 'Please enter both email and password.');
      return;
    }

    console.log('Login clicked', this.credentials);
    this.authService.login(this.credentials).subscribe({
      next: async (response) => {
        // Show success alert
        await this.presentSuccessAlert('Login Successful', 'Welcome, ' + response.user.full_name);

        // Store the authentication token
        localStorage.setItem('token', response.token);

        // Register the FCM token with the backend
        if (this.fcmToken) {
          console.log('Registering FCM token with backend:', this.fcmToken);

          // Include Firebase project ID in the request
          const payload: any = {
            token: this.fcmToken,
            device_type: this.platform.is('ios') ? 'ios' : 'android',
            project_id: environment.firebase.projectId
          };

          // Only include user_id if it exists
          if (response.user && response.user.id) {
            payload.user_id = response.user.id; // Associate the token with the user
          }

          console.log('Token registration payload:', payload);
          console.log('API URL:', `${environment.apiUrl}/device-token`);

          // Use the FCM service to register the token with the user ID
          this.fcmService.registerTokenWithBackend(this.fcmToken, response.user.id);

          // Navigate to welcome page
          this.router.navigate(['/welcome']);
        } else {
          console.warn('No FCM token available to register');
          // If no FCM token, just navigate to welcome page
          this.router.navigate(['/welcome']);
        }
      },
      error: (error) => {
        console.error('Login error:', error);
        this.errorMessage = error.error?.message || 'Login failed';

        // Show alert with appropriate message
        if (error.status === 401) {
          this.presentAlert('Login Failed', 'Invalid email or password. Please try again.');
        } else {
          this.presentAlert('Login Error', 'An error occurred during login. Please try again later.');
        }
      }
    });
  }

  async presentAlert(header: string, message: string) {
    const alert = await this.alertController.create({
      header: header,
      message: message,
      buttons: ['OK'],
      cssClass: 'login-alert'
    });

    await alert.present();
  }

  async presentSuccessAlert(header: string, message: string) {
    const alert = await this.alertController.create({
      header: header,
      message: message,
      buttons: ['OK'],
      cssClass: 'login-success-alert'
    });

    await alert.present();
  }

}