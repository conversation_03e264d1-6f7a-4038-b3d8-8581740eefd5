package com.yourpackage.webalerto;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;

public class DisasterAlert {
    private String title;
    private String message;
    private String category;
    private String severity;
    private long timestamp;
    private boolean isRead;
    private String notificationId;

    // Default constructor
    public DisasterAlert() {
        this.timestamp = System.currentTimeMillis();
        this.isRead = false;
    }

    // Constructor with all parameters
    public DisasterAlert(String title, String message, String category, String severity, long timestamp) {
        this.title = title;
        this.message = message;
        this.category = category;
        this.severity = severity;
        this.timestamp = timestamp;
        this.isRead = false;
    }

    // Constructor without timestamp (uses current time)
    public DisasterAlert(String title, String message, String category, String severity) {
        this(title, message, category, severity, System.currentTimeMillis());
    }

    // Getters
    public String getTitle() {
        return title;
    }

    public String getMessage() {
        return message;
    }

    public String getCategory() {
        return category;
    }

    public String getSeverity() {
        return severity;
    }

    public long getTimestamp() {
        return timestamp;
    }

    public boolean isRead() {
        return isRead;
    }

    public String getNotificationId() {
        return notificationId;
    }

    // Setters
    public void setTitle(String title) {
        this.title = title;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public void setSeverity(String severity) {
        this.severity = severity;
    }

    public void setTimestamp(long timestamp) {
        this.timestamp = timestamp;
    }

    public void setRead(boolean read) {
        isRead = read;
    }

    public void setNotificationId(String notificationId) {
        this.notificationId = notificationId;
    }

    // Utility methods
    public String getFormattedTime() {
        SimpleDateFormat sdf = new SimpleDateFormat("MMM dd, yyyy HH:mm", Locale.getDefault());
        return sdf.format(new Date(timestamp));
    }

    public String getTimeAgo() {
        long now = System.currentTimeMillis();
        long diff = now - timestamp;

        long seconds = diff / 1000;
        long minutes = seconds / 60;
        long hours = minutes / 60;
        long days = hours / 24;

        if (days > 0) {
            return days + " day" + (days > 1 ? "s" : "") + " ago";
        } else if (hours > 0) {
            return hours + " hour" + (hours > 1 ? "s" : "") + " ago";
        } else if (minutes > 0) {
            return minutes + " minute" + (minutes > 1 ? "s" : "") + " ago";
        } else {
            return "Just now";
        }
    }

    public int getSeverityColor() {
        if (severity == null) return android.R.color.darker_gray;
        
        switch (severity.toLowerCase()) {
            case "high":
                return android.R.color.holo_red_dark;
            case "medium":
                return android.R.color.holo_orange_dark;
            case "low":
                return android.R.color.holo_green_dark;
            default:
                return android.R.color.darker_gray;
        }
    }

    public int getCategoryIcon() {
        if (category == null) return R.drawable.ic_alert;
        
        switch (category.toLowerCase()) {
            case "earthquake":
                return R.drawable.ic_earthquake;
            case "typhoon":
                return R.drawable.ic_typhoon;
            case "flood":
                return R.drawable.ic_flood;
            case "fire":
                return R.drawable.ic_fire;
            case "landslide":
                return R.drawable.ic_landslide;
            case "tsunami":
                return R.drawable.ic_tsunami;
            default:
                return R.drawable.ic_alert;
        }
    }

    public String getCategoryDisplayName() {
        if (category == null) return "General Alert";
        
        switch (category.toLowerCase()) {
            case "earthquake":
                return "Earthquake";
            case "typhoon":
                return "Typhoon";
            case "flood":
                return "Flood";
            case "fire":
                return "Fire";
            case "landslide":
                return "Landslide";
            case "tsunami":
                return "Tsunami";
            default:
                return "General Alert";
        }
    }

    public String getSeverityDisplayName() {
        if (severity == null) return "Normal";
        
        switch (severity.toLowerCase()) {
            case "high":
                return "High Priority";
            case "medium":
                return "Medium Priority";
            case "low":
                return "Low Priority";
            default:
                return "Normal";
        }
    }

    public boolean isHighPriority() {
        return "high".equalsIgnoreCase(severity);
    }

    public boolean isMediumPriority() {
        return "medium".equalsIgnoreCase(severity);
    }

    public boolean isLowPriority() {
        return "low".equalsIgnoreCase(severity);
    }

    @Override
    public String toString() {
        return "DisasterAlert{" +
                "title='" + title + '\'' +
                ", message='" + message + '\'' +
                ", category='" + category + '\'' +
                ", severity='" + severity + '\'' +
                ", timestamp=" + timestamp +
                ", isRead=" + isRead +
                ", notificationId='" + notificationId + '\'' +
                '}';
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        DisasterAlert that = (DisasterAlert) o;

        if (timestamp != that.timestamp) return false;
        if (title != null ? !title.equals(that.title) : that.title != null) return false;
        if (message != null ? !message.equals(that.message) : that.message != null) return false;
        if (category != null ? !category.equals(that.category) : that.category != null) return false;
        return severity != null ? severity.equals(that.severity) : that.severity == null;
    }

    @Override
    public int hashCode() {
        int result = title != null ? title.hashCode() : 0;
        result = 31 * result + (message != null ? message.hashCode() : 0);
        result = 31 * result + (category != null ? category.hashCode() : 0);
        result = 31 * result + (severity != null ? severity.hashCode() : 0);
        result = 31 * result + (int) (timestamp ^ (timestamp >>> 32));
        return result;
    }
}
