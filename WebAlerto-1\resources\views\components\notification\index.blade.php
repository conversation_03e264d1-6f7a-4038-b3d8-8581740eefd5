@extends('layout.app')

@section('content')
<div class="container mx-auto px-4 py-10 max-w-6xl">
    <div class="flex flex-wrap gap-8 mb-8">
        <div class="w-full lg:w-1/2 bg-white border border-gray-200 rounded-xl shadow-lg p-8">
            <canvas id="notifChart" width="400" height="380"></canvas>
            <div id="monthlyCategoryCounts" class="hidden">
                {{ $monthlyCategoryCounts->toJson() }}
            </div>
        </div>
        
        <!-- Recent Alerts Panel -->
        <div class="w-full lg:w-1/3 bg-white rounded-xl shadow p-6 flex-1">
            <h3 class="text-lg font-semibold mb-4">Recent Alerts</h3>
            @if(count($recentAlerts) > 0)
                <ul class="space-y-2">
                    @foreach($recentAlerts as $alert)
                        <li class="flex items-center 
                            @if($alert->type == 'Flood') bg-blue-100 
                            @elseif($alert->type == 'Fire') bg-red-100 
                            @elseif($alert->type == 'Earthquake') bg-yellow-100 
                            @else bg-gray-100 @endif 
                            rounded px-3 py-2">
                            <span class="mr-2 
                                @if($alert->type == 'Flood') text-blue-500 
                                @elseif($alert->type == 'Fire') text-red-500 
                                @elseif($alert->type == 'Earthquake') text-yellow-500 
                                @else text-gray-500 @endif 
                                text-xl">
                                @if($alert->type == 'Flood') 💧
                                @elseif($alert->type == 'Fire') 🔥
                                @elseif($alert->type == 'Earthquake') ⚡
                                @else ℹ️ @endif
                            </span>
                            <span class="font-semibold">{{ $alert->message }}</span>
                            <small class="ml-auto text-gray-500">{{ $alert->time_ago }}</small>
                        </li>
                    @endforeach
                </ul>
            @else
                <p class="text-gray-500 italic">No recent alerts</p>
            @endif
        </div>
    </div>
    
    <div class="flex justify-between items-center mb-8">
        <h1 class="text-3xl font-bold text-gray-800">📢 Notification History</h1>
        <a href="{{ route('components.notification.create') }}" class="bg-blue-600 hover:bg-blue-700 text-white px-5 py-2 rounded-lg shadow-md transition-all">
            + Create Notification
        </a>
    </div>

    @if($notifications->isEmpty())
        <div class="bg-yellow-50 border-l-4 border-yellow-500 text-yellow-800 p-4 rounded">
            <p class="font-semibold">No notifications found.</p>
        </div>
    @else
        <div class="overflow-x-auto shadow-md rounded-lg border border-gray-200">
            <table class="min-w-full bg-white">
                <thead class="bg-gray-100 text-gray-700 text-sm uppercase">
                    <tr>
                        <th class="px-6 py-4 text-left">Title</th>
                        <th class="px-6 py-4 text-left">Category</th>
                        <th class="px-6 py-4 text-left">Message</th>
                        <th class="px-6 py-4 text-left">Severity</th>
                        <th class="px-6 py-4 text-left">Created</th>
                        <th class="px-6 py-4 text-left">Status</th>
                        <th class="px-6 py-4 text-center">Actions</th>
                    </tr>
                </thead>
                <tbody class="divide-y divide-gray-200">
                    @foreach($notifications as $notification)
                    <tr class="hover:bg-blue-50 transition-colors duration-150">
                        <td class="px-6 py-4 font-medium text-gray-900">{{ $notification->title }}</td>
                        <td class="px-6 py-4 text-gray-700">{{ $notification->category }}</td>
                        <td class="px-6 py-4 text-gray-600 truncate max-w-xs">{{ Str::limit($notification->message, 50) }}</td>
                        <td class="px-6 py-4">
                            @php
                                $severityColors = [
                                    'Low' => 'bg-green-100 text-green-800',
                                    'Medium' => 'bg-yellow-100 text-yellow-800',
                                    'High' => 'bg-red-100 text-red-800',
                                    'Critical' => 'bg-pink-100 text-pink-800',
                                ];
                                $severity = ucfirst($notification->severity);
                                $badgeClass = $severityColors[$severity] ?? 'bg-gray-100 text-gray-800';
                            @endphp
                            <span class="px-3 py-1 rounded-full text-xs font-semibold {{ $badgeClass }}">
                                {{ $severity }}
                            </span>
                        </td>
                        <td class="px-6 py-4 text-gray-700">{{ $notification->created_at->format('F j, Y g:i A') }}</td>
                        <td class="px-6 py-4">
                            <span class="px-2 py-1 rounded-full text-xs font-semibold {{ $notification->sent ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800' }}">
                                {{ $notification->sent ? 'Sent' : 'Pending' }}
                            </span>
                        </td>
                        <td class="px-6 py-4 text-center">
                            <div class="flex space-x-2 justify-center">
                                <button
                                    class="text-blue-600 hover:text-blue-800 font-medium underline"
                                    onclick="openModal({{ $notification->id }}, '{{ addslashes($notification->title) }}', '{{ addslashes($notification->category) }}', '{{ addslashes($notification->message) }}', '{{ $notification->sent ? 'Sent' : 'Pending' }}')">
                                    View
                                </button>
                            </div>
                        </td>
                    </tr>
                    @endforeach
                </tbody>
            </table>
              <div class="p-4 bg-gray-50 border-t border-gray-200">
                {{ $notifications->links('pagination::tailwind') }}
             </div>
        </div>
    @endif
</div>

<!-- Modal -->
<div id="notificationModal" class="fixed inset-0 z-50 bg-black bg-opacity-40 flex items-center justify-center hidden">
    <div class="bg-white rounded-xl w-full max-w-lg p-6 shadow-xl transform transition-all scale-95">
        <h2 id="modalTitle" class="text-2xl font-bold mb-4 text-gray-800"></h2>
        <p class="mb-2"><strong>📂 Category:</strong> <span id="modalCategory" class="text-gray-700"></span></p>
        <p class="mb-4"><strong>📝 Message:</strong> <span id="modalMessage" class="text-gray-700 block mt-1"></span></p>
        <p class="mb-6"><strong>📬 Status:</strong> <span id="modalStatus" class="text-gray-700"></span></p>
        <div class="text-right">
            <button onclick="closeModal()" class="bg-gray-500 hover:bg-gray-600 text-white px-5 py-2 rounded-md transition">
                Close
            </button>
        </div>
    </div>
</div>

<script>
    function openModal(id, title, category, message, status) {
        document.getElementById('modalTitle').textContent = title;
        document.getElementById('modalCategory').textContent = category;
        document.getElementById('modalMessage').textContent = message;
        document.getElementById('modalStatus').textContent = status;
        document.getElementById('notificationModal').classList.remove('hidden');
    }

    function closeModal() {
        document.getElementById('notificationModal').classList.add('hidden');
    }
</script>
<script>
    window.monthlyCategoryCounts = {!! $monthlyCategoryCounts->toJson() !!};
</script>
<script src="{{ asset('js/notification.js') }}"></script>
@endsection
