import{a as K}from"./chunk-FNGMIKCE.js";import{Aa as q,B as n,Ba as F,C as i,D as M,Ea as L,F as C,G as D,I as a,J as S,La as U,M as d,N as g,Na as B,O as p,Q as y,R as w,Ta as j,U as P,Ua as V,Xa as R,Z as O,aa as E,ba as I,bb as G,ca as v,cb as H,da as T,ea as W,fa as N,ga as k,gb as J,j as h,l as f,pb as Y,u as m,v as c,w as _,y as b,ya as z,z as x,za as A}from"./chunk-7LSN6DH6.js";import"./chunk-EFGZ4QZO.js";import"./chunk-VKNYRYKJ.js";import"./chunk-YLGV66M3.js";import"./chunk-VD4VFVTQ.js";import"./chunk-HC6MZPB3.js";import"./chunk-IP3IRUG3.js";import"./chunk-UKIOCGZG.js";import"./chunk-RMJ7PCZJ.js";import"./chunk-K54AU7WQ.js";import"./chunk-VBU7NMPV.js";import"./chunk-JIZJ6CAM.js";import"./chunk-MCRJI3T3.js";import"./chunk-YNIR5NDL.js";import"./chunk-NO26UXQI.js";import"./chunk-SELJIRKY.js";import"./chunk-ODNE7IRY.js";import"./chunk-F4H6ZFEG.js";import"./chunk-JWIEPCRG.js";import"./chunk-7WD7LEC6.js";import"./chunk-WTCPO44B.js";import"./chunk-SV7S5NYR.js";import"./chunk-L5T6STQ3.js";import"./chunk-3EJRMEWO.js";import"./chunk-GNOVVPTF.js";import"./chunk-BAKMWPBW.js";import"./chunk-5OMUW5VI.js";import"./chunk-OBXDPQ3V.js";import"./chunk-LNJ3S2LQ.js";var Q=(()=>{let o=class o{constructor(s){this.http=s,this.apiUrl=`${K.apiUrl}/mobile-users`}saveUserData(s){return this.http.post(this.apiUrl,s)}createUser(s){return this.http.post(this.apiUrl,s)}};o.\u0275fac=function(r){return new(r||o)(f(P))},o.\u0275prov=h({token:o,factory:o.\u0275fac,providedIn:"root"});let l=o;return l})();function ee(l,o){if(l&1&&(n(0,"ion-text",21)(1,"p"),a(2),i()()),l&2){let X=D();m(2),S(X.errorMessage)}}var de=(()=>{let o=class o{constructor(s,r){this.router=s,this.mobileUserService=r,this.userData={full_name:"",mobile_number:"",age:"",gender:"",address:""},this.acceptedTerms=!1,this.showError=!1,this.errorMessage=""}onSave(){console.log(this.userData);let s=["full_name","mobile_number","age","gender","address"];for(let r of s){let e=this.userData[r];if(e==null||e.toString().trim()===""){this.showError=!0,this.errorMessage="All fields are required.";return}}if(!/^[0-9]{11}$/.test(this.userData.mobile_number)){this.showError=!0,this.errorMessage="Mobile number must be 11 digits.";return}if(isNaN(Number(this.userData.age))||Number(this.userData.age)<=0){this.showError=!0,this.errorMessage="Age must be a positive number.";return}if(!this.acceptedTerms){this.showError=!0,this.errorMessage="You must accept the Terms and Conditions.";return}this.mobileUserService.createUser({full_name:this.userData.full_name,mobile_number:this.userData.mobile_number,age:Number(this.userData.age),gender:this.userData.gender,address:this.userData.address}).subscribe({next:()=>{let r={full_name:this.userData.full_name,mobile_number:this.userData.mobile_number,age:this.userData.age,gender:this.userData.gender,address:this.userData.address};localStorage.setItem("userData",JSON.stringify(r)),localStorage.setItem("onboardingComplete","true"),this.router.navigate(["/tabs/home"])},error:r=>{var e;this.showError=!0,this.errorMessage=((e=r.error)==null?void 0:e.message)||"Failed to save user."}})}};o.\u0275fac=function(r){return new(r||o)(c(O),c(Q))},o.\u0275cmp=_({type:o,selectors:[["app-data"]],decls:42,vars:7,consts:[[1,"ion-padding"],[1,"profile-container"],[1,"header-logo"],[1,"home-title"],["src","assets/ALERTO.png","alt","App Logo",1,"home-logo"],[3,"ngSubmit"],["position","floating",2,"font-size","15px"],["type","text","name","full_name","required","",2,"font-size","20px","padding-bottom","10px",3,"ngModelChange","ngModel"],["type","text","name","mobile_number","required","",2,"font-size","20px","padding-bottom","10px",3,"ngModelChange","ngModel"],["type","number","name","age","required","",2,"font-size","20px","padding-bottom","10px",3,"ngModelChange","ngModel"],["position","floating"],["name","gender","required","",3,"ngModelChange","ngModel"],["value","Male"],["value","Female"],["value","Other"],["type","text","name","address","required","",2,"font-size","20px","padding-bottom","10px",3,"ngModelChange","ngModel"],["color","danger","class","error-message",4,"ngIf"],[1,"terms-checkbox"],["slot","start","name","acceptedTerms","required","",3,"ngModelChange","ngModel"],["href","#"],["expand","block","type","submit",1,"ion-margin-top"],["color","danger",1,"error-message"]],template:function(r,e){r&1&&(n(0,"ion-content",0)(1,"div",1)(2,"div",2)(3,"div",3),M(4,"img",4),a(5," Hi, Welcome to Safe Area!"),i()(),n(6,"form",5),C("ngSubmit",function(){return e.onSave()}),n(7,"ion-item")(8,"ion-label",6),a(9,"Full Name:"),i(),n(10,"ion-input",7),p("ngModelChange",function(t){return g(e.userData.full_name,t)||(e.userData.full_name=t),t}),i()(),n(11,"ion-item")(12,"ion-label",6),a(13,"Mobile Number:"),i(),n(14,"ion-input",8),p("ngModelChange",function(t){return g(e.userData.mobile_number,t)||(e.userData.mobile_number=t),t}),i()(),n(15,"ion-item")(16,"ion-label",6),a(17,"Age:"),i(),n(18,"ion-input",9),p("ngModelChange",function(t){return g(e.userData.age,t)||(e.userData.age=t),t}),i()(),n(19,"ion-item")(20,"ion-label",10),a(21,"Gender "),n(22,"ion-select",11),p("ngModelChange",function(t){return g(e.userData.gender,t)||(e.userData.gender=t),t}),n(23,"ion-select-option",12),a(24,"Male"),i(),n(25,"ion-select-option",13),a(26,"Female"),i(),n(27,"ion-select-option",14),a(28,"Other"),i()()()(),n(29,"ion-item")(30,"ion-label",6),a(31,"Address:"),i(),n(32,"ion-input",15),p("ngModelChange",function(t){return g(e.userData.address,t)||(e.userData.address=t),t}),i()(),b(33,ee,3,1,"ion-text",16),n(34,"div",17)(35,"ion-checkbox",18),p("ngModelChange",function(t){return g(e.acceptedTerms,t)||(e.acceptedTerms=t),t}),i(),n(36,"label"),a(37,"I accept the "),n(38,"a",19),a(39,"Terms and Conditions"),i()()(),n(40,"ion-button",20),a(41," Save "),i()()()()),r&2&&(m(10),d("ngModel",e.userData.full_name),m(4),d("ngModel",e.userData.mobile_number),m(4),d("ngModel",e.userData.age),m(4),d("ngModel",e.userData.gender),m(10),d("ngModel",e.userData.address),m(),x("ngIf",e.showError),m(2),d("ngModel",e.acceptedTerms))},dependencies:[Y,L,U,B,j,V,R,G,H,J,z,A,q,F,w,y,k,W,E,I,N,T,v],styles:[".profile-container[_ngcontent-%COMP%]{height:100%;display:flex;flex-direction:column;align-items:center;justify-content:center;max-width:420px;margin:0 auto;padding:32px 20px}form[_ngcontent-%COMP%]{width:100%}ion-item[_ngcontent-%COMP%]{--background: #f9f9f9;--border-radius: 25px;--padding-start: 10px;--padding-end: 10px;--inner-padding-top: 5px;--inner-padding-bottom: 5px;margin-bottom:10px;box-shadow:0 1px 4px #0000000d;height:65px}ion-label[_ngcontent-%COMP%]{font-size:5px;color:#333}ion-input[_ngcontent-%COMP%], ion-select[_ngcontent-%COMP%]{font-size:15px}.terms-checkbox[_ngcontent-%COMP%]{display:flex;align-items:center;margin-top:12px;font-size:14px;color:#444;flex-wrap:wrap;line-height:1.4}.terms-checkbox[_ngcontent-%COMP%]   ion-checkbox[_ngcontent-%COMP%]{margin-right:8px}.terms-checkbox[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]{color:#1565c0;text-decoration:underline;font-weight:500;margin-left:4px}ion-button[_ngcontent-%COMP%]{margin-top:24px;--background: #1565c0;--border-radius: 10px;--box-shadow: 0px 4px 10px rgba(0, 0, 0, .15)}.error-message[_ngcontent-%COMP%]{text-align:center;margin:10px 0;color:#e53935}.error-message[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:0;font-size:14px}.header-logo[_ngcontent-%COMP%]{text-align:center}.header-logo[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:80px;height:auto}.header-logo[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{font-size:18px;color:#222;font-weight:600;text-shadow:0px 2px 4px rgba(0,0,0,.15)}.home-title[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center;font-size:15px;font-weight:700;letter-spacing:1px;text-shadow:1px 2px 4px #ccc;padding-top:59px}"]});let l=o;return l})();export{de as DataPage};
