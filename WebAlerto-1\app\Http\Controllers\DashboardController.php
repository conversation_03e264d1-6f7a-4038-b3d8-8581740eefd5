<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Evacuation;
use App\Models\User;

class DashboardController extends Controller
{
    public function index()
    {
        // Fetch count of active evacuation centers from database
        $activeCenters = Evacuation::where('status', 'active')->count();
        $centers = Evacuation::all();

        // Get actual notification count from database
        $totalAlerts = \App\Models\Notification::count();
        $barangayCount = 50; // You might want to replace this with actual data
        
        // Get recent notifications for the dashboard
        $recentAlerts = \App\Models\Notification::orderBy('created_at', 'desc')
                        ->take(3)
                        ->get()
                        ->map(function($notification) {
                            return (object) [
                                'type' => $notification->category,
                                'message' => $notification->title,
                                'location' => 'All Users',
                                'time_ago' => $notification->created_at->diffForHumans()
                            ];
                        });

        return view('components.dashboard', compact(
            'totalAlerts',
            'activeCenters',
            'barangayCount',    
            'recentAlerts',
            'centers' 
        ));
    }
}



