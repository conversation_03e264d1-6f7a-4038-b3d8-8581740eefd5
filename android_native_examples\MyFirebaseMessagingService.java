package com.yourpackage.webalerto;

import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.PendingIntent;
import android.content.Context;
import android.content.Intent;
import android.os.Build;
import android.util.Log;

import androidx.core.app.NotificationCompat;
import androidx.core.app.NotificationManagerCompat;

import com.google.firebase.messaging.FirebaseMessagingService;
import com.google.firebase.messaging.RemoteMessage;

import org.json.JSONException;
import org.json.JSONObject;

import java.io.IOException;

import okhttp3.Call;
import okhttp3.Callback;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;

public class MyFirebaseMessagingService extends FirebaseMessagingService {
    private static final String TAG = "FCMService";
    private static final String CHANNEL_ID = "disaster_alerts";
    private NotificationHelper notificationHelper;
    private ApiService apiService;

    @Override
    public void onCreate() {
        super.onCreate();
        notificationHelper = new NotificationHelper(this);
        apiService = new ApiService();
    }

    @Override
    public void onMessageReceived(RemoteMessage remoteMessage) {
        Log.d(TAG, "From: " + remoteMessage.getFrom());

        // Check if message contains a data payload
        if (remoteMessage.getData().size() > 0) {
            Log.d(TAG, "Message data payload: " + remoteMessage.getData());
            handleDataMessage(remoteMessage.getData());
        }

        // Check if message contains a notification payload
        if (remoteMessage.getNotification() != null) {
            Log.d(TAG, "Message Notification Body: " + remoteMessage.getNotification().getBody());
            
            String title = remoteMessage.getNotification().getTitle();
            String body = remoteMessage.getNotification().getBody();
            String severity = remoteMessage.getData().get("severity");
            String category = remoteMessage.getData().get("category");
            
            showNotification(title, body, severity, category);
        }
    }

    @Override
    public void onNewToken(String token) {
        Log.d(TAG, "Refreshed token: " + token);
        
        // Send token to your Laravel backend
        sendTokenToServer(token);
    }

    private void handleDataMessage(java.util.Map<String, String> data) {
        String title = data.get("title");
        String message = data.get("message");
        String severity = data.get("severity");
        String category = data.get("category");
        
        if (title != null && message != null) {
            showNotification(title, message, severity, category);
        }
    }

    private void showNotification(String title, String message, String severity, String category) {
        Intent intent = new Intent(this, MainActivity.class);
        intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP);
        
        // Add extra data to intent
        intent.putExtra("title", title);
        intent.putExtra("message", message);
        intent.putExtra("severity", severity);
        intent.putExtra("category", category);
        
        PendingIntent pendingIntent = PendingIntent.getActivity(this, 0, intent, 
            PendingIntent.FLAG_ONE_SHOT | PendingIntent.FLAG_IMMUTABLE);

        // Get notification icon based on category
        int iconResource = getNotificationIcon(category);
        
        // Get notification color based on severity
        int notificationColor = getNotificationColor(severity);

        NotificationCompat.Builder notificationBuilder =
            new NotificationCompat.Builder(this, CHANNEL_ID)
                .setSmallIcon(iconResource)
                .setContentTitle(title)
                .setContentText(message)
                .setAutoCancel(true)
                .setSound(android.provider.Settings.System.DEFAULT_NOTIFICATION_URI)
                .setContentIntent(pendingIntent)
                .setColor(notificationColor)
                .setPriority(getSeverityPriority(severity))
                .setCategory(NotificationCompat.CATEGORY_ALARM);

        // Add action buttons for high severity alerts
        if ("high".equals(severity)) {
            Intent actionIntent = new Intent(this, EmergencyActionActivity.class);
            PendingIntent actionPendingIntent = PendingIntent.getActivity(this, 1, actionIntent, 
                PendingIntent.FLAG_IMMUTABLE);
            
            notificationBuilder.addAction(R.drawable.ic_emergency, "Emergency Info", actionPendingIntent);
        }

        NotificationManager notificationManager =
            (NotificationManager) getSystemService(Context.NOTIFICATION_SERVICE);

        // Create notification channel for Android O and above
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            NotificationChannel channel = new NotificationChannel(
                CHANNEL_ID,
                "Disaster Alerts",
                NotificationManager.IMPORTANCE_HIGH
            );
            channel.setDescription("Emergency disaster notifications");
            channel.enableLights(true);
            channel.setLightColor(notificationColor);
            channel.enableVibration(true);
            notificationManager.createNotificationChannel(channel);
        }

        notificationManager.notify(0, notificationBuilder.build());
    }

    private int getNotificationIcon(String category) {
        if (category == null) return R.drawable.ic_alert;
        
        switch (category.toLowerCase()) {
            case "earthquake":
                return R.drawable.ic_earthquake;
            case "typhoon":
                return R.drawable.ic_typhoon;
            case "flood":
                return R.drawable.ic_flood;
            case "fire":
                return R.drawable.ic_fire;
            default:
                return R.drawable.ic_alert;
        }
    }

    private int getNotificationColor(String severity) {
        if (severity == null) return getResources().getColor(R.color.default_notification);
        
        switch (severity.toLowerCase()) {
            case "high":
                return getResources().getColor(R.color.high_severity);
            case "medium":
                return getResources().getColor(R.color.medium_severity);
            case "low":
                return getResources().getColor(R.color.low_severity);
            default:
                return getResources().getColor(R.color.default_notification);
        }
    }

    private int getSeverityPriority(String severity) {
        if (severity == null) return NotificationCompat.PRIORITY_DEFAULT;
        
        switch (severity.toLowerCase()) {
            case "high":
                return NotificationCompat.PRIORITY_HIGH;
            case "medium":
                return NotificationCompat.PRIORITY_DEFAULT;
            case "low":
                return NotificationCompat.PRIORITY_LOW;
            default:
                return NotificationCompat.PRIORITY_DEFAULT;
        }
    }

    private void sendTokenToServer(String token) {
        apiService.registerDeviceToken(token, new ApiService.ApiCallback() {
            @Override
            public void onSuccess(String response) {
                Log.d(TAG, "Token registered successfully: " + response);
            }

            @Override
            public void onError(String error) {
                Log.e(TAG, "Failed to register token: " + error);
            }
        });
    }
}
