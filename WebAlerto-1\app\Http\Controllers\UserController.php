<?php

namespace App\Http\Controllers;

use App\Models\User;
use Illuminate\Http\Request;

class UserController extends Controller
{
    public function store(Request $request)
    {
        // Validate the input data
        $request->validate([
            'full_name' => 'required|string|max:255',
            'position' => 'required|string|max:255',
            'barangay' => 'required|string|max:255',
            'email' => 'required|email|unique:users,email',
            'password' => 'required|confirmed|min:8', // password confirmation rule
        ]);

        // Create a new user in the database
        $user = User::create([
            'full_name' => $request->input('full_name'),
            'position' => $request->input('position'),
            'barangay' => $request->input('barangay'),
            'email' => $request->input('email'),
            'password' => Hash::make($request->input('password')), // hash the password
        ]);

        // Redirect or return a response (success message, etc.)
        return redirect()->route('register')->with('success', 'User successfully registered!');
    }
    public function index(Request $request)
    {
        $query = User::query();

        // Search by name (first letter matching)
        if ($request->has('search') && $request->search != '') {
            $search = strtoupper($request->search);
            $query->where('full_name', 'like', $search . '%');
        }

        $users = $query->get(); // Retrieve the filtered users

        return view('components.user_management.user-management', compact('users'));
    }

    

    

    public function deactivate($id)
    {
        $user = User::findOrFail($id);

        // Only Chairman can deactivate
        if (auth()->user()->position !== 'Chairman') {
            return redirect()->back()->with('error', 'You are not authorized to deactivate users.');
        }

        $user->status = 'Inactive';
        $user->save();

        return redirect()->route('components.user-management')->with('success', 'User deactivated successfully.');
    }


    public function search(Request $request)
    {
        $query = strtoupper($request->input('query'));

        // Query users whose full name starts with the query
        $users = User::where('full_name', 'like', $query . '%')->get();

        return response()->json(['users' => $users]);
    }
}
