<?php

namespace Tests\Unit\Controllers;

use Tests\TestCase;
use App\Http\Controllers\FCMController;
use App\Services\FCMService;
use App\Http\Requests\FCMRequest;
use App\Http\Requests\BulkFCMRequest;
use App\Http\Responses\FCMResponse;
use Mockery;

class FCMControllerTest extends TestCase
{
    protected $service;
    protected $controller;

    protected function setUp(): void
    {
        parent::setUp();
        $this->service = Mockery::mock(FCMService::class);
        $this->controller = new FCMController($this->service);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    public function test_send_notification()
    {
        $request = Mockery::mock(FCMRequest::class);
        $request->shouldReceive('validated')->andReturn([
            'token' => 'test_token',
            'title' => 'Test Notification',
            'message' => 'This is a test notification',
            'severity' => 'normal',
            'category' => 'test'
        ]);

        $this->service->shouldReceive('send')
            ->once()
            ->with('test_token', [
                'title' => 'Test Notification',
                'message' => 'This is a test notification',
                'severity' => 'normal',
                'category' => 'test'
            ])
            ->andReturn('message_id');

        $response = $this->controller->send($request);

        $this->assertInstanceOf(FCMResponse::class, $response);
        $this->assertEquals(200, $response->getStatusCode());
        $this->assertEquals([
            'success' => true,
            'message' => 'Notification sent successfully',
            'data' => ['message_id' => 'message_id']
        ], json_decode($response->getContent(), true));
    }

    public function test_send_notification_with_platform_configs()
    {
        $request = Mockery::mock(FCMRequest::class);
        $request->shouldReceive('validated')->andReturn([
            'token' => 'test_token',
            'title' => 'Test Notification',
            'message' => 'This is a test notification',
            'severity' => 'normal',
            'category' => 'test',
            'android' => [
                'priority' => 'high'
            ],
            'apns' => [
                'headers' => [
                    'apns-priority' => '10'
                ]
            ],
            'webpush' => [
                'headers' => [
                    'Urgency' => 'high'
                ]
            ]
        ]);

        $this->service->shouldReceive('send')
            ->once()
            ->with('test_token', [
                'title' => 'Test Notification',
                'message' => 'This is a test notification',
                'severity' => 'normal',
                'category' => 'test',
                'android' => [
                    'priority' => 'high'
                ],
                'apns' => [
                    'headers' => [
                        'apns-priority' => '10'
                    ]
                ],
                'webpush' => [
                    'headers' => [
                        'Urgency' => 'high'
                    ]
                ]
            ])
            ->andReturn('message_id');

        $response = $this->controller->send($request);

        $this->assertInstanceOf(FCMResponse::class, $response);
        $this->assertEquals(200, $response->getStatusCode());
        $this->assertEquals([
            'success' => true,
            'message' => 'Notification sent successfully',
            'data' => ['message_id' => 'message_id']
        ], json_decode($response->getContent(), true));
    }

    public function test_send_bulk_notifications()
    {
        $request = Mockery::mock(BulkFCMRequest::class);
        $request->shouldReceive('validated')->andReturn([
            'notifications' => [
                [
                    'token' => 'token1',
                    'title' => 'Test Notification 1',
                    'message' => 'This is test notification 1',
                    'severity' => 'normal',
                    'category' => 'test'
                ],
                [
                    'token' => 'token2',
                    'title' => 'Test Notification 2',
                    'message' => 'This is test notification 2',
                    'severity' => 'high',
                    'category' => 'test'
                ]
            ]
        ]);

        $this->service->shouldReceive('sendMulticast')
            ->once()
            ->with(
                ['token1', 'token2'],
                [
                    'title' => 'Test Notification 1',
                    'message' => 'This is test notification 1',
                    'severity' => 'normal',
                    'category' => 'test'
                ],
                true
            )
            ->andReturn([
                'success_count' => 2,
                'failure_count' => 0,
                'results' => [
                    ['status' => 'sent'],
                    ['status' => 'sent']
                ]
            ]);

        $response = $this->controller->sendBulk($request);

        $this->assertInstanceOf(FCMResponse::class, $response);
        $this->assertEquals(200, $response->getStatusCode());
        $this->assertEquals([
            'success' => true,
            'data' => [
                'success_count' => 2,
                'failure_count' => 0,
                'results' => [
                    ['status' => 'sent'],
                    ['status' => 'sent']
                ]
            ]
        ], json_decode($response->getContent(), true));
    }

    public function test_send_bulk_notifications_with_partial_success()
    {
        $request = Mockery::mock(BulkFCMRequest::class);
        $request->shouldReceive('validated')->andReturn([
            'notifications' => [
                [
                    'token' => 'token1',
                    'title' => 'Test Notification 1',
                    'message' => 'This is test notification 1',
                    'severity' => 'normal',
                    'category' => 'test'
                ],
                [
                    'token' => 'token2',
                    'title' => 'Test Notification 2',
                    'message' => 'This is test notification 2',
                    'severity' => 'high',
                    'category' => 'test'
                ]
            ]
        ]);

        $this->service->shouldReceive('sendMulticast')
            ->once()
            ->with(
                ['token1', 'token2'],
                [
                    'title' => 'Test Notification 1',
                    'message' => 'This is test notification 1',
                    'severity' => 'normal',
                    'category' => 'test'
                ],
                true
            )
            ->andReturn([
                'success_count' => 1,
                'failure_count' => 1,
                'results' => [
                    ['status' => 'sent'],
                    ['status' => 'failed', 'error' => 'Invalid token']
                ]
            ]);

        $response = $this->controller->sendBulk($request);

        $this->assertInstanceOf(FCMResponse::class, $response);
        $this->assertEquals(400, $response->getStatusCode());
        $this->assertEquals([
            'success' => false,
            'data' => [
                'success_count' => 1,
                'failure_count' => 1,
                'results' => [
                    ['status' => 'sent'],
                    ['status' => 'failed', 'error' => 'Invalid token']
                ]
            ]
        ], json_decode($response->getContent(), true));
    }

    public function test_send_bulk_notifications_with_all_failures()
    {
        $request = Mockery::mock(BulkFCMRequest::class);
        $request->shouldReceive('validated')->andReturn([
            'notifications' => [
                [
                    'token' => 'token1',
                    'title' => 'Test Notification 1',
                    'message' => 'This is test notification 1',
                    'severity' => 'normal',
                    'category' => 'test'
                ],
                [
                    'token' => 'token2',
                    'title' => 'Test Notification 2',
                    'message' => 'This is test notification 2',
                    'severity' => 'high',
                    'category' => 'test'
                ]
            ]
        ]);

        $this->service->shouldReceive('sendMulticast')
            ->once()
            ->with(
                ['token1', 'token2'],
                [
                    'title' => 'Test Notification 1',
                    'message' => 'This is test notification 1',
                    'severity' => 'normal',
                    'category' => 'test'
                ],
                true
            )
            ->andReturn([
                'success_count' => 0,
                'failure_count' => 2,
                'results' => [
                    ['status' => 'failed', 'error' => 'Invalid token'],
                    ['status' => 'failed', 'error' => 'Invalid token']
                ]
            ]);

        $response = $this->controller->sendBulk($request);

        $this->assertInstanceOf(FCMResponse::class, $response);
        $this->assertEquals(400, $response->getStatusCode());
        $this->assertEquals([
            'success' => false,
            'data' => [
                'success_count' => 0,
                'failure_count' => 2,
                'results' => [
                    ['status' => 'failed', 'error' => 'Invalid token'],
                    ['status' => 'failed', 'error' => 'Invalid token']
                ]
            ]
        ], json_decode($response->getContent(), true));
    }
} 