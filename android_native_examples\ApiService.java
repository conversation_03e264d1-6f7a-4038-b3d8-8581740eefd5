package com.yourpackage.webalerto;

import android.util.Log;

import org.json.JSONException;
import org.json.JSONObject;

import java.io.IOException;
import java.util.concurrent.TimeUnit;

import okhttp3.Call;
import okhttp3.Callback;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;

public class ApiService {
    private static final String TAG = "ApiService";
    private static final String BASE_URL = "http://*************:8000/api/"; // Update with your IP
    private static final MediaType JSON = MediaType.get("application/json; charset=utf-8");
    
    private OkHttpClient client;

    public ApiService() {
        client = new OkHttpClient.Builder()
            .connectTimeout(30, TimeUnit.SECONDS)
            .readTimeout(30, TimeUnit.SECONDS)
            .writeTimeout(30, TimeUnit.SECONDS)
            .build();
    }

    public interface ApiCallback {
        void onSuccess(String response);
        void onError(String error);
    }

    public void registerDeviceToken(String token, ApiCallback callback) {
        JSONObject json = new JSONObject();
        try {
            json.put("token", token);
            json.put("device_type", "android");
        } catch (JSONException e) {
            Log.e(TAG, "Error creating JSON for token registration", e);
            callback.onError("JSON creation error: " + e.getMessage());
            return;
        }

        RequestBody body = RequestBody.create(json.toString(), JSON);
        Request request = new Request.Builder()
            .url(BASE_URL + "device-token")
            .post(body)
            .addHeader("Content-Type", "application/json")
            .addHeader("Accept", "application/json")
            .build();

        client.newCall(request).enqueue(new Callback() {
            @Override
            public void onResponse(Call call, Response response) throws IOException {
                String responseBody = response.body().string();
                if (response.isSuccessful()) {
                    Log.d(TAG, "Token registration successful: " + responseBody);
                    callback.onSuccess(responseBody);
                } else {
                    Log.e(TAG, "Token registration failed: " + response.code() + " - " + responseBody);
                    callback.onError("HTTP " + response.code() + ": " + responseBody);
                }
            }

            @Override
            public void onFailure(Call call, IOException e) {
                Log.e(TAG, "Token registration network error", e);
                callback.onError("Network error: " + e.getMessage());
            }
        });
    }

    public void deactivateDeviceToken(String token, ApiCallback callback) {
        JSONObject json = new JSONObject();
        try {
            json.put("token", token);
        } catch (JSONException e) {
            Log.e(TAG, "Error creating JSON for token deactivation", e);
            callback.onError("JSON creation error: " + e.getMessage());
            return;
        }

        RequestBody body = RequestBody.create(json.toString(), JSON);
        Request request = new Request.Builder()
            .url(BASE_URL + "device-token/deactivate")
            .post(body)
            .addHeader("Content-Type", "application/json")
            .addHeader("Accept", "application/json")
            .build();

        client.newCall(request).enqueue(new Callback() {
            @Override
            public void onResponse(Call call, Response response) throws IOException {
                String responseBody = response.body().string();
                if (response.isSuccessful()) {
                    Log.d(TAG, "Token deactivation successful: " + responseBody);
                    callback.onSuccess(responseBody);
                } else {
                    Log.e(TAG, "Token deactivation failed: " + response.code() + " - " + responseBody);
                    callback.onError("HTTP " + response.code() + ": " + responseBody);
                }
            }

            @Override
            public void onFailure(Call call, IOException e) {
                Log.e(TAG, "Token deactivation network error", e);
                callback.onError("Network error: " + e.getMessage());
            }
        });
    }

    public void getEvacuationCenters(ApiCallback callback) {
        Request request = new Request.Builder()
            .url(BASE_URL + "evacuation-centers")
            .get()
            .addHeader("Accept", "application/json")
            .build();

        client.newCall(request).enqueue(new Callback() {
            @Override
            public void onResponse(Call call, Response response) throws IOException {
                String responseBody = response.body().string();
                if (response.isSuccessful()) {
                    Log.d(TAG, "Evacuation centers retrieved successfully");
                    callback.onSuccess(responseBody);
                } else {
                    Log.e(TAG, "Failed to get evacuation centers: " + response.code() + " - " + responseBody);
                    callback.onError("HTTP " + response.code() + ": " + responseBody);
                }
            }

            @Override
            public void onFailure(Call call, IOException e) {
                Log.e(TAG, "Evacuation centers network error", e);
                callback.onError("Network error: " + e.getMessage());
            }
        });
    }

    public void sendTestNotification(String title, String message, String severity, String category, ApiCallback callback) {
        JSONObject json = new JSONObject();
        try {
            json.put("title", title);
            json.put("message", message);
            json.put("severity", severity);
            json.put("category", category);
        } catch (JSONException e) {
            Log.e(TAG, "Error creating JSON for test notification", e);
            callback.onError("JSON creation error: " + e.getMessage());
            return;
        }

        RequestBody body = RequestBody.create(json.toString(), JSON);
        Request request = new Request.Builder()
            .url(BASE_URL + "notifications/send")
            .post(body)
            .addHeader("Content-Type", "application/json")
            .addHeader("Accept", "application/json")
            .build();

        client.newCall(request).enqueue(new Callback() {
            @Override
            public void onResponse(Call call, Response response) throws IOException {
                String responseBody = response.body().string();
                if (response.isSuccessful()) {
                    Log.d(TAG, "Test notification sent successfully: " + responseBody);
                    callback.onSuccess(responseBody);
                } else {
                    Log.e(TAG, "Failed to send test notification: " + response.code() + " - " + responseBody);
                    callback.onError("HTTP " + response.code() + ": " + responseBody);
                }
            }

            @Override
            public void onFailure(Call call, IOException e) {
                Log.e(TAG, "Test notification network error", e);
                callback.onError("Network error: " + e.getMessage());
            }
        });
    }

    public void authenticateUser(String email, String password, ApiCallback callback) {
        JSONObject json = new JSONObject();
        try {
            json.put("email", email);
            json.put("password", password);
        } catch (JSONException e) {
            Log.e(TAG, "Error creating JSON for authentication", e);
            callback.onError("JSON creation error: " + e.getMessage());
            return;
        }

        RequestBody body = RequestBody.create(json.toString(), JSON);
        Request request = new Request.Builder()
            .url(BASE_URL + "auth/login")
            .post(body)
            .addHeader("Content-Type", "application/json")
            .addHeader("Accept", "application/json")
            .build();

        client.newCall(request).enqueue(new Callback() {
            @Override
            public void onResponse(Call call, Response response) throws IOException {
                String responseBody = response.body().string();
                if (response.isSuccessful()) {
                    Log.d(TAG, "Authentication successful");
                    callback.onSuccess(responseBody);
                } else {
                    Log.e(TAG, "Authentication failed: " + response.code() + " - " + responseBody);
                    callback.onError("HTTP " + response.code() + ": " + responseBody);
                }
            }

            @Override
            public void onFailure(Call call, IOException e) {
                Log.e(TAG, "Authentication network error", e);
                callback.onError("Network error: " + e.getMessage());
            }
        });
    }

    public void registerUser(String name, String email, String password, String phone, ApiCallback callback) {
        JSONObject json = new JSONObject();
        try {
            json.put("name", name);
            json.put("email", email);
            json.put("password", password);
            json.put("phone", phone);
        } catch (JSONException e) {
            Log.e(TAG, "Error creating JSON for user registration", e);
            callback.onError("JSON creation error: " + e.getMessage());
            return;
        }

        RequestBody body = RequestBody.create(json.toString(), JSON);
        Request request = new Request.Builder()
            .url(BASE_URL + "auth/signup")
            .post(body)
            .addHeader("Content-Type", "application/json")
            .addHeader("Accept", "application/json")
            .build();

        client.newCall(request).enqueue(new Callback() {
            @Override
            public void onResponse(Call call, Response response) throws IOException {
                String responseBody = response.body().string();
                if (response.isSuccessful()) {
                    Log.d(TAG, "User registration successful");
                    callback.onSuccess(responseBody);
                } else {
                    Log.e(TAG, "User registration failed: " + response.code() + " - " + responseBody);
                    callback.onError("HTTP " + response.code() + ": " + responseBody);
                }
            }

            @Override
            public void onFailure(Call call, IOException e) {
                Log.e(TAG, "User registration network error", e);
                callback.onError("Network error: " + e.getMessage());
            }
        });
    }
}
