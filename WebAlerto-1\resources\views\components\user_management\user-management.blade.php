@extends('layout.app')

@section('content')
<div class="container mx-auto p-6">
    <h2 class="text-2xl font-bold mb-4 text-center text-black-800">User Management</h2>

    <!-- Search Bar and Back Button -->
    <div class="flex justify-between items-center mb-4"> 
        <div class="flex items-center w-full">
            @if(request('search'))
                <a href="{{ route('components.user-management') }}" class="mr-4 text-blue-500 hover:text-blue-700">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                    </svg>
                </a>
            @endif
            <form method="GET" action="{{ route('components.user-management') }}" class="flex items-center w-full">
                <div class="relative w-full">
                    <input 
                        type="text" 
                        name="search" 
                        id="searchInput"
                        placeholder="Search Name" 
                        value="{{ request('search') }}" 
                        class="border border-gray-300 rounded-lg p-2 pl-10 w-full">
                    <!-- Search Icon -->
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 absolute left-3 top-3 text-gray-400" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M10 2a8 8 0 105.293 14.707l4.707 4.707a1 1 0 001.414-1.414l-4.707-4.707A8 8 0 0010 2zm0 2a6 6 0 110 12A6 6 0 0110 4z"/>
                    </svg>
                </div>
                <button type="submit" class="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 ml-2">
                    Search
                </button>
            </form>
        </div>
    </div>

    <!-- User Table -->
    <div class="overflow-x-auto">
        <table class="min-w-full bg-white border border-gray-200 rounded-lg">
            <thead>
                <tr class="bg-gray-200 text-gray-600 uppercase text-sm leading-normal">
                    <th class="py-3 px-6 text-left">ID no.</th>
                    <th class="py-3 px-6 text-left">Full Name</th>
                    <th class="py-3 px-6 text-left">Email</th>
                    <th class="py-3 px-6 text-left">Position</th>
                    <th class="py-3 px-6 text-left">Barangay</th>
                    <th class="py-3 px-6 text-left">Status</th>
                    <th class="py-3 px-6 text-center">Actions</th>
                </tr>
            </thead>
            <tbody class="text-gray-600 text-sm font-light" id="userTableBody">
                @forelse($users as $user)
                <tr class="border-b border-gray-200 hover:bg-gray-100">
                    <td class="py-3 px-6 text-left">{{ $user->id }}</td>
                    <td class="py-3 px-6 text-left">{{ $user->full_name }}</td>
                    <td class="py-3 px-6 text-left">{{ $user->email }}</td>
                    <td class="py-3 px-6 text-left">{{ $user->position }}</td>
                    <td class="py-3 px-6 text-left">{{ $user->barangay }}</td>
                    <td class="py-3 px-6 text-left">
    <span class="
        py-1 px-3 rounded-full text-xs font-semibold
        {{ strtolower($user->status) === 'active' 
            ? 'bg-green-200 text-green-600' 
            : 'bg-red-200 text-red-600' }}">
        {{ $user->status }}
    </span>
</td>

                    <td class="py-3 px-6 text-center">
                        <button onclick="showUserDetails({{ $user->id }}, '{{ $user->full_name }}', '{{ $user->email }}', '{{ $user->position }}', '{{ $user->barangay }}', '{{ $user->created_at }}')" class="bg-yellow-500 text-white px-4 py-2 rounded hover:bg-yellow-600">
                            View
                        </button>
                    </td>
                </tr>
                @empty
                <tr>
                    <td colspan="6" class="py-3 px-6 text-center text-red-500">No users found</td>
                </tr>
                @endforelse
            </tbody>
        </table>
    </div>
</div>

<!-- User Details Modal -->
<div id="userDetailsModal" class="fixed inset-0 bg-black bg-opacity-50 hidden flex items-center justify-center">
    <div class="bg-white p-6 rounded-lg shadow-lg w-96">
        <div class="flex justify-between items-center mb-4">
            <button onclick="closeUserDetailsModal()" class="text-gray-500 hover:text-gray-700">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>
        <div id="userDetailsContent">
            <!-- User details will be populated here -->
        </div>

        <!-- Only show Deactivate Form if Chairman -->
        @if (auth()->check() && auth()->user()->position == 'Chairman')
        <form id="deactivateForm" action="" method="POST">
            @csrf
            @method('PATCH')
            <button type="submit" class="bg-red-500 text-white px-4 py-2 rounded hover:bg-red-600 w-full mt-4">
                Deactivate User
            </button>
        </form>
        @endif
    </div>
</div>

<!-- Scripts -->
<script>
// Live Search (local, no reload)
document.addEventListener('DOMContentLoaded', function () {
    const searchInput = document.getElementById('searchInput');
    const tableRows = document.querySelectorAll('#userTableBody tr');

    searchInput.addEventListener('input', function () {
        const query = this.value.toLowerCase().trim();
        tableRows.forEach(row => {
            const fullNameCell = row.querySelector('td:nth-child(2)');
            const fullName = fullNameCell ? fullNameCell.textContent.toLowerCase() : '';
            if (fullName.startsWith(query)) {
                row.style.display = '';
            } else {
                row.style.display = 'none';
            }
        });
    });
});

// Show User Details
function showUserDetails(id, full_name, email, position, barangay, createdAt) {
    const modal = document.getElementById('userDetailsModal');
    const content = document.getElementById('userDetailsContent');
    const date = new Date(createdAt);
    const formattedDate = date.toLocaleString();

    content.innerHTML = `
        <div class="space-y-4">
            <h2 class="text-xl font-semibold text-gray-800 mb-4">User Details</h2>

            <div class="flex flex-col text-gray-800">
                <strong class="text-blue-600">Name:</strong>
                <div class="mt-1 bg-blue-50 border border-blue-300 rounded-lg p-2">${full_name}</div>
            </div>

            <div class="flex flex-col text-gray-800">
                <strong class="text-green-600">Email:</strong>
                <div class="mt-1 bg-green-50 border border-green-300 rounded-lg p-2">${email}</div>
            </div>

            <div class="flex flex-col text-gray-800">
                <strong class="text-yellow-600">Position:</strong>
                <div class="mt-1 bg-yellow-50 border border-yellow-300 rounded-lg p-2">${position}</div>
            </div>

            <div class="flex flex-col text-gray-800">
                <strong class="text-red-600">Barangay:</strong>
                <div class="mt-1 bg-red-50 border border-red-300 rounded-lg p-2">${barangay}</div>
            </div>

            <div class="flex flex-col text-gray-800">
                <strong class="text-purple-600">Created At:</strong>
                <div class="mt-1 bg-purple-50 border border-purple-300 rounded-lg p-2">${formattedDate}</div>
            </div>
        </div>
    `;

    
    // Set the deactivate form's action URL
    const deactivateForm = document.getElementById('deactivateForm');
    if (deactivateForm) {
        deactivateForm.action = `/users/${id}/deactivate`;
    }

    modal.classList.remove('hidden');
}

// Close Modal
function closeUserDetailsModal() {
    document.getElementById('userDetailsModal').classList.add('hidden');
}

// Close Modal if click outside
document.getElementById('userDetailsModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeUserDetailsModal();
    }
});
</script>

@endsection
