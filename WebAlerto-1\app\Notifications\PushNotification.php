<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use NotificationChannels\Fcm\FcmChannel;
use NotificationChannels\Fcm\FcmMessage;
use NotificationChannels\Fcm\Resources\AndroidConfig;
use NotificationChannels\Fcm\Resources\AndroidFcmOptions;
use NotificationChannels\Fcm\Resources\AndroidNotification;
use NotificationChannels\Fcm\Resources\ApnsConfig;
use NotificationChannels\Fcm\Resources\ApnsFcmOptions;
use NotificationChannels\Fcm\Resources\WebpushConfig;
use NotificationChannels\Fcm\Resources\WebpushFcmOptions;

class PushNotification extends Notification
{
    use Queueable;

    /**
     * The notification title.
     *
     * @var string
     */
    protected $title;

    /**
     * The notification message.
     *
     * @var string
     */
    protected $message;

    /**
     * The notification category.
     *
     * @var string
     */
    protected $category;

    /**
     * Create a new notification instance.
     */
    public function __construct(string $title, string $message, string $category, string $severity)
    {
        $this->title = $title;
        $this->message = $message;
        $this->category = $category;
        $this->severity = $severity;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return [FcmChannel::class];
    }

    /**
     * Get the FCM representation of the notification.
     *
     * @param mixed $notifiable
     * @return FcmMessage
     */
    public function toFcm($notifiable)
    {
        $categoryIcon = $this->getCategoryIcon();
        $tokens = $notifiable->routeNotificationForFcm($this);
        
        // Log notification attempt with more details
        \Illuminate\Support\Facades\Log::info('Sending FCM notification', [
            'user_id' => $notifiable->id,
            'title' => $this->title,
            'tokens_count' => count($tokens),
            'tokens' => $tokens // Log the actual tokens for debugging
        ]);
        
        if (empty($tokens)) {
            \Illuminate\Support\Facades\Log::warning('No FCM tokens found for user', [
                'user_id' => $notifiable->id
            ]);
            return null;
        }

        $severity = $this->mapSeverity($this->severity);
        
        return FcmMessage::create()
            ->setData([
                'title' => $this->title,
                'message' => $this->message,
                'category' => $this->category,
                'click_action' => 'FLUTTER_NOTIFICATION_CLICK',
                'id' => uniqid('', true),
            ])
            ->setNotification([
                'title' => $this->title,
                'body' => $this->message,
                'icon' => $categoryIcon,
            ])
            ->setAndroid(
                AndroidConfig::create()
                    ->setFcmOptions(AndroidFcmOptions::create()->setAnalyticsLabel('alerto_notification'))
                    ->setNotification(AndroidNotification::create()->setIcon($categoryIcon))
                    ->severity($severity)
            )
            ->setApns(
                ApnsConfig::create()
                    ->setFcmOptions(ApnsFcmOptions::create()->setAnalyticsLabel('alerto_notification'))
            )
            ->setWebpush(
                WebpushConfig::create()
                    ->setFcmOptions(WebpushFcmOptions::create()->setAnalyticsLabel('alerto_notification'))
            );
    }

    /**
     * Get the appropriate icon based on the notification category.
     *
     * @return string
     */
    protected function getCategoryIcon()
    {
        switch ($this->category) {
            case 'Flood':
                return 'flood_icon';
            case 'Fire':
                return 'fire_icon';
            case 'Earthquake':
                return 'earthquake_icon';
            default:
                return 'notification_icon';
        }
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            'title' => $this->title,
            'message' => $this->message,
            'category' => $this->category,
            'severity' => $this->severity,
        ];
    }

    protected function mapSeverity(string $severity)
{
    switch ($severity) {
        case 'high':
            return 'high';  // Immediate notification
        case 'medium':
            return 'normal';  // Normal priority
        case 'low':
            return 'low';  // Low priority
        default:
            return 'normal';  // Default to normal if no severity is set
    }
}
}

    
