<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Simulated Notifications</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/qrcodejs@1.0.0/qrcode.min.js"></script>
    <style>
        body {
            padding: 20px;
        }
        .card {
            margin-bottom: 20px;
        }
        #qr-container {
            display: none;
            margin-top: 20px;
            text-align: center;
        }
        #qrcode {
            display: inline-block;
            padding: 10px;
            background: white;
            border-radius: 4px;
            margin-top: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="mb-4">Test Simulated Notifications</h1>

        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        Send Simulated Notification
                    </div>
                    <div class="card-body">
                        <form id="notification-form">
                            <div class="mb-3">
                                <label for="title" class="form-label">Title</label>
                                <input type="text" class="form-control" id="title" name="title" required>
                            </div>
                            <div class="mb-3">
                                <label for="message" class="form-label">Message</label>
                                <textarea class="form-control" id="message" name="message" rows="3" required></textarea>
                            </div>
                            <div class="mb-3">
                                <label for="category" class="form-label">Category</label>
                                <input type="text" class="form-control" id="category" name="category" value="test" required>
                            </div>
                            <div class="mb-3">
                                <label for="severity" class="form-label">Severity</label>
                                <select class="form-select" id="severity" name="severity" required>
                                    <option value="low">Low</option>
                                    <option value="medium">Medium</option>
                                    <option value="high">High</option>
                                </select>
                            </div>
                            <div class="mb-3">
                                <label for="token" class="form-label">Token (optional)</label>
                                <input type="text" class="form-control" id="token" name="token">
                                <div class="form-text">Leave blank to send to all devices</div>
                            </div>
                            <button type="submit" class="btn btn-primary">Send Notification</button>
                        </form>
                    </div>
                </div>
            </div>

            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        Instructions
                    </div>
                    <div class="card-body">
                        <h5 class="card-title">How to Test</h5>
                        <ol>
                            <li>Open the Ionic app in your browser at <a href="http://localhost:8100" target="_blank">http://localhost:8100</a></li>
                            <li>Fill out the form on the left</li>
                            <li>Click "Send Notification"</li>
                            <li>Check the browser console in the Ionic app for notification logs</li>
                            <li>You should see a toast notification in the Ionic app</li>
                        </ol>
                        <div class="alert alert-info">
                            <strong>Note:</strong> This is a simulated notification system for testing purposes only. In a production environment, you would use Firebase Cloud Messaging (FCM) to send real push notifications.
                        </div>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        Response
                    </div>
                    <div class="card-body">
                        <pre id="response">No response yet</pre>

                        <div id="qr-container">
                            <div class="alert alert-info">
                                <strong>Scan this QR code</strong> with your mobile device to open the notification in the Ionic app.
                            </div>
                            <div id="qrcode"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        document.getElementById('notification-form').addEventListener('submit', function(e) {
            e.preventDefault();

            const formData = {
                title: document.getElementById('title').value,
                message: document.getElementById('message').value,
                category: document.getElementById('category').value,
                severity: document.getElementById('severity').value,
                token: document.getElementById('token').value || null
            };

            // Send the notification
            fetch('/api/simulated-notification', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json'
                },
                body: JSON.stringify(formData)
            })
            .then(response => response.json())
            .then(data => {
                document.getElementById('response').textContent = JSON.stringify(data, null, 2);

                // Send the notification to the Ionic app
                if (data.success) {
                    // Try multiple approaches to deliver the notification

                    // 1. Try to find an existing Ionic app window
                    try {
                        const ionicWindows = [
                            window.open('http://localhost:8100', '_blank'),
                            window.open('http://127.0.0.1:8100', '_blank')
                        ];

                        let windowOpened = false;

                        for (const ionicWindow of ionicWindows) {
                            if (ionicWindow) {
                                windowOpened = true;
                                // Send the notification to the Ionic app
                                ionicWindow.postMessage({
                                    type: 'simulated-notification',
                                    notification: data.notification
                                }, '*');
                            }
                        }

                        if (!windowOpened) {
                            console.warn('Could not open Ionic app window. Make sure the Ionic app is running and pop-ups are allowed.');
                        }
                    } catch (e) {
                        console.warn('Error opening Ionic app window:', e);
                    }

                    // 2. Show a QR code that can be scanned to open the notification
                    document.getElementById('qrcode').innerHTML = '';

                    // Create notification URL with data
                    const notificationData = encodeURIComponent(JSON.stringify(data.notification));
                    const notificationUrl = `http://localhost:8100?notification=${notificationData}`;

                    // Generate QR code
                    new QRCode(document.getElementById('qrcode'), {
                        text: notificationUrl,
                        width: 128,
                        height: 128
                    });

                    document.getElementById('qr-container').style.display = 'block';
                }
            })
            .catch(error => {
                document.getElementById('response').textContent = 'Error: ' + error.message;
            });
        });
    </script>
</body>
</html>
