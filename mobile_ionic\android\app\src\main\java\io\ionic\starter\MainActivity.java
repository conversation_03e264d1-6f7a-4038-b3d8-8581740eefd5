package io.ionic.starter;

import android.content.Intent;
import android.os.Bundle;
import android.util.Log;

import com.getcapacitor.BridgeActivity;

public class MainActivity extends BridgeActivity {
    private static final String TAG = "MainActivity";

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        handleNotificationIntent(getIntent());
    }

    @Override
    protected void onNewIntent(Intent intent) {
        super.onNewIntent(intent);
        setIntent(intent);
        handleNotificationIntent(intent);
    }

    private void handleNotificationIntent(Intent intent) {
        if (intent != null && intent.getExtras() != null) {
            try {
                String navigateTo = intent.getStringExtra("navigate_to");
                String disasterType = intent.getStringExtra("disaster_type");
                String filterMode = intent.getStringExtra("filter_mode");
                String fromNotification = intent.getStringExtra("from_notification");

                Log.d(TAG, "Notification intent received:");
                Log.d(TAG, "  navigate_to: " + navigateTo);
                Log.d(TAG, "  disaster_type: " + disasterType);
                Log.d(TAG, "  filter_mode: " + filterMode);
                Log.d(TAG, "  from_notification: " + fromNotification);

                // The Ionic app will handle the navigation through the FCM service
                // This just ensures the intent data is available
            } catch (Exception e) {
                Log.e(TAG, "Error handling notification intent", e);
            }
        }
    }
}
