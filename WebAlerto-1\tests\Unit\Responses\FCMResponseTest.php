<?php

namespace Tests\Unit\Responses;

use Tests\TestCase;
use App\Http\Responses\FCMResponse;
use Illuminate\Http\JsonResponse;

class FCMResponseTest extends TestCase
{
    public function test_success_response()
    {
        $response = FCMResponse::success('Test notification sent successfully');

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(200, $response->getStatusCode());
        $this->assertEquals([
            'success' => true,
            'message' => 'Test notification sent successfully'
        ], json_decode($response->getContent(), true));
    }

    public function test_success_response_with_data()
    {
        $data = [
            'message_id' => 'test_message_id',
            'sent_at' => now()->toIso8601String()
        ];

        $response = FCMResponse::success('Test notification sent successfully', $data);

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(200, $response->getStatusCode());
        $this->assertEquals([
            'success' => true,
            'message' => 'Test notification sent successfully',
            'data' => $data
        ], json_decode($response->getContent(), true));
    }

    public function test_error_response()
    {
        $response = FCMResponse::error('Failed to send notification');

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(400, $response->getStatusCode());
        $this->assertEquals([
            'success' => false,
            'message' => 'Failed to send notification'
        ], json_decode($response->getContent(), true));
    }

    public function test_error_response_with_custom_status_code()
    {
        $response = FCMResponse::error('Unauthorized', 401);

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(401, $response->getStatusCode());
        $this->assertEquals([
            'success' => false,
            'message' => 'Unauthorized'
        ], json_decode($response->getContent(), true));
    }

    public function test_error_response_with_errors()
    {
        $errors = [
            'token' => ['The token field is required.'],
            'title' => ['The title field is required.']
        ];

        $response = FCMResponse::error('Validation failed', 422, $errors);

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(422, $response->getStatusCode());
        $this->assertEquals([
            'success' => false,
            'message' => 'Validation failed',
            'errors' => $errors
        ], json_decode($response->getContent(), true));
    }

    public function test_not_found_response()
    {
        $response = FCMResponse::notFound('Notification not found');

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(404, $response->getStatusCode());
        $this->assertEquals([
            'success' => false,
            'message' => 'Notification not found'
        ], json_decode($response->getContent(), true));
    }

    public function test_unauthorized_response()
    {
        $response = FCMResponse::unauthorized('Invalid API key');

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(401, $response->getStatusCode());
        $this->assertEquals([
            'success' => false,
            'message' => 'Invalid API key'
        ], json_decode($response->getContent(), true));
    }

    public function test_forbidden_response()
    {
        $response = FCMResponse::forbidden('Access denied');

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(403, $response->getStatusCode());
        $this->assertEquals([
            'success' => false,
            'message' => 'Access denied'
        ], json_decode($response->getContent(), true));
    }

    public function test_too_many_requests_response()
    {
        $response = FCMResponse::tooManyRequests('Rate limit exceeded');

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(429, $response->getStatusCode());
        $this->assertEquals([
            'success' => false,
            'message' => 'Rate limit exceeded'
        ], json_decode($response->getContent(), true));
    }

    public function test_server_error_response()
    {
        $response = FCMResponse::serverError('Internal server error');

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(500, $response->getStatusCode());
        $this->assertEquals([
            'success' => false,
            'message' => 'Internal server error'
        ], json_decode($response->getContent(), true));
    }

    public function test_bulk_success_response()
    {
        $data = [
            'success_count' => 2,
            'failure_count' => 0,
            'results' => [
                ['status' => 'sent'],
                ['status' => 'sent']
            ]
        ];

        $response = FCMResponse::bulkSuccess($data);

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(200, $response->getStatusCode());
        $this->assertEquals([
            'success' => true,
            'data' => $data
        ], json_decode($response->getContent(), true));
    }

    public function test_bulk_error_response()
    {
        $data = [
            'success_count' => 1,
            'failure_count' => 1,
            'results' => [
                ['status' => 'sent'],
                ['status' => 'failed', 'error' => 'Invalid token']
            ]
        ];

        $response = FCMResponse::bulkError($data);

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(400, $response->getStatusCode());
        $this->assertEquals([
            'success' => false,
            'data' => $data
        ], json_decode($response->getContent(), true));
    }
} 