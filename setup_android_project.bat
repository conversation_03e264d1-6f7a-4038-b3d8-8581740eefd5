@echo off
echo ========================================
echo WebAlerto Android Project Setup Script
echo ========================================
echo.

echo This script will help you set up the WebAlerto disaster alert system
echo for your new Android project.
echo.

echo Choose your setup option:
echo 1. Use existing Ionic project (Recommended)
echo 2. Set up native Android project
echo 3. Just start the Laravel backend
echo.

set /p choice="Enter your choice (1-3): "

if "%choice%"=="1" goto ionic_setup
if "%choice%"=="2" goto native_setup
if "%choice%"=="3" goto backend_only
goto invalid_choice

:ionic_setup
echo.
echo ========================================
echo Setting up Ionic Project
echo ========================================
echo.

echo Step 1: Installing Ionic dependencies...
cd mobile_ionic
call npm install
if errorlevel 1 (
    echo Error: Failed to install npm dependencies
    pause
    exit /b 1
)

echo.
echo Step 2: Building Ionic project...
call ionic build
if errorlevel 1 (
    echo Error: Failed to build Ionic project
    pause
    exit /b 1
)

echo.
echo Step 3: Adding Android platform...
call ionic capacitor add android
if errorlevel 1 (
    echo Warning: Android platform might already exist
)

echo.
echo Step 4: Copying google-services.json...
if exist google-services.json (
    copy google-services.json android\app\
    echo google-services.json copied successfully
) else (
    echo WARNING: google-services.json not found!
    echo Please download it from Firebase Console and place it in:
    echo 1. mobile_ionic\ folder
    echo 2. mobile_ionic\android\app\ folder
)

echo.
echo Step 5: Building Android project...
call ionic capacitor build android
if errorlevel 1 (
    echo Error: Failed to build Android project
    pause
    exit /b 1
)

echo.
echo Step 6: Opening in Android Studio...
call ionic capacitor open android

echo.
echo Ionic setup completed!
echo Next steps:
echo 1. Update Firebase configuration in src/environments/environment.ts
echo 2. Update API URL to your computer's IP address
echo 3. Build and run the app from Android Studio
echo.
goto end

:native_setup
echo.
echo ========================================
echo Setting up Native Android Project
echo ========================================
echo.

echo Creating Android project structure...
mkdir android_native_project 2>nul
cd android_native_project

echo.
echo Copying example files...
xcopy /s /y ..\android_native_examples\* .

echo.
echo Native Android setup completed!
echo.
echo Next steps:
echo 1. Create a new Android Studio project
echo 2. Copy the Java files to your project's src/main/java folder
echo 3. Copy the layout files to your project's res/layout folder
echo 4. Add the required dependencies to your build.gradle files
echo 5. Add your google-services.json file to the app folder
echo 6. Update the package name in all Java files
echo 7. Update the API URL in ApiService.java
echo.
echo Files created in android_native_project folder:
echo - MainActivity.java
echo - MyFirebaseMessagingService.java
echo - ApiService.java
echo - DisasterAlert.java
echo - layout/activity_main.xml
echo.
goto end

:backend_only
echo.
echo ========================================
echo Starting Laravel Backend Only
echo ========================================
echo.
goto start_backend

:invalid_choice
echo Invalid choice. Please run the script again and choose 1, 2, or 3.
pause
exit /b 1

:start_backend
echo.
echo Starting Laravel backend...
cd WebAlerto-1

echo.
echo Step 1: Installing Composer dependencies...
call composer install
if errorlevel 1 (
    echo Error: Failed to install Composer dependencies
    echo Make sure Composer is installed and in your PATH
    pause
    exit /b 1
)

echo.
echo Step 2: Checking .env file...
if not exist .env (
    echo Creating .env file from .env.example...
    copy .env.example .env
    echo.
    echo Please update the .env file with your database and Firebase configuration
    echo Press any key to continue after updating .env...
    pause
)

echo.
echo Step 3: Generating application key...
call php artisan key:generate

echo.
echo Step 4: Running database migrations...
call php artisan migrate
if errorlevel 1 (
    echo Warning: Database migration failed
    echo Make sure your database is configured correctly in .env
)

echo.
echo Step 5: Starting Laravel development server...
echo Server will start on http://localhost:8000
echo.
echo To access from mobile device, use your computer's IP address:
for /f "tokens=2 delims=:" %%a in ('ipconfig ^| findstr /c:"IPv4 Address"') do (
    for /f "tokens=1" %%b in ("%%a") do echo http://%%b:8000
)
echo.
echo Press Ctrl+C to stop the server
echo.

call php artisan serve --host=0.0.0.0 --port=8000

:end
echo.
echo ========================================
echo Setup completed!
echo ========================================
echo.

if "%choice%"=="1" (
    echo Your Ionic project is ready!
    echo Open Android Studio and run the project on your device.
) else if "%choice%"=="2" (
    echo Your native Android files are ready!
    echo Create a new Android Studio project and copy the files.
)

echo.
echo Important reminders:
echo 1. Update Firebase configuration with your project details
echo 2. Update API URLs to point to your Laravel backend
echo 3. Make sure your Laravel backend is running
echo 4. Test FCM notifications using the provided test scripts
echo.

echo For testing FCM notifications, use:
echo php test-fcm-notification.php YOUR_FCM_TOKEN
echo.

pause
