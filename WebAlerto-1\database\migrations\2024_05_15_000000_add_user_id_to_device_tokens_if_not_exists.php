<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Check if the user_id column exists in the device_tokens table
        if (!Schema::hasColumn('device_tokens', 'user_id')) {
            Schema::table('device_tokens', function (Blueprint $table) {
                // Add the user_id column as nullable
                $table->unsignedBigInteger('user_id')->nullable()->after('id');

                // Add the foreign key constraint
                $table->foreign('user_id')
                      ->references('id')
                      ->on('users')
                      ->onDelete('cascade');
            });

            // Column was added successfully
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // We don't want to drop the user_id column if we're rolling back,
        // as it might be used by other migrations
    }

    /**
     * Check if a foreign key exists
     */
    protected function hasForeignKey(string $table, string $column): bool
    {
        try {
            // Try to drop the foreign key and catch the exception if it doesn't exist
            Schema::table($table, function (Blueprint $table) use ($column) {
                $table->dropForeign([$column]);
            });

            // If we get here, the foreign key existed and was dropped
            // We need to add it back
            Schema::table($table, function (Blueprint $table) use ($column) {
                $table->foreign($column)
                      ->references('id')
                      ->on('users')
                      ->onDelete('cascade');
            });

            return true;
        } catch (\Exception $e) {
            // Foreign key doesn't exist
            return false;
        }
    }
};
