import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { IonicModule, ToastController } from '@ionic/angular';
import { Router } from '@angular/router';
import { NotificationListComponent } from '../../components/notification-list/notification-list.component';
import { FcmService } from '../../services/fcm.service';

@Component({
  selector: 'app-home',
  templateUrl: './home.page.html',
  styleUrls: ['./home.page.scss'],
  standalone: true,
  imports: [IonicModule, CommonModule, NotificationListComponent]
})
export class HomePage implements OnInit {
  isOffline = false;

  constructor(
    private router: Router,
    private toastCtrl: ToastController,
    private fcmService: FcmService
  ) {}

  ngOnInit() {
    const savedOfflineStatus = localStorage.getItem('isOffline');
    this.isOffline = savedOfflineStatus === 'true';
  }

  toggleStatus() {
    this.isOffline = !this.isOffline;
    localStorage.setItem('isOffline', String(this.isOffline));
  }

  openDisasterMap(disasterType: string) {
    console.log(`Navigating to map tab for disaster type: ${disasterType}`);

    // Map the disaster type to the backend's expected format
    let mappedType = disasterType;
    if (disasterType === 'earthquake') {
      mappedType = 'Earthquake';
    } else if (disasterType === 'typhoon') {
      mappedType = 'Typhoon';
    } else if (disasterType === 'flashflood') {
      mappedType = 'Flood';
    }

    // Show loading toast
    this.toastCtrl.create({
      message: `Loading evacuation centers for ${mappedType}...`,
      duration: 2000,
      color: 'primary'
    }).then(toast => toast.present());

    // Navigate to the map tab with the disaster type as a query parameter
    this.router.navigate(['/tabs/map'], {
      queryParams: {
        disasterType: mappedType,
        filterMode: 'true'
      }
    });
  }

  viewMap() {
    console.log(`Viewing all evacuation centers in ${this.isOffline ? 'offline' : 'online'} mode`);

    // Show loading toast
    this.toastCtrl.create({
      message: 'Loading all evacuation centers...',
      duration: 2000,
      color: 'primary'
    }).then(toast => toast.present());

    // Navigate to the map tab to show all evacuation centers
    this.router.navigate(['/tabs/map'], {
      queryParams: {
        disasterType: 'all',
        filterMode: 'true'
      }
    });
  }


}