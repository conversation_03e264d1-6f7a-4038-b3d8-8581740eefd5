import{f as c}from"./chunk-BAKMWPBW.js";var y=(e,t=0)=>new Promise(a=>{l(e,t,a)}),l=(e,t=0,a)=>{let n,i,r={passive:!0},d=500,u=()=>{n&&n()},s=o=>{(o===void 0||e===o.target)&&(u(),a(o))};return e&&(e.addEventListener("webkitTransitionEnd",s,r),e.addEventListener("transitionend",s,r),i=setTimeout(s,t+d),n=()=>{i!==void 0&&(clearTimeout(i),i=void 0),e.removeEventListener("webkitTransitionEnd",s,r),e.removeEventListener("transitionend",s,r)}),u},v=(e,t)=>{e.componentOnReady?e.componentOnReady().then(a=>t(a)):b(()=>t(e))},g=e=>e.componentOnReady!==void 0,f=(e,t=[])=>{let a={};return t.forEach(n=>{e.hasAttribute(n)&&(e.getAttribute(n)!==null&&(a[n]=e.getAttribute(n)),e.removeAttribute(n))}),a},m=["role","aria-activedescendant","aria-atomic","aria-autocomplete","aria-braillelabel","aria-brailleroledescription","aria-busy","aria-checked","aria-colcount","aria-colindex","aria-colindextext","aria-colspan","aria-controls","aria-current","aria-describedby","aria-description","aria-details","aria-disabled","aria-errormessage","aria-expanded","aria-flowto","aria-haspopup","aria-hidden","aria-invalid","aria-keyshortcuts","aria-label","aria-labelledby","aria-level","aria-live","aria-multiline","aria-multiselectable","aria-orientation","aria-owns","aria-placeholder","aria-posinset","aria-pressed","aria-readonly","aria-relevant","aria-required","aria-roledescription","aria-rowcount","aria-rowindex","aria-rowindextext","aria-rowspan","aria-selected","aria-setsize","aria-sort","aria-valuemax","aria-valuemin","aria-valuenow","aria-valuetext"],w=(e,t)=>{let a=m;return t&&t.length>0&&(a=a.filter(n=>!t.includes(n))),f(e,a)},A=(e,t,a,n)=>e.addEventListener(t,a,n),T=(e,t,a,n)=>e.removeEventListener(t,a,n),x=(e,t=e)=>e.shadowRoot||t,b=e=>typeof __zone_symbol__requestAnimationFrame=="function"?__zone_symbol__requestAnimationFrame(e):typeof requestAnimationFrame=="function"?requestAnimationFrame(e):setTimeout(e),h=e=>!!e.shadowRoot&&!!e.attachShadow,_=e=>{if(e.focus(),e.classList.contains("ion-focusable")){let t=e.closest("ion-app");t&&t.setFocus([e])}},L=(e,t,a,n,i)=>{if(e||h(t)){let r=t.querySelector("input.aux-input");r||(r=t.ownerDocument.createElement("input"),r.type="hidden",r.classList.add("aux-input"),t.appendChild(r)),r.disabled=i,r.name=a,r.value=n||""}},R=(e,t,a)=>Math.max(e,Math.min(t,a)),O=(e,t)=>{if(!e){let a="ASSERT: "+t;c(a);debugger;throw new Error(a)}},k=e=>{if(e){let t=e.changedTouches;if(t&&t.length>0){let a=t[0];return{x:a.clientX,y:a.clientY}}if(e.pageX!==void 0)return{x:e.pageX,y:e.pageY}}return{x:0,y:0}},q=e=>{let t=document.dir==="rtl";switch(e){case"start":return t;case"end":return!t;default:throw new Error(`"${e}" is not a valid value for [side]. Use "start" or "end" instead.`)}},S=(e,t)=>{let a=e._original||e;return{_original:e,emit:p(a.emit.bind(a),t)}},p=(e,t=0)=>{let a;return(...n)=>{clearTimeout(a),a=setTimeout(e,t,...n)}},F=(e,t)=>{if(e!=null||(e={}),t!=null||(t={}),e===t)return!0;let a=Object.keys(e);if(a.length!==Object.keys(t).length)return!1;for(let n of a)if(!(n in t)||e[n]!==t[n])return!1;return!0},I=e=>typeof e=="number"&&!isNaN(e)&&isFinite(e);export{y as a,v as b,g as c,f as d,w as e,A as f,T as g,x as h,b as i,h as j,_ as k,L as l,R as m,O as n,k as o,q as p,S as q,p as r,F as s,I as t};
