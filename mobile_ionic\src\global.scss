/* Emergency notification styles - must be first */
@use "app/styles/emergency-notifications.scss";

/*
 * App Global CSS
 * ----------------------------------------------------------------------------
 * Put style rules here that you want to apply globally. These styles are for
 * the entire app and not just one component. Additionally, this file can be
 * used as an entry point to import other CSS/Sass files to be included in the
 * output CSS.
 * For more information on global stylesheets, visit the documentation:
 * https://ionicframework.com/docs/layout/global-stylesheets
 */

/* Core CSS required for Ionic components to work properly */
@import "@ionic/angular/css/core.css";

/* Basic CSS for apps built with Ionic */
@import "@ionic/angular/css/normalize.css";
@import "@ionic/angular/css/structure.css";
@import "@ionic/angular/css/typography.css";
@import "@ionic/angular/css/display.css";

/* Optional CSS utils that can be commented out */
@import "@ionic/angular/css/padding.css";
@import "@ionic/angular/css/float-elements.css";
@import "@ionic/angular/css/text-alignment.css";
@import "@ionic/angular/css/text-transformation.css";
@import "@ionic/angular/css/flex-utils.css";

/**
 * Ionic Dark Mode
 * -----------------------------------------------------
 * For more info, please see:
 * https://ionicframework.com/docs/theming/dark-mode
 */

/* @import "@ionic/angular/css/palettes/dark.always.css"; */
/* @import "@ionic/angular/css/palettes/dark.class.css"; */
@import '@ionic/angular/css/palettes/dark.system.css';
@import "leaflet/dist/leaflet.css";

/* Custom Modal Styles */
.evacuation-center-modal {
  --backdrop-opacity: 0.6;
  --width: 90%;
  --height: auto;
  --border-radius: 12px;
  --box-shadow: 0 28px 48px rgba(0, 0, 0, 0.4);
}

.evacuation-center-modal .modal-wrapper {
  position: relative;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
  overflow: hidden;
  border-radius: 12px;
}

.evacuation-center-modal .ion-page {
  position: relative;
  display: block;
  contain: content;
}

/* Placeholder for evacuation center images */
.center-image img[src$="evacuation-placeholder.jpg"] {
  object-fit: cover;
  background-color: #f0f0f0;
  background-image: linear-gradient(45deg, #e0e0e0 25%, transparent 25%, transparent 75%, #e0e0e0 75%, #e0e0e0),
                    linear-gradient(45deg, #e0e0e0 25%, transparent 25%, transparent 75%, #e0e0e0 75%, #e0e0e0);
  background-size: 20px 20px;
  background-position: 0 0, 10px 10px;
}

/* Leaflet popup styling */
.evacuation-popup {
  padding: 5px;
  max-width: 250px;
}

.evacuation-popup h3 {
  margin: 0 0 8px 0;
  color: #1565c0;
  font-size: 16px;
  font-weight: 600;
  border-bottom: 1px solid #eee;
  padding-bottom: 5px;
}

.evacuation-popup p {
  margin: 5px 0;
  font-size: 14px;
  line-height: 1.4;
}

.evacuation-popup strong {
  font-weight: 600;
  color: #333;
}

/* Evacuation Details Modal Styles */
.evacuation-details-modal {
  --backdrop-opacity: 0.6;
  --border-radius: 16px 16px 0 0;
  --box-shadow: 0 -4px 16px rgba(0, 0, 0, 0.12);
}

/* Marker icon styles */
.leaflet-marker-icon {
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
  transition: transform 0.2s ease-in-out;
}

.leaflet-marker-icon:hover {
  transform: scale(1.1);
}

.evacuation-details-modal .modal-wrapper {
  position: absolute;
  bottom: 0;
  border-radius: 16px 16px 0 0;
  box-shadow: 0 -4px 16px rgba(0, 0, 0, 0.12);
}

.evacuation-details-modal ion-content {
  --padding-top: 16px;
  --padding-bottom: 16px;
  --padding-start: 16px;
  --padding-end: 16px;
}

.evacuation-details-modal ion-header {
  position: relative;
}

.evacuation-details-modal ion-header::after {
  content: '';
  position: absolute;
  top: 8px;
  left: 50%;
  transform: translateX(-50%);
  width: 40px;
  height: 4px;
  background-color: var(--ion-color-medium);
  border-radius: 2px;
  opacity: 0.5;
}

/* Emergency Alert Styles */
.emergency-alert {
  --backdrop-opacity: 0.9;
  --background: #f44336;
  --height: auto;
}

.emergency-alert .alert-head {
  background-color: #d32f2f;
  color: white;
  padding: 16px;
  text-align: center;
}

.emergency-alert .alert-title {
  font-size: 20px;
  font-weight: bold;
  color: white;
  text-transform: uppercase;
}

.emergency-alert .alert-sub-title {
  font-size: 16px;
  color: rgba(255, 255, 255, 0.9);
  margin-top: 4px;
}

.emergency-alert .alert-message {
  color: white;
  font-size: 16px;
  padding: 16px;
  text-align: center;
}

.emergency-alert .alert-button-group {
  padding: 8px;
}

.emergency-alert .alert-button {
  color: white;
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 4px;
  margin: 4px;
}

.emergency-alert .alert-button:hover {
  background-color: rgba(255, 255, 255, 0.3);
}

/* Emergency Notification Styles - Disaster Specific */
.emergency-notification {
  --backdrop-opacity: 0.95;
  --width: 90%;
  --max-width: 400px;
  --border-radius: 16px;
  --box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.emergency-notification .alert-wrapper {
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.emergency-notification .alert-head {
  padding: 24px 20px 16px 20px;
  text-align: center;
  position: relative;
}

.emergency-notification .alert-head::before {
  content: '!';
  position: absolute;
  top: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: #d32f2f;
  color: white;
  font-size: 24px;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 3px solid white;
}

.emergency-notification .alert-title {
  font-size: 18px;
  font-weight: bold;
  color: white;
  text-transform: uppercase;
  margin-top: 8px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.emergency-notification .alert-sub-title {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.95);
  margin-top: 4px;
  font-weight: 600;
}

.emergency-notification .alert-message {
  color: white;
  font-size: 16px;
  padding: 16px 20px;
  text-align: center;
  line-height: 1.4;
  background-color: rgba(0, 0, 0, 0.1);
}

.emergency-notification .alert-button-group {
  padding: 16px;
  background-color: rgba(255, 255, 255, 0.1);
}

.emergency-notification .alert-button {
  color: white;
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  margin: 4px;
  font-weight: 600;
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.emergency-notification .alert-button:hover {
  background-color: rgba(255, 255, 255, 0.3);
}

/* Earthquake Alert - Orange */
.earthquake-alert {
  --background: #ffa500;
}

.earthquake-alert .alert-head {
  background: linear-gradient(135deg, #ffa500 0%, #ff8c00 100%);
}

.earthquake-alert .alert-head::before {
  background-color: #cc7a00;
}

/* Flood Alert - Blue */
.flood-alert {
  --background: #0066cc;
}

.flood-alert .alert-head {
  background: linear-gradient(135deg, #0066cc 0%, #0052a3 100%);
}

.flood-alert .alert-head::before {
  background-color: #004080;
}

/* Typhoon Alert - Green */
.typhoon-alert {
  --background: #008000;
}

.typhoon-alert .alert-head {
  background: linear-gradient(135deg, #008000 0%, #006600 100%);
}

.typhoon-alert .alert-head::before {
  background-color: #004d00;
}

/* Fire Alert - Red */
.fire-alert {
  --background: #ff0000;
}

.fire-alert .alert-head {
  background: linear-gradient(135deg, #ff0000 0%, #cc0000 100%);
}

.fire-alert .alert-head::before {
  background-color: #990000;
}

/* General Alert - Gray */
.general-alert {
  --background: #666666;
}

.general-alert .alert-head {
  background: linear-gradient(135deg, #666666 0%, #555555 100%);
}

.general-alert .alert-head::before {
  background-color: #444444;
}
