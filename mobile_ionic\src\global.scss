/*
 * App Global CSS
 * ----------------------------------------------------------------------------
 * Put style rules here that you want to apply globally. These styles are for
 * the entire app and not just one component. Additionally, this file can be
 * used as an entry point to import other CSS/Sass files to be included in the
 * output CSS.
 * For more information on global stylesheets, visit the documentation:
 * https://ionicframework.com/docs/layout/global-stylesheets
 */

/* Core CSS required for Ionic components to work properly */
@import "@ionic/angular/css/core.css";

/* Basic CSS for apps built with Ionic */
@import "@ionic/angular/css/normalize.css";
@import "@ionic/angular/css/structure.css";
@import "@ionic/angular/css/typography.css";
@import "@ionic/angular/css/display.css";

/* Optional CSS utils that can be commented out */
@import "@ionic/angular/css/padding.css";
@import "@ionic/angular/css/float-elements.css";
@import "@ionic/angular/css/text-alignment.css";
@import "@ionic/angular/css/text-transformation.css";
@import "@ionic/angular/css/flex-utils.css";

/**
 * Ionic Dark Mode
 * -----------------------------------------------------
 * For more info, please see:
 * https://ionicframework.com/docs/theming/dark-mode
 */

/* @import "@ionic/angular/css/palettes/dark.always.css"; */
/* @import "@ionic/angular/css/palettes/dark.class.css"; */
@import '@ionic/angular/css/palettes/dark.system.css';
@import "leaflet/dist/leaflet.css";

/* Custom Modal Styles */
.evacuation-center-modal {
  --backdrop-opacity: 0.6;
  --width: 90%;
  --height: auto;
  --border-radius: 12px;
  --box-shadow: 0 28px 48px rgba(0, 0, 0, 0.4);
}

.evacuation-center-modal .modal-wrapper {
  position: relative;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
  overflow: hidden;
  border-radius: 12px;
}

.evacuation-center-modal .ion-page {
  position: relative;
  display: block;
  contain: content;
}

/* Placeholder for evacuation center images */
.center-image img[src$="evacuation-placeholder.jpg"] {
  object-fit: cover;
  background-color: #f0f0f0;
  background-image: linear-gradient(45deg, #e0e0e0 25%, transparent 25%, transparent 75%, #e0e0e0 75%, #e0e0e0),
                    linear-gradient(45deg, #e0e0e0 25%, transparent 25%, transparent 75%, #e0e0e0 75%, #e0e0e0);
  background-size: 20px 20px;
  background-position: 0 0, 10px 10px;
}

/* Leaflet popup styling */
.evacuation-popup {
  padding: 5px;
  max-width: 250px;
}

.evacuation-popup h3 {
  margin: 0 0 8px 0;
  color: #1565c0;
  font-size: 16px;
  font-weight: 600;
  border-bottom: 1px solid #eee;
  padding-bottom: 5px;
}

.evacuation-popup p {
  margin: 5px 0;
  font-size: 14px;
  line-height: 1.4;
}

.evacuation-popup strong {
  font-weight: 600;
  color: #333;
}

/* Evacuation Details Modal Styles */
.evacuation-details-modal {
  --backdrop-opacity: 0.6;
  --border-radius: 16px 16px 0 0;
  --box-shadow: 0 -4px 16px rgba(0, 0, 0, 0.12);
}

/* Marker icon styles */
.leaflet-marker-icon {
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
  transition: transform 0.2s ease-in-out;
}

.leaflet-marker-icon:hover {
  transform: scale(1.1);
}

.evacuation-details-modal .modal-wrapper {
  position: absolute;
  bottom: 0;
  border-radius: 16px 16px 0 0;
  box-shadow: 0 -4px 16px rgba(0, 0, 0, 0.12);
}

.evacuation-details-modal ion-content {
  --padding-top: 16px;
  --padding-bottom: 16px;
  --padding-start: 16px;
  --padding-end: 16px;
}

.evacuation-details-modal ion-header {
  position: relative;
}

.evacuation-details-modal ion-header::after {
  content: '';
  position: absolute;
  top: 8px;
  left: 50%;
  transform: translateX(-50%);
  width: 40px;
  height: 4px;
  background-color: var(--ion-color-medium);
  border-radius: 2px;
  opacity: 0.5;
}

/* Emergency Alert Styles */
.emergency-alert {
  --backdrop-opacity: 0.9;
  --background: #f44336;
  --height: auto;
}

.emergency-alert .alert-head {
  background-color: #d32f2f;
  color: white;
  padding: 16px;
  text-align: center;
}

.emergency-alert .alert-title {
  font-size: 20px;
  font-weight: bold;
  color: white;
  text-transform: uppercase;
}

.emergency-alert .alert-sub-title {
  font-size: 16px;
  color: rgba(255, 255, 255, 0.9);
  margin-top: 4px;
}

.emergency-alert .alert-message {
  color: white;
  font-size: 16px;
  padding: 16px;
  text-align: center;
}

.emergency-alert .alert-button-group {
  padding: 8px;
}

.emergency-alert .alert-button {
  color: white;
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 4px;
  margin: 4px;
}

.emergency-alert .alert-button:hover {
  background-color: rgba(255, 255, 255, 0.3);
}
