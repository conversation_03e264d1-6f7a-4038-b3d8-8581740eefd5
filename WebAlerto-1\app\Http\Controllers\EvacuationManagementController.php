<?php
namespace App\Http\Controllers;
use App\Models\Evacuation;
use App\Models\AppNotification;
use App\Models\User;
use App\Models\PushNotification;
use Illuminate\Http\Request;

class EvacuationManagementController extends Controller
{
    public function showDashboard(Request $request)
    {
        $query = Evacuation::query();

        if ($request->filled('search')) {
            $searchTerm = $request->search;
            $query->where(function($q) use ($searchTerm) {
                $q->where('name', 'LIKE', '%' . $searchTerm . '%')
                  ->orWhere('disaster_type', 'LIKE', '%' . $searchTerm . '%');
            });
        }

        $centers = $query->paginate(10);

        // Get total counts for active and inactive centers
        $activeCount = Evacuation::where('status', 'Active')->count();
        $inactiveCount = Evacuation::where('status', 'Inactive')->count();

        return view('components.evacuation_management.evacuation-dashboard', compact('centers', 'activeCount', 'inactiveCount'));
    }

    public function showAddForm()
    {
        return view('components.evacuation_management.add-evacuation-center');
    }

    public function showEditForm($id)
    {
        $evacuationCenter = Evacuation::findOrFail($id);
        return view('components.evacuation_management.edit-evacuation-center', compact('evacuationCenter'));
    }

    public function store(Request $request)
    {
        $validatedData = $request->validate([
            'name' => 'required|string|max:255',
            'street_name' => 'required|string|max:255',
            'province' => 'required|string|max:255',
            'city' => 'required|string|max:255',
            'postal_code' => 'required|string|max:20',
            'barangay' => 'required|string|max:255',
            'latitude' => 'nullable|numeric',
            'longitude' => 'nullable|numeric',
            'capacity' => 'required|integer|min:1',
            'contact' => 'required|string|max:255',
            'disaster_type' => 'required|string|in:Typhoon,Flood,Earthquake',
            'status' => 'required|string|in:Active,Inactive',
        ]);

        // Set marker color based on disaster type
        $markerColors = [
            'Typhoon' => '#FF0000', // Red
            'Flood' => '#00FF00',   // Green
            'Earthquake' => '#FFA500' // Yellow-Orange
        ];

        $validatedData['marker_color'] = $markerColors[$validatedData['disaster_type']];

        // Check for existing center with the same details
        $existingCenter = Evacuation::where(function($query) use ($validatedData) {
            $query->where('name', $validatedData['name'])
                ->where('street_name', $validatedData['street_name'])
                ->where('barangay', $validatedData['barangay'])
                ->where('city', $validatedData['city'])
                ->where('province', $validatedData['province']);
        })->first();

        if ($existingCenter) {
            return back()->withInput()->withErrors([
                'name' => 'An evacuation center with these details already exists.'
            ]);
        }

        $evacuationCenter = Evacuation::create($validatedData);

        // Send notifications to all users about the new evacuation center
        $this->sendEvacuationCenterNotification($evacuationCenter);

        return redirect()->route('components.evacuation_management.evacuation-dashboard')
            ->with('success', 'Evacuation center added successfully!');
    }

    public function edit($id)
    {
        $evacuationCenter = Evacuation::findOrFail($id);
        return view('components.evacuation_management.edit-evacuation-center', compact('evacuationCenter'));
    }

    public function update(Request $request, $id)
    {
        $validatedData = $request->validate([
            'name' => 'required|string|max:255',
            'street_name' => 'required|string|max:255',
            'province' => 'required|string|max:255',
            'city' => 'required|string|max:255',
            'postal_code' => 'required|string|max:20',
            'barangay' => 'required|string|max:255',
            'latitude' => 'nullable|numeric',
            'longitude' => 'nullable|numeric',
            'capacity' => 'required|integer|min:1',
            'contact' => 'required|string|max:255',
            'disaster_type' => 'required|string|in:Typhoon,Flood,Earthquake',
            'status' => 'required|string|in:Active,Inactive',
        ]);

        // Set marker color based on disaster type
        $markerColors = [
            'Typhoon' => '#FF0000', // Red
            'Flood' => '#00FF00',   // Green
            'Earthquake' => '#FFA500' // Yellow-Orange
        ];

        $validatedData['marker_color'] = $markerColors[$validatedData['disaster_type']];

        // Check for existing center with the same details, excluding the current center
        $existingCenter = Evacuation::where(function($query) use ($validatedData, $id) {
            $query->where('name', $validatedData['name'])
                ->where('street_name', $validatedData['street_name'])
                ->where('barangay', $validatedData['barangay'])
                ->where('city', $validatedData['city'])
                ->where('province', $validatedData['province'])
                ->where('id', '!=', $id);
        })->first();

        if ($existingCenter) {
            return back()->withInput()->withErrors([
                'name' => 'An evacuation center with these details already exists.'
            ]);
        }

        $evacuationCenter = Evacuation::findOrFail($id);
        $evacuationCenter->update($validatedData);

        return redirect()->route('components.evacuation_management.evacuation-dashboard')
            ->with('success', 'Evacuation center updated successfully!');
    }

    public function destroy($id)
    {
        $evacuationCenter = Evacuation::findOrFail($id);
        $evacuationCenter->delete();

        return redirect()->route('components.evacuation_management.evacuation-dashboard')
            ->with('success', 'Evacuation center deleted successfully!');
    }

    public function show($id)
    {
        $evacuationCenter = Evacuation::findOrFail($id);
        return view('components.evacuation_management.view-map', compact('evacuationCenter'));
    }

    public function centersList($type)
    {
        $query = Evacuation::query();

        switch (strtolower($type)) {
            case 'active':
                $query->where('status', 'Active');
                $title = 'Active Centers';
                break;
            case 'inactive':
                $query->where('status', 'Inactive');
                $title = 'Inactive Centers';
                break;
            default:
                $title = 'All Centers';
        }

        $centers = $query->paginate(10);

        return view('components.evacuation_management.centers-list', compact('centers', 'title', 'type'));
    }

    public function getCenterDetails($id)
    {
        $center = Evacuation::findOrFail($id);
        return response()->json($center);
    }

    /**
     * API endpoint to list all evacuation centers for mobile app
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function apiList()
    {
        // Log the request for debugging
        \Log::info('API request received for evacuation centers');

        $centers = Evacuation::select([
            'id', 'name', 'street_name', 'barangay', 'city', 'province',
            'latitude', 'longitude', 'capacity', 'status', 'disaster_type',
            'contact'
        ])->get();

        // Log the raw data from the database
        \Log::info('Retrieved evacuation centers from database', ['count' => $centers->count()]);

        $formattedCenters = $centers->map(function($center) {
            return [
                'id' => $center->id,
                'name' => $center->name,
                'address' => "{$center->street_name}, {$center->barangay}, {$center->city}, {$center->province}",
                'latitude' => $center->latitude,
                'longitude' => $center->longitude,
                'capacity' => $center->capacity,
                'status' => $center->status,
                'disaster_type' => $center->disaster_type,
                'contact' => $center->contact
            ];
        });

        // Log the formatted data being returned
        \Log::info('Returning formatted evacuation centers', ['count' => $formattedCenters->count()]);

        return response()->json($formattedCenters);
    }

    /**
     * Send notification to all users when a new evacuation center is added
     */
    private function sendEvacuationCenterNotification($evacuationCenter)
    {
        try {
            // Create app notifications for all users
            $users = User::all();

            foreach ($users as $user) {
                AppNotification::createEvacuationCenterNotification($evacuationCenter, $user->id);
            }

            // Also create a broadcast notification (user_id = null)
            AppNotification::createEvacuationCenterNotification($evacuationCenter, null);

            // Send FCM push notification
            $title = 'New Evacuation Center Added';
            $message = "A new evacuation center '{$evacuationCenter->name}' has been added in {$evacuationCenter->city}, {$evacuationCenter->province} for {$evacuationCenter->disaster_type} emergencies.";

            $pushNotification = new PushNotification();
            $pushNotification->title = $title;
            $pushNotification->message = $message;
            $pushNotification->category = 'evacuation_center';
            $pushNotification->severity = 'medium';
            $pushNotification->data = [
                'type' => 'evacuation_center_added',
                'evacuation_center_id' => $evacuationCenter->id,
                'evacuation_center_name' => $evacuationCenter->name,
                'disaster_type' => $evacuationCenter->disaster_type,
                'location' => [
                    'city' => $evacuationCenter->city,
                    'province' => $evacuationCenter->province,
                    'latitude' => $evacuationCenter->latitude,
                    'longitude' => $evacuationCenter->longitude
                ]
            ];

            // Send to all users
            $pushNotification->sendToAllUsers();

            \Log::info("Sent evacuation center notification for: {$evacuationCenter->name}");

        } catch (\Exception $e) {
            \Log::error("Failed to send evacuation center notification: " . $e->getMessage());
        }
    }
}
