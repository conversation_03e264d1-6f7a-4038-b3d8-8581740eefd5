<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Evacuation;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Carbon\Carbon;

class OfflineDataController extends Controller
{
    /**
     * Get all evacuation centers for offline sync
     */
    public function getEvacuationCenters(Request $request): JsonResponse
    {
        try {
            $query = Evacuation::where('status', 'Active');

            // Optional filtering by disaster type
            if ($request->has('disaster_type') && $request->disaster_type !== 'all') {
                $query->where('disaster_type', $request->disaster_type);
            }

            // Optional filtering by location (within radius)
            if ($request->has('lat') && $request->has('lng') && $request->has('radius')) {
                $lat = $request->lat;
                $lng = $request->lng;
                $radius = $request->radius; // in kilometers

                // Using Haversine formula for distance calculation
                $query->selectRaw("
                    *,
                    (6371 * acos(
                        cos(radians(?)) * 
                        cos(radians(latitude)) * 
                        cos(radians(longitude) - radians(?)) + 
                        sin(radians(?)) * 
                        sin(radians(latitude))
                    )) AS distance
                ", [$lat, $lng, $lat])
                ->having('distance', '<=', $radius)
                ->orderBy('distance');
            }

            $centers = $query->get()->map(function($center) {
                return [
                    'id' => $center->id,
                    'name' => $center->name,
                    'address' => "{$center->street_name}, {$center->barangay}, {$center->city}, {$center->province}",
                    'latitude' => (float) $center->latitude,
                    'longitude' => (float) $center->longitude,
                    'capacity' => $center->capacity,
                    'status' => $center->status,
                    'disaster_type' => $center->disaster_type,
                    'contact' => $center->contact,
                    'image_url' => $center->image_url,
                    'last_updated' => $center->updated_at->toISOString()
                ];
            });

            return response()->json([
                'success' => true,
                'data' => $centers,
                'count' => $centers->count(),
                'sync_timestamp' => now()->toISOString(),
                'message' => 'Evacuation centers retrieved successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve evacuation centers',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get evacuation centers by disaster type
     */
    public function getEvacuationCentersByType(Request $request, string $disasterType): JsonResponse
    {
        try {
            $validTypes = ['Typhoon', 'Flood', 'Earthquake'];
            
            if (!in_array($disasterType, $validTypes)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Invalid disaster type. Valid types: ' . implode(', ', $validTypes)
                ], 400);
            }

            $centers = Evacuation::where('status', 'Active')
                ->where('disaster_type', $disasterType)
                ->get()
                ->map(function($center) {
                    return [
                        'id' => $center->id,
                        'name' => $center->name,
                        'address' => "{$center->street_name}, {$center->barangay}, {$center->city}, {$center->province}",
                        'latitude' => (float) $center->latitude,
                        'longitude' => (float) $center->longitude,
                        'capacity' => $center->capacity,
                        'status' => $center->status,
                        'disaster_type' => $center->disaster_type,
                        'contact' => $center->contact,
                        'image_url' => $center->image_url,
                        'last_updated' => $center->updated_at->toISOString()
                    ];
                });

            return response()->json([
                'success' => true,
                'data' => $centers,
                'disaster_type' => $disasterType,
                'count' => $centers->count(),
                'sync_timestamp' => now()->toISOString()
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve evacuation centers',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get nearest evacuation centers
     */
    public function getNearestCenters(Request $request): JsonResponse
    {
        $request->validate([
            'lat' => 'required|numeric|between:-90,90',
            'lng' => 'required|numeric|between:-180,180',
            'limit' => 'integer|min:1|max:20',
            'disaster_type' => 'string|in:Typhoon,Flood,Earthquake'
        ]);

        try {
            $lat = $request->lat;
            $lng = $request->lng;
            $limit = $request->limit ?? 5;
            $disasterType = $request->disaster_type;

            $query = Evacuation::where('status', 'Active');

            if ($disasterType) {
                $query->where('disaster_type', $disasterType);
            }

            $centers = $query->selectRaw("
                *,
                (6371 * acos(
                    cos(radians(?)) * 
                    cos(radians(latitude)) * 
                    cos(radians(longitude) - radians(?)) + 
                    sin(radians(?)) * 
                    sin(radians(latitude))
                )) AS distance
            ", [$lat, $lng, $lat])
            ->orderBy('distance')
            ->limit($limit)
            ->get()
            ->map(function($center) {
                return [
                    'id' => $center->id,
                    'name' => $center->name,
                    'address' => "{$center->street_name}, {$center->barangay}, {$center->city}, {$center->province}",
                    'latitude' => (float) $center->latitude,
                    'longitude' => (float) $center->longitude,
                    'capacity' => $center->capacity,
                    'status' => $center->status,
                    'disaster_type' => $center->disaster_type,
                    'contact' => $center->contact,
                    'image_url' => $center->image_url,
                    'distance' => round($center->distance, 2), // Distance in kilometers
                    'last_updated' => $center->updated_at->toISOString()
                ];
            });

            return response()->json([
                'success' => true,
                'data' => $centers,
                'user_location' => [
                    'lat' => $lat,
                    'lng' => $lng
                ],
                'count' => $centers->count(),
                'sync_timestamp' => now()->toISOString()
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve nearest centers',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get sync status and metadata
     */
    public function getSyncStatus(): JsonResponse
    {
        try {
            $totalCenters = Evacuation::count();
            $activeCenters = Evacuation::where('status', 'Active')->count();
            $inactiveCenters = Evacuation::where('status', 'Inactive')->count();

            $centersByType = Evacuation::where('status', 'Active')
                ->selectRaw('disaster_type, COUNT(*) as count')
                ->groupBy('disaster_type')
                ->pluck('count', 'disaster_type');

            $lastUpdated = Evacuation::max('updated_at');

            return response()->json([
                'success' => true,
                'data' => [
                    'total_centers' => $totalCenters,
                    'active_centers' => $activeCenters,
                    'inactive_centers' => $inactiveCenters,
                    'centers_by_type' => $centersByType,
                    'last_updated' => $lastUpdated ? Carbon::parse($lastUpdated)->toISOString() : null,
                    'server_time' => now()->toISOString()
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve sync status',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Check for data updates since last sync
     */
    public function checkUpdates(Request $request): JsonResponse
    {
        $request->validate([
            'last_sync' => 'required|date'
        ]);

        try {
            $lastSync = Carbon::parse($request->last_sync);
            
            $updatedCenters = Evacuation::where('updated_at', '>', $lastSync)
                ->where('status', 'Active')
                ->count();

            $hasUpdates = $updatedCenters > 0;

            return response()->json([
                'success' => true,
                'has_updates' => $hasUpdates,
                'updated_centers_count' => $updatedCenters,
                'last_sync' => $lastSync->toISOString(),
                'server_time' => now()->toISOString(),
                'message' => $hasUpdates ? 'Updates available' : 'Data is up to date'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to check for updates',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
