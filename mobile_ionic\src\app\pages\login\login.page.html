

<ion-content class="ion-padding">
  <div class="login-container">
    
      

      <ion-card-content>
        <div class="login-wrapper">
          <img src="assets/ALERTO.png" alt="App Logo" class="login-logo" />
          <h1 class="login-title">Log In Here!</h1>
          <p></p>
          <form (ngSubmit)="onLogin()" class="login-form">
            <ion-item>
              <ion-label position="floating">Email:</ion-label>
              <ion-input type="email" [(ngModel)]="credentials.email" name="email" required></ion-input>
            </ion-item>
            <ion-item>
              <ion-label position="floating">Password:</ion-label>
              <ion-input type="password" [(ngModel)]="credentials.password" name="password" required></ion-input>
            </ion-item>
            <br><br>
            <ion-button expand="block" type="submit" class="login-btn">Log In</ion-button>
          </form>
          <div class="login-link">
            Don't have an account?
            <a (click)="goToRegister()"><strong><u>Sign Up</u></strong></a>
          </div>
        </div>
      </ion-card-content>
    
  </div>
</ion-content> 