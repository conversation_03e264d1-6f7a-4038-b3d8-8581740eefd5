@extends('layout.app')

@section('title','View Evacuation Center')

@section('content')
<div class="container mx-auto p-6">
  <div class="flex justify-end mb-4">
    <a href="{{ request()->query('from') === 'centers-list' ? route('components.evacuation_management.centers-list', ['type' => request()->query('type')]) : route('components.evacuation_management.evacuation-dashboard') }}" 
       class="text-gray-500 hover:text-red-600 text-4xl font-bold" aria-label="Close">&times;</a>
  </div>
  <div id="mapView" class="overflow-hidden h-96 w-full shadow relative">
    <div id="mapLoading" class="absolute inset-0 bg-gray-100 flex items-center justify-center">
      <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
    </div>
  </div>
  <!-- Info Modal -->
  <div id="infoModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 z-[9999] flex items-center justify-center hidden" role="dialog" aria-labelledby="modalName" aria-modal="true">
      <div class="bg-white p-6 rounded-lg shadow-lg w-96 relative max-h-[90vh] overflow-y-auto">
          <button onclick="closeModal()" class="absolute top-2 right-2 text-gray-500 hover:text-red-600 text-xl font-bold" aria-label="Close modal">&times;</button>
          <h3 id="modalName" class="text-xl font-bold mb-4"></h3>
          <div class="space-y-2">
            <p id="modalAddress" class="mb-2 flex items-start">
              <svg class="w-5 h-5 text-gray-500 mr-2 mt-1 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"/>
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"/>
              </svg>
              <span></span>
            </p>
            <p id="modalCapacity" class="mb-2 flex items-center">
              <svg class="w-5 h-5 text-gray-500 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"/>
              </svg>
              <span></span>
            </p>
            <p id="modalContact" class="mb-2 flex items-center">
              <svg class="w-5 h-5 text-gray-500 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"/>
              </svg>
              <span></span>
            </p>
            <p id="modalStatus" class="mb-2 flex items-center">
              <svg class="w-5 h-5 text-gray-500 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
              </svg>
              <span></span>
            </p>
            <p id="modalType" class="mb-2 flex items-center">
              <svg class="w-5 h-5 text-gray-500 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"/>
              </svg>
              <span></span>
            </p>
          </div>
      </div>
  </div>
</div>
@endsection

@section('scripts')
<script>
document.addEventListener('DOMContentLoaded', () => {
    try {
        const lat = {{ $evacuationCenter->latitude }};
        const lng = {{ $evacuationCenter->longitude }};
        const map = L.map('mapView').setView([lat, lng], 15);
        
        L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
            attribution: '&copy; OpenStreetMap contributors'
        }).addTo(map);

        // Create custom icon based on disaster type
        const disasterType = "{{ $evacuationCenter->disaster_type }}";
        let iconUrl;
        switch(disasterType) {
            case 'Flood':
                iconUrl = 'https://raw.githubusercontent.com/pointhi/leaflet-color-markers/master/img/marker-icon-green.png';
                break;
            case 'Typhoon':
                iconUrl = 'https://raw.githubusercontent.com/pointhi/leaflet-color-markers/master/img/marker-icon-blue.png';
                break;
            case 'Earthquake':
                iconUrl = 'https://raw.githubusercontent.com/pointhi/leaflet-color-markers/master/img/marker-icon-orange.png';
                break;
            default:
                iconUrl = 'https://raw.githubusercontent.com/pointhi/leaflet-color-markers/master/img/marker-icon-grey.png';
        }

        const customIcon = L.icon({
            iconUrl: iconUrl,
            shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/0.7.7/images/marker-shadow.png',
            iconSize: [25, 41],
            iconAnchor: [12, 41],
            popupAnchor: [1, -34],
            shadowSize: [41, 41]
        });

        const marker = L.marker([lat, lng], {icon: customIcon}).addTo(map);
        
        const info = {
            name: "{{ $evacuationCenter->name }}",
            address: {
                street: "{{ $evacuationCenter->street_name }}",
                barangay: "{{ $evacuationCenter->barangay }}",
                city: "{{ $evacuationCenter->city }}",
                province: "{{ $evacuationCenter->province }}",
                region: "{{ $evacuationCenter->region }}",
                postal: "{{ $evacuationCenter->postal_code }}",
                country: "{{ $evacuationCenter->country }}"
            },
            capacity: "{{ $evacuationCenter->capacity }}",
            contact: "{{ $evacuationCenter->contact }}",
            status: "{{ $evacuationCenter->status }}",
            type: disasterType
        };

        marker.on('click', () => {
            document.getElementById('modalName').textContent = info.name;
            
            // Format address properly
            const addressParts = [
                info.address.street,
                info.address.barangay,
                info.address.city,
                info.address.province,
                info.address.region,
                info.address.postal,
                info.address.country
            ].filter(part => part && part.trim() !== '');
            
            document.getElementById('modalAddress').querySelector('span').textContent = addressParts.join(', ');
            document.getElementById('modalCapacity').querySelector('span').textContent = `Capacity: ${info.capacity}`;
            document.getElementById('modalContact').querySelector('span').textContent = `Contact: ${info.contact}`;
            
            // Add color to status
            const statusSpan = document.createElement('span');
            statusSpan.textContent = info.status;
            statusSpan.className = `ml-1 px-2 py-1 rounded-full text-sm font-medium ${
                info.status === 'Active' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
            }`;
            document.getElementById('modalStatus').querySelector('span').innerHTML = 'Status: ';
            document.getElementById('modalStatus').querySelector('span').appendChild(statusSpan);
            
            // Add color to disaster type
            const typeSpan = document.createElement('span');
            typeSpan.textContent = info.type;
            typeSpan.className = `ml-1 px-2 py-1 rounded-full text-sm font-medium ${
                info.type === 'Flood' ? 'bg-green-100 text-green-800' :
                info.type === 'Typhoon' ? 'bg-blue-100 text-blue-800' :
                info.type === 'Earthquake' ? 'bg-orange-100 text-orange-800' :
                'bg-gray-100 text-gray-800'
            }`;
            document.getElementById('modalType').querySelector('span').innerHTML = 'Disaster Type: ';
            document.getElementById('modalType').querySelector('span').appendChild(typeSpan);
            
            document.getElementById('infoModal').classList.remove('hidden');
        });

        // Hide loading spinner when map is ready
        document.getElementById('mapLoading').style.display = 'none';

    } catch (error) {
        console.error('Error initializing map:', error);
        document.getElementById('mapView').innerHTML = '<div class="p-4 text-red-500">Error loading map. Please try again later.</div>';
    }
});

window.closeModal = function() { 
    document.getElementById('infoModal').classList.add('hidden');
};
</script>
@endsection
