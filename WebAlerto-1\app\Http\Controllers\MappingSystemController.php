<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Evacuation; // Changed from EvacuationCenter to Evacuation

class MappingSystemController extends Controller
{
    public function index()
    {
        $centers = Evacuation::select([
            'id', 'name', 'street_name', 'barangay', 'city', 'province',
            'latitude', 'longitude', 'capacity', 'status', 'disaster_type',
            'contact'
        ])->get()
        ->map(function($center) {
            return [
                'id' => $center->id,
                'name' => $center->name,
                'address' => "{$center->street_name}, {$center->barangay}, {$center->city}, {$center->province}",
                'latitude' => $center->latitude,
                'longitude' => $center->longitude,
                'capacity' => $center->capacity,
                'status' => $center->status,
                'disaster_type' => $center->disaster_type,
                'contact' => $center->contact
            ];
        });
        
        return view('components.map', compact('centers'));
    }

    public function store(Request $request)
    {
        // Validate the incoming request data
        $validatedData = $request->validate([
            'name' => 'required|string|max:255',
            'address' => 'required|string|max:255',
            'latitude' => 'required|numeric|between:-90,90',
            'longitude' => 'required|numeric|between:-180,180',
            'capacity' => 'required|integer|min:1',
            'status' => 'required|string|in:Active,Inactive',
            'image_url' => 'nullable|string|max:255'
        ]);

        // Create new evacuation center
        $center = Evacuation::create($validatedData);

        // Return response with success message and center data
        return response()->json([
            'success' => true,
            'message' => 'Evacuation center added successfully',
            'center' => $center
        ], 201);
    }

    public function storeBatch()
    {
        $centers = [
            [
                'name' => 'Mabolo Elementary School',
                'address' => 'Mabolo, Cebu City',
                'latitude' => 10.3200,
                'longitude' => 123.9000,
                'capacity' => 500,
                'status' => 'Active',
                'image_url' => 'https://images.unsplash.com/photo-1523050854058-8df90110c9f1?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=60'
            ],
            [
                'name' => 'Mabolo National High School',
                'address' => 'Mabolo, Cebu City',
                'latitude' => 10.3180,
                'longitude' => 123.9020,
                'capacity' => 800,
                'status' => 'Active',
                'image_url' => 'https://blogger.googleusercontent.com/img/b/R29vZ2xl/AVvXsEjB-jSVIm2eBIw0tjMMVnuHYl-Bm2QSeLHCdZWjRVAoNMPgqP0rc3Ht_6yO66XrT0c0yPsVzjwox612YiOQR00IzyljV6UVmCVpHnSmnbPVCvH8dC_RbmpmDAmxMlpMOwK3H4Y5npMGPspu9e4JnSPZx6iNUIxfCuBJSO4SkqLHDpgkBHyb1TdZPcmunpE/w1200-h630-p-k-no-nu/urot%20(1).jpg'
            ],
            [
                'name' => 'Mabolo Barangay Hall',
                'address' => 'Mabolo, Cebu City',
                'latitude' => 10.3220,
                'longitude' => 123.8980,
                'capacity' => 300,
                'status' => 'Active',
                'image_url' => 'https://images.unsplash.com/photo-1523050854058-8df90110c9f1?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=60'
            ],
            [
                'name' => 'Mabolo Sports Complex',
                'address' => 'Mabolo, Cebu City',
                'latitude' => 10.3210,
                'longitude' => 123.9010,
                'capacity' => 1000,
                'status' => 'Active',
                'image_url' => 'https://images.unsplash.com/photo-1523050854058-8df90110c9f1?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=60'
            ],
            [
                'name' => 'Mabolo Open Field',
                'address' => 'Mabolo, Cebu City',
                'latitude' => 10.3190,
                'longitude' => 123.8990,
                'capacity' => 2000,
                'status' => 'Active',
                'image_url' => 'https://images.unsplash.com/photo-1523050854058-8df90110c9f1?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=60'
            ],
            [
                'name' => 'Mabolo Community Center',
                'address' => 'Mabolo, Cebu City',
                'latitude' => 10.3230,
                'longitude' => 123.9030,
                'capacity' => 600,
                'status' => 'Active',
                'image_url' => 'https://images.unsplash.com/photo-1523050854058-8df90110c9f1?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=60'
            ]
        ];

        foreach ($centers as $centerData) {
            // Check if center already exists
            $exists = Evacuation::where('name', $centerData['name'])
                ->where('address', $centerData['address'])
                ->exists();
            
            if (!$exists) {
                Evacuation::create($centerData);
            }
        }

        return response()->json([
            'success' => true,
            'message' => 'Evacuation centers added successfully'
        ]);
    }

    public function destroy($id)
    {
        try {
            $center = Evacuation::findOrFail($id);
            $center->delete();

            return response()->json([
                'success' => true,
                'message' => 'Evacuation center deleted successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error deleting evacuation center'
            ], 500);
        }
    }

    public function search(Request $request)
    {
        $center = Evacuation::where('name', 'LIKE', '%San Roque Gym%')->first();
        
        if ($center) {
            return response()->json([
                'success' => true,
                'center' => $center
            ]);
        }
        
        return response()->json([
            'success' => false,
            'message' => 'Center not found'
        ]);
    }
}
