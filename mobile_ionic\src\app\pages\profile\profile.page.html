

<ion-content>
  <div class="profile-section ion-padding" style=" color: rgb(255, 255, 255); background-color: rgb(3, 178, 221); height: 200px;">

    <div class="profile-info" style="margin-top: 115px; margin-right: 260px;">
      <h2>Hi, {{ userData.full_name }}</h2>
      <p style="color:black">{{ userData.email }}</p>
    </div>
  </div>

  <ion-list lines="full" style="margin-top: 50px;">
    <ion-item button (click)="openAccountInfoModal()" style="padding-top: 10px;">
      <img src="assets/setting (1).png" style="width:28px; height:28px; display:block; margin:auto;" slot="start" />
      <ion-label style="padding-left: 15px; font-size: 17px;">Account Information</ion-label>
    </ion-item>

    <ion-item button (click)="openGuideModal()" style="padding-top: 10px;">
      <img src="assets/info.png" style="width:28px; height:28px; display:block; margin:auto;" slot="start" />
      <ion-label style="padding-left: 15px; font-size: 17px;">Reference Guide for Map Symbols</ion-label>
    </ion-item>

    <ion-item button (click)="openEmergencyContactsModal()" style="padding-top: 10px;">
      <img src="assets/medical-call.png" style="width:28px; height:28px; display:block; margin:auto;" slot="start" />
      <ion-label style="padding-left: 15px; font-size: 17px;">Emergency Contacts</ion-label>
    </ion-item>

    <ion-item button (click)="openSafetyTipsModal()" style="padding-top: 10px;">
      <img src="assets/first-aid-box.png" style="width:28px; height:28px; display:block; margin:auto;" slot="start" />
      <ion-label style="padding-left: 15px; font-size: 17px;">Safety Tips</ion-label>
    </ion-item>

    <ion-item button (click)="openPrivacyModal()" style="padding-top: 10px;">
      <img src="assets/shield.png" style="width:28px; height:28px; display:block; margin:auto;" slot="start" />
      <ion-label style="padding-left: 15px;   font-size: 17px;">Privacy Policy</ion-label>
    </ion-item>

    <ion-item button (click)="openTermsModal()" style="padding-top: 10px;">
      <img src="assets/terms-and-conditions.png" style="width:28px; height:28px; display:block; margin:auto; " slot="start" />
      <ion-label style="padding-left: 15px; font-size: 17px;">Terms and Condition</ion-label>
    </ion-item>

    <ion-item button (click)="goToSettings()" style="padding-top: 10px;">
      <ion-icon name="settings-outline" style="width:28px; height:28px; display:block; margin:auto; color: #3880ff;" slot="start"></ion-icon>
      <ion-label style="padding-left: 15px; font-size: 17px;">Notification Settings</ion-label>
    </ion-item>

  </ion-list>
</ion-content>