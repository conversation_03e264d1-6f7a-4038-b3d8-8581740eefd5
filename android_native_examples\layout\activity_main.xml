<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:padding="16dp"
    tools:context=".MainActivity">

    <!-- Header -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:layout_marginBottom="24dp">

        <ImageView
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:src="@drawable/ic_alert"
            android:layout_marginEnd="12dp" />

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="WebAlerto"
                android:textSize="24sp"
                android:textStyle="bold"
                android:textColor="@color/primary_color" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Disaster Alert System"
                android:textSize="14sp"
                android:textColor="@color/secondary_text" />

        </LinearLayout>

    </LinearLayout>

    <!-- Action Buttons -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:layout_marginBottom="24dp">

        <Button
            android:id="@+id/btn_test_notification"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Send Test Notification"
            android:background="@drawable/button_primary"
            android:textColor="@android:color/white"
            android:layout_marginBottom="8dp"
            android:padding="12dp" />

        <Button
            android:id="@+id/btn_view_evacuation_centers"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="View Evacuation Centers"
            android:background="@drawable/button_secondary"
            android:textColor="@color/primary_color"
            android:layout_marginBottom="8dp"
            android:padding="12dp" />

        <Button
            android:id="@+id/btn_refresh_token"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Refresh FCM Token"
            android:background="@drawable/button_outline"
            android:textColor="@color/primary_color"
            android:padding="12dp" />

    </LinearLayout>

    <!-- Disaster Categories -->
    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Disaster Categories"
        android:textSize="18sp"
        android:textStyle="bold"
        android:layout_marginBottom="12dp" />

    <GridLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:columnCount="2"
        android:rowCount="2"
        android:layout_marginBottom="24dp">

        <androidx.cardview.widget.CardView
            android:layout_width="0dp"
            android:layout_height="120dp"
            android:layout_columnWeight="1"
            android:layout_margin="4dp"
            app:cardCornerRadius="8dp"
            app:cardElevation="4dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical"
                android:gravity="center"
                android:padding="16dp"
                android:background="@color/earthquake_bg">

                <ImageView
                    android:layout_width="48dp"
                    android:layout_height="48dp"
                    android:src="@drawable/ic_earthquake"
                    android:layout_marginBottom="8dp" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Earthquake"
                    android:textStyle="bold"
                    android:textColor="@android:color/white" />

            </LinearLayout>

        </androidx.cardview.widget.CardView>

        <androidx.cardview.widget.CardView
            android:layout_width="0dp"
            android:layout_height="120dp"
            android:layout_columnWeight="1"
            android:layout_margin="4dp"
            app:cardCornerRadius="8dp"
            app:cardElevation="4dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical"
                android:gravity="center"
                android:padding="16dp"
                android:background="@color/typhoon_bg">

                <ImageView
                    android:layout_width="48dp"
                    android:layout_height="48dp"
                    android:src="@drawable/ic_typhoon"
                    android:layout_marginBottom="8dp" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Typhoon"
                    android:textStyle="bold"
                    android:textColor="@android:color/white" />

            </LinearLayout>

        </androidx.cardview.widget.CardView>

        <androidx.cardview.widget.CardView
            android:layout_width="0dp"
            android:layout_height="120dp"
            android:layout_columnWeight="1"
            android:layout_margin="4dp"
            app:cardCornerRadius="8dp"
            app:cardElevation="4dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical"
                android:gravity="center"
                android:padding="16dp"
                android:background="@color/flood_bg">

                <ImageView
                    android:layout_width="48dp"
                    android:layout_height="48dp"
                    android:src="@drawable/ic_flood"
                    android:layout_marginBottom="8dp" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Flood"
                    android:textStyle="bold"
                    android:textColor="@android:color/white" />

            </LinearLayout>

        </androidx.cardview.widget.CardView>

        <androidx.cardview.widget.CardView
            android:layout_width="0dp"
            android:layout_height="120dp"
            android:layout_columnWeight="1"
            android:layout_margin="4dp"
            app:cardCornerRadius="8dp"
            app:cardElevation="4dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical"
                android:gravity="center"
                android:padding="16dp"
                android:background="@color/fire_bg">

                <ImageView
                    android:layout_width="48dp"
                    android:layout_height="48dp"
                    android:src="@drawable/ic_fire"
                    android:layout_marginBottom="8dp" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Fire"
                    android:textStyle="bold"
                    android:textColor="@android:color/white" />

            </LinearLayout>

        </androidx.cardview.widget.CardView>

    </GridLayout>

    <!-- Recent Alerts -->
    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Recent Alerts"
        android:textSize="18sp"
        android:textStyle="bold"
        android:layout_marginBottom="12dp" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recycler_view_alerts"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        tools:listitem="@layout/item_disaster_alert" />

</LinearLayout>
