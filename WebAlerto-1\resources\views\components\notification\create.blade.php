@extends('layout.app')

@section('content')
<div class="container mx-auto px-4 py-8 max-w-4xl">
    <div class="flex justify-between items-center mb-8">
        <h1 class="text-2xl font-bold text-gray-800 flex items-center gap-2">
            <span class="material-icons text-blue-500">add_alert</span>
            Create Notification Alert
        </h1>
        <a href="{{ route('components.notification.index') }}" class="text-blue-600 hover:underline text-sm font-medium">
            ← Back to Notification History
        </a>
    </div>

    @if(session('success'))
        <div id="successMessage" class="bg-green-100 border border-green-300 text-green-800 px-4 py-3 rounded relative mb-6">
            <strong class="font-semibold">Success:</strong> {{ session('success') }}
        </div>
    @endif

    @if($errors->any())
        <div class="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 rounded mb-6">
            <ul class="list-disc list-inside space-y-1">
                @foreach($errors->all() as $error)
                    <li>{{ $error }}</li>
                @endforeach
            </ul>
        </div>
    @endif

    <form action="{{ route('components.notification.store') }}" method="POST" class="bg-white rounded-2xl shadow-lg p-8 border border-gray-200">
        @csrf
        <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
            <!-- Left Column -->
            <div>
                <h2 class="text-lg font-semibold text-blue-700 mb-4 flex items-center gap-2">
                    <span class="material-icons text-blue-500">edit</span>
                    Notification Details
                </h2>
                <div class="mb-6">
                    <label for="title" class="block text-sm font-semibold text-gray-700 mb-2">Alert Title</label>
                    <input
                        type="text"
                        name="title"
                        id="title"
                        value="{{ old('title') }}"
                        placeholder="Enter notification title"
                        class="w-full border border-gray-300 px-4 py-3 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition"
                        required>
                </div>

                <div class="mb-6">
                    <label for="message" class="block text-sm font-semibold text-gray-700 mb-2">Alert Description</label>
                    <textarea
                        name="message"
                        id="message"
                        rows="5"
                        placeholder="Type your alert message here..."
                        class="w-full border border-gray-300 px-4 py-3 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition resize-none"
                        required>{{ old('message') }}</textarea>
                </div>

                <div class="mb-6">
                    <label class="block text-sm font-semibold text-gray-700 mb-2">Priority Level</label>
                    <div class="flex space-x-6">
                        <label class="inline-flex items-center cursor-pointer">
                            <input type="radio" name="severity" value="high" class="form-radio h-5 w-5 text-red-600" {{ old('severity') === 'high' ? 'checked' : '' }}>
                            <span class="ml-2 flex items-center">
                                <span class="h-4 w-4 rounded-full bg-red-500 mr-2 border-2 border-red-300"></span>
                                <span class="font-semibold text-red-600">High</span>
                            </span>
                        </label>
                        <label class="inline-flex items-center cursor-pointer">
                            <input type="radio" name="severity" value="medium" class="form-radio h-5 w-5 text-yellow-500" {{ old('severity') === 'medium' ? 'checked' : '' }}>
                            <span class="ml-2 flex items-center">
                                <span class="h-4 w-4 rounded-full bg-yellow-400 mr-2 border-2 border-yellow-300"></span>
                                <span class="font-semibold text-yellow-600">Medium</span>
                            </span>
                        </label>
                        <label class="inline-flex items-center cursor-pointer">
                            <input type="radio" name="severity" value="low" class="form-radio h-5 w-5 text-green-500" {{ old('severity') === 'low' ? 'checked' : '' }}>
                            <span class="ml-2 flex items-center">
                                <span class="h-4 w-4 rounded-full bg-green-400 mr-2 border-2 border-green-300"></span>
                                <span class="font-semibold text-green-600">Low</span>
                            </span>
                        </label>
                    </div>
                </div>
            </div>

            <!-- Right Column -->
            <div>
                <h2 class="text-lg font-semibold text-blue-700 mb-4 flex items-center gap-2">
                    <span class="material-icons text-blue-500">category</span>
                    Alert Category
                </h2>
                <div class="mb-6">
                    <label for="category" class="block text-sm font-semibold text-gray-700 mb-2">Category</label>
                    <select
                        name="category"
                        id="category"
                        class="w-full border border-gray-300 px-4 py-3 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition"
                        required>
                        <option value="" disabled {{ old('category') ? '' : 'selected' }}>Select a category</option>
                        <option value="Flood" {{ old('category') === 'Flood' ? 'selected' : '' }}>🌊 Flood</option>
                        <option value="Fire" {{ old('category') === 'Fire' ? 'selected' : '' }}>🔥 Fire</option>
                        <option value="Earthquake" {{ old('category') === 'Earthquake' ? 'selected' : '' }}>🌍 Earthquake</option>
                    </select>
                </div>
            </div>
        </div>

        <!-- Submit Button -->
        <div class="mt-8 pt-6 border-t border-gray-200 flex justify-end">
            <div class="flex space-x-3">
                <a href="{{ route('components.notification.index') }}" class="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50">
                    Cancel
                </a>
                <button
                    type="submit"
                    class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 font-semibold rounded-lg shadow-md transition">
                    🚀 Send Notification
                </button>
            </div>
        </div>
    </form>
</div>

<!-- Material Icons CDN (add this to your main layout if not already present) -->
<link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">

@endsection