import{a as be}from"./chunk-DGJ3H4GJ.js";import{c as N,e as Te,f as J,i as Me,j as re,k as Pe}from"./chunk-4BFL6ZL3.js";import{a as ae}from"./chunk-JE3FXFC3.js";import"./chunk-HU6UQ5WL.js";import{a as ge}from"./chunk-WMEG6PAA.js";import{f as Oe,g as Le}from"./chunk-VD4VFVTQ.js";import{a as se}from"./chunk-HC6MZPB3.js";import{i as xe,j as Se,l as ve,m as Ae,n as ie,q as De,r as K,s as Ee,u as Ce,v as Z}from"./chunk-UKIOCGZG.js";import{a as u}from"./chunk-RMJ7PCZJ.js";import{a as ye,b as ke,c as we}from"./chunk-K54AU7WQ.js";import{c as Be}from"./chunk-VBU7NMPV.js";import"./chunk-JIZJ6CAM.js";import{a as H}from"./chunk-MCRJI3T3.js";import{b as G}from"./chunk-L5T6STQ3.js";import{b as Ye,d as de,f as q,g as Ie,j as Re,k as I}from"./chunk-3EJRMEWO.js";import{c as me,d as ue,h as Y,i as W,m as $}from"./chunk-GNOVVPTF.js";import{a as oe,e as U}from"./chunk-BAKMWPBW.js";import{a as ne}from"./chunk-5OMUW5VI.js";import"./chunk-OBXDPQ3V.js";import{h as P}from"./chunk-LNJ3S2LQ.js";var z=function(e){return e.Dark="DARK",e.Light="LIGHT",e.Default="DEFAULT",e}(z||{}),he={getEngine(){let e=ge();if(e!=null&&e.isPluginAvailable("StatusBar"))return e.Plugins.StatusBar},setStyle(e){let t=this.getEngine();t&&t.setStyle(e)},getStyle:function(){return P(this,null,function*(){let e=this.getEngine();if(!e)return z.Default;let{style:t}=yield e.getInfo();return t})}},le=(e,t)=>{if(t===1)return 0;let o=1/(1-t),r=-(t*o);return e*o+r},Ne=()=>{!H||H.innerWidth>=768||he.setStyle({style:z.Dark})},ce=(e=z.Default)=>{!H||H.innerWidth>=768||he.setStyle({style:e})},He=(e,t)=>P(void 0,null,function*(){typeof e.canDismiss!="function"||!(yield e.canDismiss(void 0,K))||(t.isRunning()?t.onFinish(()=>{e.dismiss(void 0,"handler")},{oneTimeCallback:!0}):e.dismiss(void 0,"handler"))}),pe=e=>.00255275*2.71828**(-14.9619*e)-1.00255*2.71828**(-.0380968*e)+1,Q={MIN_PRESENTING_SCALE:.915},je=(e,t,o,r)=>{let n=e.offsetHeight,a=!1,i=!1,d=null,l=null,f=.2,m=!0,b=0,g=()=>d&&N(d)?d.scrollY:!0,C=ne({el:e,gestureName:"modalSwipeToClose",gesturePriority:Ee,direction:"y",threshold:10,canStart:x=>{let v=x.event.target;return v===null||!v.closest?!0:(d=J(v),d?(N(d)?l=Y(d).querySelector(".inner-scroll"):l=d,!!!d.querySelector("ion-refresher")&&l.scrollTop===0):v.closest("ion-footer")===null)},onStart:x=>{let{deltaY:v}=x;m=g(),i=e.canDismiss!==void 0&&e.canDismiss!==!0,v>0&&d&&re(d),t.progressStart(!0,a?1:0)},onMove:x=>{let{deltaY:v}=x;v>0&&d&&re(d);let T=x.deltaY/n,R=T>=0&&i,A=R?f:.9999,O=R?pe(T/A):T,y=$(1e-4,O,A);t.progressStep(y),y>=.5&&b<.5?ce(o):y<.5&&b>=.5&&Ne(),b=y},onEnd:x=>{let v=x.velocityY,T=x.deltaY/n,R=T>=0&&i,A=R?f:.9999,O=R?pe(T/A):T,y=$(1e-4,O,A),j=(x.deltaY+v*1e3)/n,L=!R&&j>=.5,F=L?-.001:.001;L?(t.easing("cubic-bezier(0.32, 0.72, 0, 1)"),F+=se([0,0],[.32,.72],[0,1],[1,1],y)[0]):(t.easing("cubic-bezier(1, 0, 0.68, 0.28)"),F+=se([0,0],[1,0],[.68,.28],[1,1],y)[0]);let X=_e(L?T*n:(1-y)*n,v);a=L,C.enable(!1),d&&Pe(d,m),t.onFinish(()=>{L||C.enable(!0)}).progressEnd(L?1:0,F,X),R&&y>A/4?He(e,t):L&&r()}});return C},_e=(e,t)=>$(400,e/Math.abs(t*1.1),500),We=e=>{let{currentBreakpoint:t,backdropBreakpoint:o,expandToScroll:r}=e,s=o===void 0||o<t,n=s?`calc(var(--backdrop-opacity) * ${t})`:"0",a=u("backdropAnimation").fromTo("opacity",0,n);s&&a.beforeStyles({"pointer-events":"none"}).afterClearStyles(["pointer-events"]);let i=u("wrapperAnimation").keyframes([{offset:0,opacity:1,transform:"translateY(100%)"},{offset:1,opacity:1,transform:`translateY(${100-t*100}%)`}]),d=r?void 0:u("contentAnimation").keyframes([{offset:0,opacity:1,maxHeight:`${(1-t)*100}%`},{offset:1,opacity:1,maxHeight:`${t*100}%`}]);return{wrapperAnimation:i,backdropAnimation:a,contentAnimation:d}},$e=e=>{let{currentBreakpoint:t,backdropBreakpoint:o}=e,r=`calc(var(--backdrop-opacity) * ${le(t,o)})`,s=[{offset:0,opacity:r},{offset:1,opacity:0}],n=[{offset:0,opacity:r},{offset:o,opacity:0},{offset:1,opacity:0}],a=u("backdropAnimation").keyframes(o!==0?n:s);return{wrapperAnimation:u("wrapperAnimation").keyframes([{offset:0,opacity:1,transform:`translateY(${100-t*100}%)`},{offset:1,opacity:1,transform:"translateY(100%)"}]),backdropAnimation:a}},Ve=()=>{let e=u().fromTo("opacity",.01,"var(--backdrop-opacity)").beforeStyles({"pointer-events":"none"}).afterClearStyles(["pointer-events"]),t=u().fromTo("transform","translateY(100vh)","translateY(0vh)");return{backdropAnimation:e,wrapperAnimation:t,contentAnimation:void 0}},qe=(e,t)=>{let{presentingEl:o,currentBreakpoint:r,expandToScroll:s}=t,n=Y(e),{wrapperAnimation:a,backdropAnimation:i,contentAnimation:d}=r!==void 0?We(t):Ve();i.addElement(n.querySelector("ion-backdrop")),a.addElement(n.querySelectorAll(".modal-wrapper, .modal-shadow")).beforeStyles({opacity:1}),!s&&(d==null||d.addElement(e.querySelector(".ion-page")));let l=u("entering-base").addElement(e).easing("cubic-bezier(0.32,0.72,0,1)").duration(500).addAnimation([a]).beforeAddWrite(()=>{if(s)return;let f=e.querySelector("ion-footer"),m=e.shadowRoot.querySelector("ion-footer");if(f&&!m){let b=f.clientHeight,g=f.cloneNode(!0);e.shadowRoot.appendChild(g),f.style.setProperty("display","none"),f.setAttribute("aria-hidden","true"),e.querySelector(".ion-page").style.setProperty("padding-bottom",`${b}px`)}});if(d&&l.addAnimation(d),o){let f=window.innerWidth<768,m=o.tagName==="ION-MODAL"&&o.presentingElement!==void 0,b=Y(o),g=u().beforeStyles({transform:"translateY(0)","transform-origin":"top center",overflow:"hidden"}),w=document.body;if(f){let B=CSS.supports("width","max(0px, 1px)")?"max(30px, var(--ion-safe-area-top))":"30px",c=m?"-10px":B,S=Q.MIN_PRESENTING_SCALE,C=`translateY(${c}) scale(${S})`;g.afterStyles({transform:C}).beforeAddWrite(()=>w.style.setProperty("background-color","black")).addElement(o).keyframes([{offset:0,filter:"contrast(1)",transform:"translateY(0px) scale(1)",borderRadius:"0px"},{offset:1,filter:"contrast(0.85)",transform:C,borderRadius:"10px 10px 0 0"}]),l.addAnimation(g)}else if(l.addAnimation(i),!m)a.fromTo("opacity","0","1");else{let c=`translateY(-10px) scale(${m?Q.MIN_PRESENTING_SCALE:1})`;g.afterStyles({transform:c}).addElement(b.querySelector(".modal-wrapper")).keyframes([{offset:0,filter:"contrast(1)",transform:"translateY(0) scale(1)"},{offset:1,filter:"contrast(0.85)",transform:c}]);let S=u().afterStyles({transform:c}).addElement(b.querySelector(".modal-shadow")).keyframes([{offset:0,opacity:"1",transform:"translateY(0) scale(1)"},{offset:1,opacity:"0",transform:c}]);l.addAnimation([g,S])}}else l.addAnimation(i);return l},Ue=()=>{let e=u().fromTo("opacity","var(--backdrop-opacity)",0),t=u().fromTo("transform","translateY(0vh)","translateY(100vh)");return{backdropAnimation:e,wrapperAnimation:t}},Fe=(e,t,o=500)=>{let{presentingEl:r,currentBreakpoint:s,expandToScroll:n}=t,a=Y(e),{wrapperAnimation:i,backdropAnimation:d}=s!==void 0?$e(t):Ue();d.addElement(a.querySelector("ion-backdrop")),i.addElement(a.querySelectorAll(".modal-wrapper, .modal-shadow")).beforeStyles({opacity:1});let l=u("leaving-base").addElement(e).easing("cubic-bezier(0.32,0.72,0,1)").duration(o).addAnimation(i).beforeAddWrite(()=>{if(n)return;let f=e.querySelector("ion-footer");if(f){let m=e.shadowRoot.querySelector("ion-footer");f.style.removeProperty("display"),f.removeAttribute("aria-hidden"),m.style.setProperty("display","none"),m.setAttribute("aria-hidden","true"),e.querySelector(".ion-page").style.removeProperty("padding-bottom")}});if(r){let f=window.innerWidth<768,m=r.tagName==="ION-MODAL"&&r.presentingElement!==void 0,b=Y(r),g=u().beforeClearStyles(["transform"]).afterClearStyles(["transform"]).onFinish(B=>{if(B!==1)return;r.style.setProperty("overflow",""),Array.from(w.querySelectorAll("ion-modal:not(.overlay-hidden)")).filter(S=>S.presentingElement!==void 0).length<=1&&w.style.setProperty("background-color","")}),w=document.body;if(f){let B=CSS.supports("width","max(0px, 1px)")?"max(30px, var(--ion-safe-area-top))":"30px",c=m?"-10px":B,S=Q.MIN_PRESENTING_SCALE,C=`translateY(${c}) scale(${S})`;g.addElement(r).keyframes([{offset:0,filter:"contrast(0.85)",transform:C,borderRadius:"10px 10px 0 0"},{offset:1,filter:"contrast(1)",transform:"translateY(0px) scale(1)",borderRadius:"0px"}]),l.addAnimation(g)}else if(l.addAnimation(d),!m)i.fromTo("opacity","1","0");else{let c=`translateY(-10px) scale(${m?Q.MIN_PRESENTING_SCALE:1})`;g.addElement(b.querySelector(".modal-wrapper")).afterStyles({transform:"translate3d(0, 0, 0)"}).keyframes([{offset:0,filter:"contrast(0.85)",transform:c},{offset:1,filter:"contrast(1)",transform:"translateY(0) scale(1)"}]);let S=u().addElement(b.querySelector(".modal-shadow")).afterStyles({transform:"translateY(0) scale(1)"}).keyframes([{offset:0,opacity:"0",transform:c},{offset:1,opacity:"1",transform:"translateY(0) scale(1)"}]);l.addAnimation([g,S])}}else l.addAnimation(d);return l},Ze=()=>{let e=u().fromTo("opacity",.01,"var(--backdrop-opacity)").beforeStyles({"pointer-events":"none"}).afterClearStyles(["pointer-events"]),t=u().keyframes([{offset:0,opacity:.01,transform:"translateY(40px)"},{offset:1,opacity:1,transform:"translateY(0px)"}]);return{backdropAnimation:e,wrapperAnimation:t,contentAnimation:void 0}},Je=(e,t)=>{let{currentBreakpoint:o,expandToScroll:r}=t,s=Y(e),{wrapperAnimation:n,backdropAnimation:a,contentAnimation:i}=o!==void 0?We(t):Ze();a.addElement(s.querySelector("ion-backdrop")),n.addElement(s.querySelector(".modal-wrapper")),r&&(i==null||i.addElement(e.querySelector(".ion-page")));let d=u().addElement(e).easing("cubic-bezier(0.36,0.66,0.04,1)").duration(280).addAnimation([a,n]).beforeAddWrite(()=>{if(r)return;let l=e.querySelector("ion-footer"),f=e.shadowRoot.querySelector("ion-footer");if(l&&!f){let m=l.clientHeight,b=l.cloneNode(!0);e.shadowRoot.appendChild(b),l.style.setProperty("display","none"),l.setAttribute("aria-hidden","true"),e.querySelector(".ion-page").style.setProperty("padding-bottom",`${m}px`)}});return i&&d.addAnimation(i),d},Qe=()=>{let e=u().fromTo("opacity","var(--backdrop-opacity)",0),t=u().keyframes([{offset:0,opacity:.99,transform:"translateY(0px)"},{offset:1,opacity:0,transform:"translateY(40px)"}]);return{backdropAnimation:e,wrapperAnimation:t}},Xe=(e,t)=>{let{currentBreakpoint:o,expandToScroll:r}=t,s=Y(e),{wrapperAnimation:n,backdropAnimation:a}=o!==void 0?$e(t):Qe();return a.addElement(s.querySelector("ion-backdrop")),n.addElement(s.querySelector(".modal-wrapper")),u().easing("cubic-bezier(0.47,0,0.745,0.715)").duration(200).addAnimation([a,n]).beforeAddWrite(()=>{if(r)return;let d=e.querySelector("ion-footer");if(d){let l=e.shadowRoot.querySelector("ion-footer");d.style.removeProperty("display"),d.removeAttribute("aria-hidden"),l.style.setProperty("display","none"),l.setAttribute("aria-hidden","true"),e.querySelector(".ion-page").style.removeProperty("padding-bottom")}})},et=(e,t,o,r,s,n,a=[],i,d,l,f)=>{let m=[{offset:0,opacity:"var(--backdrop-opacity)"},{offset:1,opacity:.01}],b=[{offset:0,opacity:"var(--backdrop-opacity)"},{offset:1-s,opacity:0},{offset:1,opacity:0}],g={WRAPPER_KEYFRAMES:[{offset:0,transform:"translateY(0%)"},{offset:1,transform:"translateY(100%)"}],BACKDROP_KEYFRAMES:s!==0?b:m,CONTENT_KEYFRAMES:[{offset:0,maxHeight:"100%"},{offset:1,maxHeight:"0%"}]},w=e.querySelector("ion-content"),B=o.clientHeight,c=r,S=0,C=!1,x=null,v=.95,T=a[a.length-1],R=a[0],A=n.childAnimations.find(p=>p.id==="wrapperAnimation"),O=n.childAnimations.find(p=>p.id==="backdropAnimation"),y=n.childAnimations.find(p=>p.id==="contentAnimation"),j=()=>{e.style.setProperty("pointer-events","auto"),t.style.setProperty("pointer-events","auto"),e.classList.remove(Z)},L=()=>{e.style.setProperty("pointer-events","none"),t.style.setProperty("pointer-events","none"),e.classList.add(Z)},F=p=>{let h=e.querySelector("ion-footer");if(!h)return;let k=o.nextElementSibling,D=p==="original"?k:h,_=p==="original"?h:k;_.style.removeProperty("display"),_.removeAttribute("aria-hidden");let M=e.querySelector(".ion-page");if(p==="original")M.style.removeProperty("padding-bottom");else{let E=_.clientHeight;M.style.setProperty("padding-bottom",`${E}px`)}D.style.setProperty("display","none"),D.setAttribute("aria-hidden","true")};A&&O&&(A.keyframes([...g.WRAPPER_KEYFRAMES]),O.keyframes([...g.BACKDROP_KEYFRAMES]),y==null||y.keyframes([...g.CONTENT_KEYFRAMES]),n.progressStart(!0,1-c),c>s?j():L()),w&&c!==T&&i&&(w.scrollY=!1);let X=p=>{let h=J(p.event.target);if(c=d(),!i&&h)return(N(h)?Y(h).querySelector(".inner-scroll"):h).scrollTop===0;if(c===1&&h){let k=N(h)?Y(h).querySelector(".inner-scroll"):h;return!!!h.querySelector("ion-refresher")&&k.scrollTop===0}return!0},Ge=p=>{if(C=e.canDismiss!==void 0&&e.canDismiss!==!0&&R===0,!i){let h=J(p.event.target);x=h&&N(h)?Y(h).querySelector(".inner-scroll"):h}i||F("original"),p.deltaY>0&&w&&(w.scrollY=!1),W(()=>{e.focus()}),n.progressStart(!0,1-c)},Ke=p=>{if(!i&&p.deltaY<=0&&x)return;p.deltaY>0&&w&&(w.scrollY=!1);let h=1-c,k=a.length>1?1-a[1]:void 0,D=h+p.deltaY/B,_=k!==void 0&&D>=k&&C,M=_?v:.9999,E=_&&k!==void 0?k+pe((D-k)/(M-k)):D;S=$(1e-4,E,M),n.progressStep(S)},ze=p=>{if(!i&&p.deltaY<=0&&x&&x.scrollTop>0)return;let h=p.velocityY,k=(p.deltaY+h*350)/B,D=c-k,_=a.reduce((M,E)=>Math.abs(E-D)<Math.abs(M-D)?E:M);fe({breakpoint:_,breakpointOffset:S,canDismiss:C,animated:!0})},fe=p=>{let{breakpoint:h,canDismiss:k,breakpointOffset:D,animated:_}=p,M=k&&h===0,E=M?c:h,ee=E!==0;return c=0,A&&O&&(A.keyframes([{offset:0,transform:`translateY(${D*100}%)`},{offset:1,transform:`translateY(${(1-E)*100}%)`}]),O.keyframes([{offset:0,opacity:`calc(var(--backdrop-opacity) * ${le(1-D,s)})`},{offset:1,opacity:`calc(var(--backdrop-opacity) * ${le(E,s)})`}]),y&&y.keyframes([{offset:0,maxHeight:`${(1-D)*100}%`},{offset:1,maxHeight:`${E*100}%`}]),n.progressStep(0)),V.enable(!1),!i&&ee&&F("cloned"),M?He(e,n):ee||l(),w&&(E===a[a.length-1]||!i)&&(w.scrollY=!0),new Promise(te=>{n.onFinish(()=>{ee?A&&O?W(()=>{A.keyframes([...g.WRAPPER_KEYFRAMES]),O.keyframes([...g.BACKDROP_KEYFRAMES]),y==null||y.keyframes([...g.CONTENT_KEYFRAMES]),n.progressStart(!0,1-E),c=E,f(c),c>s?j():L(),V.enable(!0),te()}):(V.enable(!0),te()):te()},{oneTimeCallback:!0}).progressEnd(1,0,_?500:0)})},V=ne({el:o,gestureName:"modalSheet",gesturePriority:40,direction:"y",threshold:10,canStart:X,onStart:Ge,onMove:Ke,onEnd:ze});return{gesture:V,moveSheetToBreakpoint:fe}},tt=':host{--width:100%;--min-width:auto;--max-width:auto;--height:100%;--min-height:auto;--max-height:auto;--overflow:hidden;--border-radius:0;--border-width:0;--border-style:none;--border-color:transparent;--background:var(--ion-background-color, #fff);--box-shadow:none;--backdrop-opacity:0;left:0;right:0;top:0;bottom:0;display:-ms-flexbox;display:flex;position:absolute;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;outline:none;color:var(--ion-text-color, #000);contain:strict}.modal-wrapper,ion-backdrop{pointer-events:auto}:host(.overlay-hidden){display:none}.modal-wrapper,.modal-shadow{border-radius:var(--border-radius);width:var(--width);min-width:var(--min-width);max-width:var(--max-width);height:var(--height);min-height:var(--min-height);max-height:var(--max-height);border-width:var(--border-width);border-style:var(--border-style);border-color:var(--border-color);background:var(--background);-webkit-box-shadow:var(--box-shadow);box-shadow:var(--box-shadow);overflow:var(--overflow);z-index:10}.modal-shadow{position:absolute;background:transparent}@media only screen and (min-width: 768px) and (min-height: 600px){:host{--width:600px;--height:500px;--ion-safe-area-top:0px;--ion-safe-area-bottom:0px;--ion-safe-area-right:0px;--ion-safe-area-left:0px}}@media only screen and (min-width: 768px) and (min-height: 768px){:host{--width:600px;--height:600px}}.modal-handle{left:0px;right:0px;top:5px;border-radius:8px;-webkit-margin-start:auto;margin-inline-start:auto;-webkit-margin-end:auto;margin-inline-end:auto;position:absolute;width:36px;height:5px;-webkit-transform:translateZ(0);transform:translateZ(0);border:0;background:var(--ion-color-step-350, var(--ion-background-color-step-350, #c0c0be));cursor:pointer;z-index:11}.modal-handle::before{-webkit-padding-start:4px;padding-inline-start:4px;-webkit-padding-end:4px;padding-inline-end:4px;padding-top:4px;padding-bottom:4px;position:absolute;width:36px;height:5px;-webkit-transform:translate(-50%, -50%);transform:translate(-50%, -50%);content:""}:host(.modal-sheet){--height:calc(100% - (var(--ion-safe-area-top) + 10px))}:host(.modal-sheet) .modal-wrapper,:host(.modal-sheet) .modal-shadow{position:absolute;bottom:0}:host(.modal-sheet.modal-no-expand-scroll) ion-footer{position:absolute;bottom:0;width:var(--width)}:host{--backdrop-opacity:var(--ion-backdrop-opacity, 0.4)}:host(.modal-card),:host(.modal-sheet){--border-radius:10px}@media only screen and (min-width: 768px) and (min-height: 600px){:host{--border-radius:10px}}.modal-wrapper{-webkit-transform:translate3d(0,  100%,  0);transform:translate3d(0,  100%,  0)}@media screen and (max-width: 767px){@supports (width: max(0px, 1px)){:host(.modal-card){--height:calc(100% - max(30px, var(--ion-safe-area-top)) - 10px)}}@supports not (width: max(0px, 1px)){:host(.modal-card){--height:calc(100% - 40px)}}:host(.modal-card) .modal-wrapper{border-start-start-radius:var(--border-radius);border-start-end-radius:var(--border-radius);border-end-end-radius:0;border-end-start-radius:0}:host(.modal-card){--backdrop-opacity:0;--width:100%;-ms-flex-align:end;align-items:flex-end}:host(.modal-card) .modal-shadow{display:none}:host(.modal-card) ion-backdrop{pointer-events:none}}@media screen and (min-width: 768px){:host(.modal-card){--width:calc(100% - 120px);--height:calc(100% - (120px + var(--ion-safe-area-top) + var(--ion-safe-area-bottom)));--max-width:720px;--max-height:1000px;--backdrop-opacity:0;--box-shadow:0px 0px 30px 10px rgba(0, 0, 0, 0.1);-webkit-transition:all 0.5s ease-in-out;transition:all 0.5s ease-in-out}:host(.modal-card) .modal-wrapper{-webkit-box-shadow:none;box-shadow:none}:host(.modal-card) .modal-shadow{-webkit-box-shadow:var(--box-shadow);box-shadow:var(--box-shadow)}}:host(.modal-sheet) .modal-wrapper{border-start-start-radius:var(--border-radius);border-start-end-radius:var(--border-radius);border-end-end-radius:0;border-end-start-radius:0}:host(.modal-sheet.modal-no-expand-scroll) ion-footer ion-toolbar:first-of-type{padding-top:6px}',ot=tt,nt=':host{--width:100%;--min-width:auto;--max-width:auto;--height:100%;--min-height:auto;--max-height:auto;--overflow:hidden;--border-radius:0;--border-width:0;--border-style:none;--border-color:transparent;--background:var(--ion-background-color, #fff);--box-shadow:none;--backdrop-opacity:0;left:0;right:0;top:0;bottom:0;display:-ms-flexbox;display:flex;position:absolute;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;outline:none;color:var(--ion-text-color, #000);contain:strict}.modal-wrapper,ion-backdrop{pointer-events:auto}:host(.overlay-hidden){display:none}.modal-wrapper,.modal-shadow{border-radius:var(--border-radius);width:var(--width);min-width:var(--min-width);max-width:var(--max-width);height:var(--height);min-height:var(--min-height);max-height:var(--max-height);border-width:var(--border-width);border-style:var(--border-style);border-color:var(--border-color);background:var(--background);-webkit-box-shadow:var(--box-shadow);box-shadow:var(--box-shadow);overflow:var(--overflow);z-index:10}.modal-shadow{position:absolute;background:transparent}@media only screen and (min-width: 768px) and (min-height: 600px){:host{--width:600px;--height:500px;--ion-safe-area-top:0px;--ion-safe-area-bottom:0px;--ion-safe-area-right:0px;--ion-safe-area-left:0px}}@media only screen and (min-width: 768px) and (min-height: 768px){:host{--width:600px;--height:600px}}.modal-handle{left:0px;right:0px;top:5px;border-radius:8px;-webkit-margin-start:auto;margin-inline-start:auto;-webkit-margin-end:auto;margin-inline-end:auto;position:absolute;width:36px;height:5px;-webkit-transform:translateZ(0);transform:translateZ(0);border:0;background:var(--ion-color-step-350, var(--ion-background-color-step-350, #c0c0be));cursor:pointer;z-index:11}.modal-handle::before{-webkit-padding-start:4px;padding-inline-start:4px;-webkit-padding-end:4px;padding-inline-end:4px;padding-top:4px;padding-bottom:4px;position:absolute;width:36px;height:5px;-webkit-transform:translate(-50%, -50%);transform:translate(-50%, -50%);content:""}:host(.modal-sheet){--height:calc(100% - (var(--ion-safe-area-top) + 10px))}:host(.modal-sheet) .modal-wrapper,:host(.modal-sheet) .modal-shadow{position:absolute;bottom:0}:host(.modal-sheet.modal-no-expand-scroll) ion-footer{position:absolute;bottom:0;width:var(--width)}:host{--backdrop-opacity:var(--ion-backdrop-opacity, 0.32)}@media only screen and (min-width: 768px) and (min-height: 600px){:host{--border-radius:2px;--box-shadow:0 28px 48px rgba(0, 0, 0, 0.4)}}.modal-wrapper{-webkit-transform:translate3d(0,  40px,  0);transform:translate3d(0,  40px,  0);opacity:0.01}',it=nt,rt=class{constructor(e){Ye(this,e),this.didPresent=I(this,"ionModalDidPresent",7),this.willPresent=I(this,"ionModalWillPresent",7),this.willDismiss=I(this,"ionModalWillDismiss",7),this.didDismiss=I(this,"ionModalDidDismiss",7),this.ionBreakpointDidChange=I(this,"ionBreakpointDidChange",7),this.didPresentShorthand=I(this,"didPresent",7),this.willPresentShorthand=I(this,"willPresent",7),this.willDismissShorthand=I(this,"willDismiss",7),this.didDismissShorthand=I(this,"didDismiss",7),this.ionMount=I(this,"ionMount",7),this.lockController=be(),this.triggerController=Ce(),this.coreDelegate=we(),this.isSheetModal=!1,this.inheritedAttributes={},this.inline=!1,this.gestureAnimationDismissing=!1,this.onHandleClick=()=>{let{sheetTransition:t,handleBehavior:o}=this;o!=="cycle"||t!==void 0||this.moveToNextBreakpoint()},this.onBackdropTap=()=>{let{sheetTransition:t}=this;t===void 0&&this.dismiss(void 0,De)},this.onLifecycle=t=>{let o=this.usersElement,r=st[t.type];if(o&&r){let s=new CustomEvent(r,{bubbles:!1,cancelable:!1,detail:t.detail});o.dispatchEvent(s)}},this.presented=!1,this.hasController=!1,this.overlayIndex=void 0,this.delegate=void 0,this.keyboardClose=!0,this.enterAnimation=void 0,this.leaveAnimation=void 0,this.breakpoints=void 0,this.expandToScroll=!0,this.initialBreakpoint=void 0,this.backdropBreakpoint=0,this.handle=void 0,this.handleBehavior="none",this.component=void 0,this.componentProps=void 0,this.cssClass=void 0,this.backdropDismiss=!0,this.showBackdrop=!0,this.animated=!0,this.presentingElement=void 0,this.htmlAttributes=void 0,this.isOpen=!1,this.trigger=void 0,this.keepContentsMounted=!1,this.focusTrap=!0,this.canDismiss=!0}onIsOpenChange(e,t){e===!0&&t===!1?this.present():e===!1&&t===!0&&this.dismiss()}triggerChanged(){let{trigger:e,el:t,triggerController:o}=this;e&&o.addClickListener(t,e)}breakpointsChanged(e){e!==void 0&&(this.sortedBreakpoints=e.sort((t,o)=>t-o))}connectedCallback(){let{el:e}=this;xe(e),this.triggerChanged()}disconnectedCallback(){this.triggerController.removeClickListener()}componentWillLoad(){var e;let{breakpoints:t,initialBreakpoint:o,el:r,htmlAttributes:s}=this,n=this.isSheetModal=t!==void 0&&o!==void 0,a=["aria-label","role"];this.inheritedAttributes=ue(r,a),s!==void 0&&a.forEach(i=>{s[i]&&(this.inheritedAttributes=Object.assign(Object.assign({},this.inheritedAttributes),{[i]:s[i]}),delete s[i])}),n&&(this.currentBreakpoint=this.initialBreakpoint),t!==void 0&&o!==void 0&&!t.includes(o)&&U("[ion-modal] - Your breakpoints array must include the initialBreakpoint value."),!((e=this.htmlAttributes)===null||e===void 0)&&e.id||Se(this.el)}componentDidLoad(){this.isOpen===!0&&W(()=>this.present()),this.breakpointsChanged(this.breakpoints),this.triggerChanged()}getDelegate(e=!1){if(this.workingDelegate&&!e)return{delegate:this.workingDelegate,inline:this.inline};let t=this.el.parentNode,o=this.inline=t!==null&&!this.hasController,r=this.workingDelegate=o?this.delegate||this.coreDelegate:this.delegate;return{inline:o,delegate:r}}checkCanDismiss(e,t){return P(this,null,function*(){let{canDismiss:o}=this;return typeof o=="function"?o(e,t):o})}present(){return P(this,null,function*(){let e=yield this.lockController.lock();if(this.presented){e();return}let{presentingElement:t,el:o}=this;this.currentBreakpoint=this.initialBreakpoint;let{inline:r,delegate:s}=this.getDelegate(!0);this.ionMount.emit(),this.usersElement=yield ye(s,o,this.component,["ion-page"],this.componentProps,r),me(o)?yield Le(this.usersElement):this.keepContentsMounted||(yield Oe()),de(()=>this.el.classList.add("show-modal"));let n=t!==void 0;n&&G(this)==="ios"&&(this.statusBarStyle=yield he.getStyle(),Ne()),yield ve(this,"modalEnter",qe,Je,{presentingEl:t,currentBreakpoint:this.initialBreakpoint,backdropBreakpoint:this.backdropBreakpoint,expandToScroll:this.expandToScroll}),typeof window<"u"&&(this.keyboardOpenCallback=()=>{this.gesture&&(this.gesture.enable(!1),W(()=>{this.gesture&&this.gesture.enable(!0)}))},window.addEventListener(ae,this.keyboardOpenCallback)),this.isSheetModal?this.initSheetGesture():n&&this.initSwipeToClose(),e()})}initSwipeToClose(){var e;if(G(this)!=="ios")return;let{el:t}=this,o=this.leaveAnimation||oe.get("modalLeave",Fe),r=this.animation=o(t,{presentingEl:this.presentingElement,expandToScroll:this.expandToScroll});if(!Te(t)){Me(t);return}let n=(e=this.statusBarStyle)!==null&&e!==void 0?e:z.Default;this.gesture=je(t,r,n,()=>{this.gestureAnimationDismissing=!0,ce(this.statusBarStyle),this.animation.onFinish(()=>P(this,null,function*(){yield this.dismiss(void 0,K),this.gestureAnimationDismissing=!1}))}),this.gesture.enable(!0)}initSheetGesture(){let{wrapperEl:e,initialBreakpoint:t,backdropBreakpoint:o}=this;if(!e||t===void 0)return;let r=this.enterAnimation||oe.get("modalEnter",qe),s=this.animation=r(this.el,{presentingEl:this.presentingElement,currentBreakpoint:t,backdropBreakpoint:o,expandToScroll:this.expandToScroll});s.progressStart(!0,1);let{gesture:n,moveSheetToBreakpoint:a}=et(this.el,this.backdropEl,e,t,o,s,this.sortedBreakpoints,this.expandToScroll,()=>{var i;return(i=this.currentBreakpoint)!==null&&i!==void 0?i:0},()=>this.sheetOnDismiss(),i=>{this.currentBreakpoint!==i&&(this.currentBreakpoint=i,this.ionBreakpointDidChange.emit({breakpoint:i}))});this.gesture=n,this.moveSheetToBreakpoint=a,this.gesture.enable(!0)}sheetOnDismiss(){this.gestureAnimationDismissing=!0,this.animation.onFinish(()=>P(this,null,function*(){this.currentBreakpoint=0,this.ionBreakpointDidChange.emit({breakpoint:this.currentBreakpoint}),yield this.dismiss(void 0,K),this.gestureAnimationDismissing=!1}))}dismiss(e,t){return P(this,null,function*(){var o;if(this.gestureAnimationDismissing&&t!==K)return!1;let r=yield this.lockController.lock();if(t!=="handler"&&!(yield this.checkCanDismiss(e,t)))return r(),!1;let{presentingElement:s}=this;s!==void 0&&G(this)==="ios"&&ce(this.statusBarStyle),typeof window<"u"&&this.keyboardOpenCallback&&(window.removeEventListener(ae,this.keyboardOpenCallback),this.keyboardOpenCallback=void 0);let a=yield Ae(this,e,t,"modalLeave",Fe,Xe,{presentingEl:s,currentBreakpoint:(o=this.currentBreakpoint)!==null&&o!==void 0?o:this.initialBreakpoint,backdropBreakpoint:this.backdropBreakpoint,expandToScroll:this.expandToScroll});if(a){let{delegate:i}=this.getDelegate();yield ke(i,this.usersElement),de(()=>this.el.classList.remove("show-modal")),this.animation&&this.animation.destroy(),this.gesture&&this.gesture.destroy()}return this.currentBreakpoint=void 0,this.animation=void 0,r(),a})}onDidDismiss(){return ie(this.el,"ionModalDidDismiss")}onWillDismiss(){return ie(this.el,"ionModalWillDismiss")}setCurrentBreakpoint(e){return P(this,null,function*(){if(!this.isSheetModal){U("[ion-modal] - setCurrentBreakpoint is only supported on sheet modals.");return}if(!this.breakpoints.includes(e)){U(`[ion-modal] - Attempted to set invalid breakpoint value ${e}. Please double check that the breakpoint value is part of your defined breakpoints.`);return}let{currentBreakpoint:t,moveSheetToBreakpoint:o,canDismiss:r,breakpoints:s,animated:n}=this;t!==e&&o&&(this.sheetTransition=o({breakpoint:e,breakpointOffset:1-t,canDismiss:r!==void 0&&r!==!0&&s[0]===0,animated:n}),yield this.sheetTransition,this.sheetTransition=void 0)})}getCurrentBreakpoint(){return P(this,null,function*(){return this.currentBreakpoint})}moveToNextBreakpoint(){return P(this,null,function*(){let{breakpoints:e,currentBreakpoint:t}=this;if(!e||t==null)return!1;let o=e.filter(a=>a!==0),s=(o.indexOf(t)+1)%o.length,n=o[s];return yield this.setCurrentBreakpoint(n),!0})}render(){let{handle:e,isSheetModal:t,presentingElement:o,htmlAttributes:r,handleBehavior:s,inheritedAttributes:n,focusTrap:a,expandToScroll:i}=this,d=e!==!1&&t,l=G(this),f=o!==void 0&&l==="ios",m=s==="cycle";return q(Ie,Object.assign({key:"0991b2e4e32da511e59fb1463b47e4ac1b86d1ca","no-router":!0,tabindex:"-1"},r,{style:{zIndex:`${2e4+this.overlayIndex}`},class:Object.assign({[l]:!0,"modal-default":!f&&!t,"modal-card":f,"modal-sheet":t,"modal-no-expand-scroll":t&&!i,"overlay-hidden":!0,[Z]:a===!1},Be(this.cssClass)),onIonBackdropTap:this.onBackdropTap,onIonModalDidPresent:this.onLifecycle,onIonModalWillPresent:this.onLifecycle,onIonModalWillDismiss:this.onLifecycle,onIonModalDidDismiss:this.onLifecycle}),q("ion-backdrop",{key:"ca9453ffe1021fb252ad9460676cfabb5633f00f",ref:b=>this.backdropEl=b,visible:this.showBackdrop,tappable:this.backdropDismiss,part:"backdrop"}),l==="ios"&&q("div",{key:"9f8da446a7b0f3b26aec856e13f6d6d131a7e37b",class:"modal-shadow"}),q("div",Object.assign({key:"9d08bf600571849c97b58f66df40b496a358d1e1",role:"dialog"},n,{"aria-modal":"true",class:"modal-wrapper ion-overlay-wrapper",part:"content",ref:b=>this.wrapperEl=b}),d&&q("button",{key:"f8bf0d1126e5376519101225d9965727121ee042",class:"modal-handle",tabIndex:m?0:-1,"aria-label":"Activate to adjust the size of the dialog overlaying the screen",onClick:m?this.onHandleClick:void 0,part:"handle"}),q("slot",{key:"6d52849df98f2c6c8fbc03996a931ea6a39a512b"})))}get el(){return Re(this)}static get watchers(){return{isOpen:["onIsOpenChange"],trigger:["triggerChanged"]}}},st={ionModalDidPresent:"ionViewDidEnter",ionModalWillPresent:"ionViewWillEnter",ionModalWillDismiss:"ionViewWillLeave",ionModalDidDismiss:"ionViewDidLeave"};rt.style={ios:ot,md:it};export{rt as ion_modal};
