.debug-container {
  max-width: 600px;
  margin: 0 auto;
  padding: 16px;
}

.diagnostic-item {
  margin-bottom: 16px;
  border-radius: 8px;
  overflow: hidden;
}

.diagnostic-header {
  padding: 12px 16px;
  font-weight: 600;
  color: white;
  display: flex;
  align-items: center;
  gap: 8px;
}

.diagnostic-header.pending {
  background-color: var(--ion-color-warning);
}

.diagnostic-header.success {
  background-color: var(--ion-color-success);
}

.diagnostic-header.error {
  background-color: var(--ion-color-danger);
}

.diagnostic-header.warning {
  background-color: var(--ion-color-warning);
}

.diagnostic-content {
  padding: 16px;
  background-color: var(--ion-color-light);
  border-left: 4px solid var(--ion-color-medium);
}

.diagnostic-content.pending {
  border-left-color: var(--ion-color-warning);
}

.diagnostic-content.success {
  border-left-color: var(--ion-color-success);
}

.diagnostic-content.error {
  border-left-color: var(--ion-color-danger);
}

.diagnostic-content.warning {
  border-left-color: var(--ion-color-warning);
}

.diagnostic-message {
  font-size: 0.9rem;
  margin-bottom: 8px;
}

.diagnostic-details {
  font-size: 0.8rem;
  color: var(--ion-color-medium);
  font-family: monospace;
  background-color: var(--ion-color-step-50);
  padding: 8px;
  border-radius: 4px;
  white-space: pre-wrap;
  word-break: break-all;
}

.test-credentials {
  background-color: var(--ion-color-step-100);
  padding: 12px;
  border-radius: 8px;
  margin-bottom: 16px;
}

.test-credentials h3 {
  margin: 0 0 8px 0;
  color: var(--ion-color-primary);
}

.test-credentials p {
  margin: 4px 0;
  font-size: 0.9rem;
  color: var(--ion-color-medium);
}

.action-buttons {
  display: flex;
  gap: 8px;
  margin-top: 16px;
}

.action-buttons ion-button {
  flex: 1;
}

ion-card {
  margin-bottom: 16px;
}

ion-card-title {
  color: var(--ion-color-primary);
}

.status-icon {
  font-size: 1.2rem;
}

.loading-spinner {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px;
}

.error-text {
  color: var(--ion-color-danger);
}

.success-text {
  color: var(--ion-color-success);
}

.warning-text {
  color: var(--ion-color-warning);
}
