<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::table('evacuation_centers', function (Blueprint $table) {
            // First drop the columns if they exist
            if (Schema::hasColumn('evacuation_centers', 'disaster_type')) {
                $table->dropColumn('disaster_type');
            }
            if (Schema::hasColumn('evacuation_centers', 'marker_color')) {
                $table->dropColumn('marker_color');
            }
            
            // Add disaster_type column with enum values
            $table->enum('disaster_type', ['Typhoon', 'Flood', 'Earthquake'])
                  ->default('Typhoon')
                  ->after('status');
            
            // Add marker_color column
            $table->string('marker_color')
                  ->default('#FF0000') // Red for Typhoon
                  ->after('disaster_type');
        });
    }

    public function down()
    {
        Schema::table('evacuation_centers', function (Blueprint $table) {
            if (Schema::hasColumn('evacuation_centers', 'disaster_type')) {
                $table->dropColumn('disaster_type');
            }
            if (Schema::hasColumn('evacuation_centers', 'marker_color')) {
                $table->dropColumn('marker_color');
            }
        });
    }
};