# FCM Notification Testing Guide

This guide will help you test Firebase Cloud Messaging (FCM) notifications in your WebAlerto-1 application.

## Prerequisites

1. Your Laravel backend (WebAlerto-1) is running
2. Your Ionic app (mobile_ionic) is installed on your device
3. You have a valid FCM token registered with your backend

## Testing Methods

### 1. Using the Test Script

We've created a PHP script to send test notifications directly using the FCM API:

```bash
cd WebAlerto-1
php test-fcm-notification.php [FCM_TOKEN]
```

If you don't provide an FCM token, the script will try to find one in your database.

### 2. Using the API Endpoint

You can send a notification using the API endpoint:

```bash
curl -X POST http://localhost:8000/api/notifications/send \
  -H "Content-Type: application/json" \
  -d '{
    "title": "Test Notification",
    "body": "This is a test notification",
    "category": "General",
    "severity": "high"
  }'
```

### 3. Using the Admin Panel

You can also send notifications from the admin panel:

1. Log in to the admin panel
2. Go to the Notifications section
3. Create a new notification
4. Fill in the details and click Send

## Verifying Notifications

### 1. Check the Device

- The notification should appear on your device
- If the app is in the foreground, it should show an alert or toast
- If the app is in the background, it should show a system notification
- If the device is locked, it should show a notification on the lock screen

### 2. Check the Logs

#### Laravel Logs

Check the Laravel logs for any errors:

```bash
tail -f WebAlerto-1/storage/logs/laravel.log
```

#### Android Logs

Check the Android logs using ADB:

```bash
adb logcat | grep -E "FCM|Firebase"
```

### 3. Check the Database

Check if the FCM token is properly registered in the database:

```sql
SELECT * FROM device_tokens ORDER BY created_at DESC LIMIT 10;
```

## Troubleshooting

### 1. Notification Not Appearing

If the notification doesn't appear on your device:

- Make sure the FCM token is valid and registered with your backend
- Check if Google Play Services is installed and up to date
- Verify that notification permissions are granted
- Check the Android logs for any errors

### 2. FCM Token Not Registered

If the FCM token is not being registered with your backend:

- Check the network connection
- Verify that the API URL is correct
- Check the Laravel logs for any errors
- Try refreshing the FCM token in the app

### 3. Backend Errors

If you're getting errors from the backend:

- Make sure the FIREBASE_SERVER_KEY is set in your .env file
- Verify that the FCM API is enabled in your Firebase project
- Check the Laravel logs for any errors

## Testing Different Notification Types

### 1. High Severity Notification

```json
{
  "title": "Emergency Alert",
  "body": "This is a high severity notification",
  "category": "General",
  "severity": "high"
}
```

### 2. Medium Severity Notification

```json
{
  "title": "Important Notice",
  "body": "This is a medium severity notification",
  "category": "General",
  "severity": "medium"
}
```

### 3. Low Severity Notification

```json
{
  "title": "Information",
  "body": "This is a low severity notification",
  "category": "General",
  "severity": "low"
}
```

### 4. Notification with Category

```json
{
  "title": "Flood Alert",
  "body": "Flood warning in your area",
  "category": "flood",
  "severity": "high"
}
```

## Additional Resources

- [Firebase Cloud Messaging Documentation](https://firebase.google.com/docs/cloud-messaging)
- [Laravel FCM Package Documentation](https://github.com/kreait/laravel-firebase)
- [Ionic FCM Documentation](https://capacitorjs.com/docs/apis/push-notifications)
