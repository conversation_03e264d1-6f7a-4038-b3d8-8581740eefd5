<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\User;

class UserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create 5 normal users (random position and barangay)
        User::factory()->count(5)->create();

        // Create 1 Chairman user
        User::factory()->chairman()->create([
            'email' => '<EMAIL>', 
            'password' => bcrypt('password123'), // default password
            'full_name' => 'Chairman <PERSON>',
            'barangay' => 'Mabolo', 
        ]);
    }
}
