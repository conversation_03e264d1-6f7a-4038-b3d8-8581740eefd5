import type { CapacitorConfig } from '@capacitor/cli';

const config: CapacitorConfig = {
  appId: 'com.capstone.webalerto',
  appName: 'WebAlerto',
  webDir: 'www',
  server: {
    androidScheme: 'http',
    cleartext: true,
    allowNavigation: [
      'http://localhost:8100',
      'http://localhost:8000',
      'http://127.0.0.1:8100',
      'http://127.0.0.1:8000',
      'http://***************:8000',
      'https://*.ngrok-free.app',
      'http://*.ngrok-free.app',
      'https://*.ngrok.io',
      'http://*.ngrok.io'
    ]
    // Comment out the development server URL for production builds
    // url: 'http://localhost:8100',
    // iosScheme: 'ionic'
  },
  plugins: {
    SplashScreen: {
      launchShowDuration: 1000,
      backgroundColor: "#FFFFFF",
      androidSplashResourceName: "splash",
      showSpinner: true,
      spinnerColor: "#999999",
      autoHide: true
    },
    Geolocation: {
      permissions: {
        android: {
          coarseLocation: true,
          fineLocation: true
        },
        ios: {
          whenInUse: true,
          always: false
        }
      }
    },
    FirebaseMessaging: {
      presentationOptions: ["badge", "sound", "alert"]
    }
  },
  android: {
    allowMixedContent: true,
    captureInput: true,
    webContentsDebuggingEnabled: true
  },
  ios: {
    contentInset: "always",
    preferredContentMode: "mobile"
  }
};

export default config;
