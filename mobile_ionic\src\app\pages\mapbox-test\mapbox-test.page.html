<ion-header [translucent]="true">
  <ion-toolbar>
    <ion-title>Mapbox Integration Test</ion-title>
    <ion-buttons slot="end">
      <ion-button (click)="runTests()" [disabled]="isRunning">
        <ion-icon name="refresh"></ion-icon>
      </ion-button>
    </ion-buttons>
  </ion-toolbar>
</ion-header>

<ion-content [fullscreen]="true">
  <ion-header collapse="condense">
    <ion-toolbar>
      <ion-title size="large">Mapbox Test</ion-title>
    </ion-toolbar>
  </ion-header>

  <div class="test-container">
    
    <!-- Test Results Summary -->
    <ion-card>
      <ion-card-header>
        <ion-card-title>Test Results</ion-card-title>
        <ion-card-subtitle>Mapbox integration status</ion-card-subtitle>
      </ion-card-header>
      <ion-card-content>
        
        <ion-item>
          <ion-icon 
            [name]="getTestIcon(testResults.tokenValid)" 
            [color]="getTestColor(testResults.tokenValid)"
            slot="start">
          </ion-icon>
          <ion-label>
            <h3>Token Configuration</h3>
            <p>{{ testResults.tokenValid ? 'Valid Mapbox token configured' : 'Invalid or missing token' }}</p>
          </ion-label>
        </ion-item>

        <ion-item>
          <ion-icon 
            [name]="getTestIcon(testResults.apiReachable)" 
            [color]="getTestColor(testResults.apiReachable)"
            slot="start">
          </ion-icon>
          <ion-label>
            <h3>API Connectivity</h3>
            <p>{{ testResults.apiReachable ? 'Mapbox API is reachable' : 'Cannot reach Mapbox API' }}</p>
          </ion-label>
        </ion-item>

        <ion-item>
          <ion-icon 
            [name]="getTestIcon(testResults.routingWorks)" 
            [color]="getTestColor(testResults.routingWorks)"
            slot="start">
          </ion-icon>
          <ion-label>
            <h3>Routing Functionality</h3>
            <p>{{ testResults.routingWorks ? 'Route calculation working' : 'Route calculation failed' }}</p>
          </ion-label>
        </ion-item>

      </ion-card-content>
    </ion-card>

    <!-- Test Logs -->
    <ion-card>
      <ion-card-header>
        <ion-card-title>Test Logs</ion-card-title>
        <ion-card-subtitle>Detailed test execution logs</ion-card-subtitle>
      </ion-card-header>
      <ion-card-content>
        <div class="logs-container">
          <div *ngFor="let log of testLogs" class="log-entry">
            {{ log }}
          </div>
          <div *ngIf="testLogs.length === 0" class="no-logs">
            No logs yet. Click the refresh button to run tests.
          </div>
        </div>
      </ion-card-content>
    </ion-card>

    <!-- Instructions -->
    <ion-card>
      <ion-card-header>
        <ion-card-title>Next Steps</ion-card-title>
      </ion-card-header>
      <ion-card-content>
        <div *ngIf="testResults.tokenValid && testResults.apiReachable && testResults.routingWorks">
          <ion-icon name="checkmark-circle" color="success"></ion-icon>
          <h3>🎉 Perfect! Your Mapbox integration is working!</h3>
          <p>You can now use the map features in your app. The network errors should be resolved.</p>
          
          <ion-button expand="block" color="success" routerLink="/tabs/map">
            <ion-icon name="map" slot="start"></ion-icon>
            Go to Map
          </ion-button>
        </div>
        
        <div *ngIf="!testResults.tokenValid">
          <ion-icon name="warning" color="warning"></ion-icon>
          <h3>⚠️ Token Issue</h3>
          <p>Your Mapbox token is not configured correctly. Please check your environment files.</p>
        </div>
        
        <div *ngIf="testResults.tokenValid && !testResults.apiReachable">
          <ion-icon name="wifi-off" color="danger"></ion-icon>
          <h3>🌐 Connectivity Issue</h3>
          <p>Cannot reach Mapbox API. Please check your internet connection.</p>
        </div>
        
        <div *ngIf="testResults.tokenValid && testResults.apiReachable && !testResults.routingWorks">
          <ion-icon name="alert-circle" color="warning"></ion-icon>
          <h3>🗺️ Routing Issue</h3>
          <p>API is reachable but routing failed. Check the logs for details.</p>
        </div>
      </ion-card-content>
    </ion-card>

  </div>
</ion-content>

<style>
.test-container {
  padding: 16px;
}

.logs-container {
  background: #f5f5f5;
  border-radius: 8px;
  padding: 12px;
  max-height: 300px;
  overflow-y: auto;
  font-family: 'Courier New', monospace;
  font-size: 12px;
}

.log-entry {
  margin-bottom: 4px;
  word-wrap: break-word;
}

.no-logs {
  color: #666;
  font-style: italic;
}

ion-card {
  margin-bottom: 16px;
}

ion-item {
  --padding-start: 0;
  --inner-padding-end: 0;
}
</style>
