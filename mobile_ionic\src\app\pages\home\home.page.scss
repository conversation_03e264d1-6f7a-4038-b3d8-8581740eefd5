.status-text {
  margin-left: 8px;
}

ion-header, ion-title {
  text-align: center;
  font-family: '<PERSON><PERSON><PERSON>', Arial, sans-serif;
  font-size: 2rem;
  font-weight: 700;
  letter-spacing: 1px;
  text-shadow: 1px 2px 4px #ccc;
}

.disaster-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 24px;
  margin: 32px 0 0 0;
}

.disaster {
  margin: 0;
  cursor: pointer;
  transition: transform 0.2s;

  &:hover {
    transform: scale(1.05);
  }

  ion-card-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    padding: 16px;
  }

  img {
    width: 60px;
    height: 60px;
    margin-bottom: 8px;
  }
}

.earthquake {
  --background: #ffcc80;
}

.typhoon {
  --background: #c5e1a5;
  size: 100px;
  width: 105px;
  height: 120px;
}

.flood {
  --background: #81d4fa;
}

.view-map {
  margin-top: 24px;
  --background: #00bfff;

  &:hover {
    --background: #0090cc;
  }

  &[disabled] {
    --background: #999;
  }
}

.top-disaster {
  display: flex;
  justify-content: space-between;
  gap: 16px;
  align-items: center;


}
  .home-logo {
    width: 150px;
    height: 150px;


  }
    .home-title {
      padding-top: 105px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 30px;
      font-weight: 700;
      letter-spacing: 1px;
      text-shadow: 1px 2px 4px #ccc;
    }

.notifications-section {
  margin-top: 20px;
  border-top: 1px solid var(--ion-color-light);
  padding-top: 10px;
}

ion-item-divider {
  --background: transparent;
  --color: var(--ion-color-primary);
  font-weight: bold;
  font-size: 1.1rem;
  letter-spacing: 0.5px;
  margin-bottom: 8px;
}