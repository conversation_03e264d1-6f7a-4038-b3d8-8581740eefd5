<?php

namespace App\Services;

use App\Contracts\FCMServiceInterface;
use App\Exceptions\FCMException;
use App\Models\DeviceToken;
use App\Models\Notification;
use Illuminate\Support\Facades\Log;
use Kreait\Firebase\Factory;
use Kreait\Firebase\Messaging\CloudMessage;
use Kreait\Firebase\Messaging\Message;
use Kreait\Firebase\Messaging\Notification as FirebaseNotification;
use Kreait\Firebase\Messaging\AndroidConfig;
use Kreait\Firebase\Messaging\ApnsConfig;
use Kreait\Firebase\Messaging\WebPushConfig;
use Kreait\Firebase\Messaging\MessageTarget;
use Kreait\Firebase\Messaging\MulticastSendReport;
use Kreait\Firebase\Exception\MessagingException;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\RateLimiter;

class FCMService implements FCMServiceInterface
{
    protected $batchSize;
    protected $messaging;
    protected $rateLimiter;
    protected $maxNotificationsPerMinute = 60;
    protected $notificationExpiry = 3600; // 1 hour
    protected $maxRetries;
    protected $retryDelay;

    /**
     * Constructor
     *
     * Initializes the FCM service with configuration from config files
     */
    public function __construct()
    {
        $this->batchSize = config('firebase.batch_size', 500);
        $this->rateLimiter = RateLimiter::class;
        $this->maxRetries = config('fcm.retry.max_attempts', 3);
        $this->retryDelay = config('fcm.retry.delay', 1000);

        // Set CA certificate path for cURL
        if (!ini_get('curl.cainfo')) {
            $certPath = storage_path('certs/cacert.pem');
            if (file_exists($certPath)) {
                ini_set('curl.cainfo', $certPath);
                ini_set('openssl.cafile', $certPath);
                Log::info('Set CA certificate path for cURL', ['path' => $certPath]);
            } else {
                Log::warning('CA certificate file not found', ['path' => $certPath]);
            }
        }

        try {
            $this->initializeFirebase();
        } catch (FCMException $e) {
            // Log the error but don't re-throw
            // This allows the application to continue running even if FCM is not available
            // The error will be handled when trying to send notifications
            Log::error('FCM initialization failed in constructor', [
                'error' => $e->getMessage()
            ]);

            // Set messaging to null to indicate initialization failed
            $this->messaging = null;
        }
    }

    /**
     * Initialize Firebase Messaging
     *
     * @throws FCMException If Firebase service account file is missing or invalid
     * @return bool True if initialization was successful
     */
    protected function initializeFirebase()
    {
        $serviceAccount = storage_path(config('firebase.projects.app.credentials', 'firebase-service-account.json'));

        // Check if service account file exists
        if (!file_exists($serviceAccount)) {
            $errorMessage = 'Firebase service account file not found at: ' . $serviceAccount;
            Log::error($errorMessage);
            throw new FCMException($errorMessage);
        }

        try {
            // Parse the service account JSON file
            $serviceAccountData = json_decode(file_get_contents($serviceAccount), true);
            if (json_last_error() !== JSON_ERROR_NONE) {
                $errorMessage = 'Firebase service account file is not valid JSON: ' . json_last_error_msg();
                Log::error($errorMessage, ['path' => $serviceAccount]);
                throw new FCMException($errorMessage);
            }

            // Validate required fields
            $requiredFields = ['type', 'project_id', 'private_key_id', 'private_key', 'client_email'];
            foreach ($requiredFields as $field) {
                if (!isset($serviceAccountData[$field]) || empty($serviceAccountData[$field])) {
                    $errorMessage = "Firebase service account file is missing required field: {$field}";
                    Log::error($errorMessage, ['path' => $serviceAccount]);
                    throw new FCMException($errorMessage);
                }
            }

            // Validate private key format
            if (!str_contains($serviceAccountData['private_key'], '-----BEGIN PRIVATE KEY-----')) {
                $errorMessage = 'Firebase service account file has invalid private key format';
                Log::error($errorMessage, ['path' => $serviceAccount]);
                throw new FCMException($errorMessage);
            }

            // Initialize Firebase Messaging
            $factory = (new Factory)->withServiceAccount($serviceAccount);
            $this->messaging = $factory->createMessaging();

            Log::info('Firebase Messaging initialized successfully', [
                'project_id' => $serviceAccountData['project_id']
            ]);

            return true;
        } catch (FCMException $e) {
            // Re-throw FCM exceptions
            throw $e;
        } catch (\Exception $e) {
            // Wrap other exceptions in FCMException
            $errorMessage = 'Failed to initialize Firebase: ' . $e->getMessage();
            Log::error($errorMessage, [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            throw new FCMException($errorMessage, 0, $e);
        }
    }

    public function send(string $token, array $notification): string
    {
        try {
            $this->validateNotification($notification);

            $message = $this->buildMessage($token, $notification);

            Log::channel(config('fcm.logging.channel'))
                ->info('Sending FCM notification', [
                    'token' => $token,
                    'notification' => $notification
                ]);

            $attempt = 0;
            do {
                try {
                    $messageId = $this->messaging->send($message);
                    $this->logSuccess($token, $messageId);
                    return $messageId;
                } catch (MessagingException $e) {
                    $attempt++;
                    if ($attempt >= $this->maxRetries) {
                        throw $e;
                    }
                    usleep($this->retryDelay * 1000);
                }
            } while ($attempt < $this->maxRetries);
        } catch (MessagingException $e) {
            $this->logError($token, $e);
            throw new FCMException(
                "Failed to send notification: {$e->getMessage()}",
                $e->getCode(),
                $e,
                ['token' => $token, 'notification' => $notification]
            );
        }
    }

    public function sendMulticast(array $tokens, array $notification, bool $detailed = false): array
    {
        if (empty($tokens)) {
            throw new FCMException('No tokens provided for multicast message');
        }

        if (count($tokens) > config('fcm.max_tokens', 500)) {
            throw new FCMException('Too many tokens provided for multicast message. Maximum is 500.');
        }

        try {
            $this->validateNotification($notification);

            $message = $this->buildMessage(null, $notification);

            Log::channel(config('fcm.logging.channel'))
                ->info('Sending FCM multicast notification', [
                    'token_count' => count($tokens),
                    'notification' => $notification
                ]);

            $attempt = 0;
            do {
                try {
                    $report = $this->messaging->sendMulticast($message, $tokens);
                    return $this->processMulticastReport($report, $detailed);
                } catch (MessagingException $e) {
                    $attempt++;
                    if ($attempt >= $this->maxRetries) {
                        throw $e;
                    }
                    usleep($this->retryDelay * 1000);
                }
            } while ($attempt < $this->maxRetries);
        } catch (MessagingException $e) {
            $this->logError('multicast', $e);
            throw new FCMException(
                "Failed to send multicast notification: {$e->getMessage()}",
                $e->getCode(),
                $e,
                ['tokens' => $tokens, 'notification' => $notification]
            );
        }
    }

    public function validateToken(string $token): bool
    {
        $cacheKey = "fcm_token_validation:{$token}";

        return Cache::remember($cacheKey, now()->addHours(24), function () use ($token) {
            try {
                $this->messaging->validateRegistrationTokens([$token]);
                return true;
            } catch (MessagingException $e) {
                return false;
            }
        });
    }

    public function getDeliveryStatus(string $messageId): array
    {
        try {
            $status = $this->messaging->getMessageStatus($messageId);
            return [
                'message_id' => $messageId,
                'status' => $status->getStatus(),
                'delivered_at' => $status->getDeliveredAt(),
                'error' => $status->getError()
            ];
        } catch (MessagingException $e) {
            throw new FCMException(
                "Failed to get message status: {$e->getMessage()}",
                $e->getCode(),
                $e,
                ['message_id' => $messageId]
            );
        }
    }

    protected function buildMessage(?string $token, array $notification): Message
    {
        $message = $token
            ? CloudMessage::withTarget(MessageTarget::TOKEN, $token)
            : CloudMessage::new();

        $message->withNotification(FirebaseNotification::create(
            $notification['title'],
            $notification['message']
        ));

        if (isset($notification['data'])) {
            $message->withData($notification['data']);
        }

        if (isset($notification['android'])) {
            $message->withAndroidConfig(AndroidConfig::fromArray($notification['android']));
        }

        if (isset($notification['apns'])) {
            $message->withApnsConfig(ApnsConfig::fromArray($notification['apns']));
        }

        if (isset($notification['webpush'])) {
            $message->withWebPushConfig(WebPushConfig::fromArray($notification['webpush']));
        }

        return $message;
    }

    protected function validateNotification(array $notification): void
    {
        $required = ['title', 'message'];
        $missing = array_diff($required, array_keys($notification));

        if (!empty($missing)) {
            throw new FCMException(
                'Missing required notification fields: ' . implode(', ', $missing)
            );
        }
    }

    protected function processMulticastReport(MulticastSendReport $report, bool $detailed): array
    {
        $result = [
            'success_count' => $report->successes()->count(),
            'failure_count' => $report->failures()->count(),
        ];

        if ($detailed) {
            $result['results'] = $report->getItems()->map(function ($item) {
                return [
                    'status' => $item->isSuccess() ? 'sent' : 'failed',
                    'error' => $item->isSuccess() ? null : $item->error()->getMessage()
                ];
            })->toArray();
        }

        return $result;
    }

    protected function logSuccess(string $token, string $messageId): void
    {
        Log::channel(config('fcm.logging.channel'))
            ->info('FCM notification sent successfully', [
                'token' => $token,
                'message_id' => $messageId
            ]);
    }

    protected function logError(string $token, \Exception $e): void
    {
        Log::channel(config('fcm.logging.channel'))
            ->error('FCM notification failed', [
                'token' => $token,
                'error' => $e->getMessage(),
                'code' => $e->getCode()
            ]);
    }

    public function sendNotification(Notification $notification, array $tokens = null)
    {
        // Check rate limit
        if (!$this->checkRateLimit()) {
            Log::warning('Rate limit exceeded for FCM notifications');
            return [
                'success' => false,
                'message' => 'Rate limit exceeded. Please try again later.',
                'success_count' => 0,
                'failure_count' => 0,
                'invalid_tokens' => 0
            ];
        }

        if ($tokens === null) {
            $tokens = $this->getActiveTokens();
        }

        if (empty($tokens)) {
            Log::info('No active device tokens found');
            return [
                'success' => false,
                'message' => 'No active device tokens found',
                'success_count' => 0,
                'failure_count' => 0,
                'invalid_tokens' => 0
            ];
        }

        // Check if Firebase Messaging is initialized
        if (!$this->messaging) {
            // Try to initialize Firebase again
            try {
                $this->initializeFirebase();
            } catch (FCMException $e) {
                // If initialization fails again, handle the error
                return $this->handleMessagingError();
            }

            // If we still don't have messaging after re-initialization, handle the error
            if (!$this->messaging) {
                return $this->handleMessagingError();
            }
        }

        // Create the notification payload with customization
        $fcmNotification = $this->createNotificationPayload($notification);
        $data = $this->createDataPayload($notification);

        // Send to all tokens in batches
        $successCount = 0;
        $failureCount = 0;
        $invalidTokens = [];
        $errors = [];

        // Process tokens in chunks
        foreach (array_chunk($tokens, $this->batchSize) as $tokenBatch) {
            $result = $this->sendBatchNotifications($tokenBatch, $fcmNotification, $data);
            $successCount += $result['success_count'];
            $failureCount += $result['failure_count'];
            $invalidTokens = array_merge($invalidTokens, $result['invalid_tokens']);

            if (!empty($result['error'])) {
                $errors[] = $result['error'];
            }
        }

        // Handle results
        if ($successCount === 0 && !empty($errors)) {
            return $this->handleSendError($errors, $failureCount, $invalidTokens);
        }

        // Clean up invalid tokens
        if (!empty($invalidTokens)) {
            $this->deactivateInvalidTokens($invalidTokens);
        }

        // Mark notification as sent
        $notification->sent = true;
        $notification->save();

        // Log success
        $this->logNotificationSuccess($notification, $successCount, $failureCount, $invalidTokens);

        return [
            'success' => true,
            'message' => "Notification sent to {$successCount} devices" . ($failureCount > 0 ? " ({$failureCount} failures)" : ""),
            'success_count' => $successCount,
            'failure_count' => $failureCount,
            'invalid_tokens' => count($invalidTokens)
        ];
    }

    protected function checkRateLimit()
    {
        $key = 'fcm_notifications_' . date('Y-m-d_H');
        $maxAttempts = $this->maxNotificationsPerMinute;
        $decaySeconds = 60; // 1 minute decay time

        return !RateLimiter::tooManyAttempts($key, $maxAttempts) && RateLimiter::hit($key, $decaySeconds);
    }

    /**
     * Create the notification payload for FCM
     *
     * @param Notification $notification The notification to create a payload for
     * @return array The notification payload
     */
    protected function createNotificationPayload(Notification $notification)
    {
        // Base notification payload
        $payload = [
            'title' => $notification->title,
            'body' => $notification->message,
            'sound' => $this->getNotificationSound($notification->severity),
            'badge' => '1',
            'priority' => $this->getNotificationPriority($notification->severity),
            'expiry' => time() + $this->notificationExpiry
        ];

        // Add category-specific customizations
        if ($notification->category) {
            // Add icon based on category
            $payload['icon'] = $this->getCategoryIcon($notification->category);

            // Add color based on disaster type (not severity)
            $payload['color'] = $this->getDisasterColor($notification->category);

            // Add tag based on category for notification grouping
            $payload['tag'] = strtolower($notification->category);
        }

        return $payload;
    }

    /**
     * Create the data payload for FCM
     *
     * @param Notification $notification The notification to create a data payload for
     * @return array The data payload
     */
    protected function createDataPayload(Notification $notification)
    {
        // Base data payload
        $payload = [
            'title' => $notification->title,
            'body' => $notification->message,
            'category' => $notification->category,
            'severity' => $notification->severity,
            'click_action' => 'FLUTTER_NOTIFICATION_CLICK',
            'notification_id' => $notification->id,
            'time' => now()->toIso8601String(),
            'project_id' => config('firebase.projects.app.project_id', 'last-5acaf')
        ];

        // Add additional data based on notification type
        if ($notification->category) {
            switch (strtolower($notification->category)) {
                case 'earthquake':
                case 'flood':
                case 'typhoon':
                case 'fire':
                    // For disaster notifications, add disaster type
                    $payload['disaster_type'] = strtolower($notification->category);
                    $payload['action'] = 'show_evacuation';
                    break;

                case 'general':
                case 'announcement':
                    // For general announcements
                    $payload['action'] = 'show_announcement';
                    break;

                default:
                    // Default action
                    $payload['action'] = 'open_app';
                    break;
            }
        }

        return $payload;
    }

    /**
     * Get the appropriate icon for a notification category
     *
     * @param string $category The notification category
     * @return string The icon name
     */
    protected function getCategoryIcon($category)
    {
        switch (strtolower($category)) {
            case 'earthquake':
                return 'earthquake_icon';
            case 'flood':
                return 'flood_icon';
            case 'typhoon':
                return 'typhoon_icon';
            case 'fire':
                return 'fire_icon';
            default:
                return 'notification_icon';
        }
    }

    /**
     * Get the appropriate color for a disaster category
     *
     * @param string $category The notification category
     * @return string The color in hex format
     */
    protected function getDisasterColor($category)
    {
        switch (strtolower($category)) {
            case 'earthquake':
                return '#FFA500'; // Orange for earthquake
            case 'flood':
                return '#0066CC'; // Blue for flood
            case 'typhoon':
                return '#008000'; // Green for typhoon
            case 'fire':
                return '#FF0000'; // Red for fire
            default:
                return '#03B2DD'; // Default app color
        }
    }

    /**
     * Get the appropriate color for a notification severity
     *
     * @param string $severity The notification severity
     * @return string The color in hex format
     */
    protected function getSeverityColor($severity)
    {
        switch (strtolower($severity)) {
            case 'high':
                return '#FF0000'; // Red
            case 'medium':
                return '#FFA500'; // Orange
            default:
                return '#0000FF'; // Blue
        }
    }

    protected function getNotificationSound($severity)
    {
        switch ($severity) {
            case 'high':
                return 'emergency.mp3';
            case 'medium':
                return 'alert.mp3';
            default:
                return 'default.mp3';
        }
    }

    protected function getNotificationPriority($severity)
    {
        switch ($severity) {
            case 'high':
                return 'high';
            case 'medium':
                return 'normal';
            default:
                return 'low';
        }
    }

    protected function handleMessagingError()
    {
        $serviceAccount = storage_path(config('firebase.projects.app.credentials', 'firebase-service-account.json'));
        $errorMessage = '';

        if (!file_exists($serviceAccount)) {
            $errorMessage = 'Firebase service account file not found. Please check that the file exists at: ' . $serviceAccount;
        } else {
            $serviceAccountData = json_decode(file_get_contents($serviceAccount), true);
            if (json_last_error() !== JSON_ERROR_NONE) {
                $errorMessage = 'Firebase service account file is not valid JSON. Error: ' . json_last_error_msg();
            } else if (isset($serviceAccountData['private_key_id']) && $serviceAccountData['private_key_id'] === '1234567890abcdef1234567890abcdef12345678') {
                $errorMessage = 'Firebase service account file contains placeholder values. Please replace with actual service account credentials.';
            } else {
                $errorMessage = 'Firebase messaging not initialized. Please check your Firebase configuration.';
            }
        }

        Log::error($errorMessage);
        return [
            'success' => false,
            'message' => $errorMessage,
            'success_count' => 0,
            'failure_count' => 0,
            'invalid_tokens' => 0
        ];
    }

    protected function handleSendError($errors, $failureCount, $invalidTokens)
    {
        $errorMessage = 'Failed to send notifications: ' . implode('; ', array_unique($errors));
        Log::error($errorMessage);

        return [
            'success' => false,
            'message' => $errorMessage,
            'success_count' => 0,
            'failure_count' => $failureCount,
            'invalid_tokens' => count($invalidTokens)
        ];
    }

    protected function logNotificationSuccess($notification, $successCount, $failureCount, $invalidTokens)
    {
        Log::info('Notification process completed', [
            'id' => $notification->id,
            'sent' => $notification->sent,
            'success_count' => $successCount,
            'failure_count' => $failureCount,
            'invalid_tokens' => count($invalidTokens)
        ]);
    }

    protected function sendBatchNotifications(array $tokenBatch, array $fcmNotification, array $data)
    {
        $successCount = 0;
        $failureCount = 0;
        $invalidTokens = [];

        try {
            $messages = [];

            foreach ($tokenBatch as $token) {
                if ($this->isValidTokenFormat($token)) {
                    $messages[] = CloudMessage::withTarget('token', $token)
                        ->withNotification($fcmNotification)
                        ->withData($data);
                } else {
                    $invalidTokens[] = $token;
                    Log::warning('Invalid token format', ['token' => $this->maskToken($token)]);
                }
            }

            if (empty($messages)) {
                return [
                    'success_count' => 0,
                    'failure_count' => 0,
                    'invalid_tokens' => $invalidTokens
                ];
            }

            $sendReport = $this->messaging->sendAll($messages);
            $successCount = $sendReport->successes()->count();
            $failureCount = $sendReport->failures()->count();

            foreach ($sendReport->failures()->getItems() as $failure) {
                $error = $failure->error();
                $errorMessage = $error->getMessage();
                $errorCode = $this->extractErrorCode($errorMessage);

                Log::error('FCM notification failure', [
                    'token_hash' => hash('sha256', $failure->target()->value()),
                    'error' => $errorMessage,
                    'code' => $errorCode
                ]);

                $invalidTokenCodes = ['invalid-argument', 'registration-token-not-registered', 'invalid-registration-token', 'unregistered'];
                if (!empty($errorCode) && in_array($errorCode, $invalidTokenCodes)) {
                    $invalidTokens[] = $failure->target()->value();
                }
            }

            return [
                'success_count' => $successCount,
                'failure_count' => $failureCount,
                'invalid_tokens' => $invalidTokens
            ];
        } catch (\Exception $e) {
            Log::error('Failed to send batch of FCM notifications', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'batch_size' => count($tokenBatch)
            ]);

            return [
                'success_count' => $successCount,
                'failure_count' => $failureCount + count($tokenBatch),
                'invalid_tokens' => $invalidTokens,
                'error' => $e->getMessage()
            ];
        }
    }

    protected function getActiveTokens()
    {
        return DeviceToken::where('is_active', true)->pluck('token')->toArray();
    }

    protected function deactivateInvalidTokens(array $tokens)
    {
        $count = DeviceToken::whereIn('token', $tokens)->update(['is_active' => false]);
        Log::info('Deactivated invalid tokens', ['count' => $count]);
        return $count;
    }

    protected function isValidTokenFormat(string $token)
    {
        return preg_match('/^[a-zA-Z0-9:_\-]+$/', $token) && strlen($token) > 20;
    }

    protected function extractErrorCode(string $errorMessage)
    {
        if (preg_match('/error(?:\s+code)?[:\s]+([a-z0-9_-]+)/i', $errorMessage, $matches)) {
            return $matches[1];
        }
        return '';
    }

    protected function maskToken(string $token)
    {
        if (strlen($token) > 10) {
            return substr($token, 0, 5) . '...' . substr($token, -5);
        }
        return substr($token, 0, 3) . '...';
    }
}
