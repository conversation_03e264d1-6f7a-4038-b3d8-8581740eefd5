import{a as B}from"./chunk-X3ALRMID.js";import{b as z}from"./chunk-2IZDSB5C.js";import{a as w}from"./chunk-FNGMIKCE.js";import{B as n,Ba as I,C as r,D as h,Ea as O,F as _,I as a,M as d,N as p,Na as E,O as u,Ta as F,U as C,Ua as W,Xa as A,Z as k,aa as y,ba as P,ca as S,da as T,ea as b,fa as v,ga as x,gb as L,lb as j,pb as N,u as m,v as l,va as R,w as M}from"./chunk-7LSN6DH6.js";import"./chunk-EFGZ4QZO.js";import"./chunk-VKNYRYKJ.js";import"./chunk-NETZAO6G.js";import"./chunk-YLGV66M3.js";import"./chunk-VD4VFVTQ.js";import"./chunk-HC6MZPB3.js";import"./chunk-IP3IRUG3.js";import"./chunk-UKIOCGZG.js";import"./chunk-RMJ7PCZJ.js";import"./chunk-K54AU7WQ.js";import"./chunk-VBU7NMPV.js";import"./chunk-JIZJ6CAM.js";import"./chunk-MCRJI3T3.js";import"./chunk-YNIR5NDL.js";import"./chunk-NO26UXQI.js";import"./chunk-SELJIRKY.js";import"./chunk-ODNE7IRY.js";import"./chunk-F4H6ZFEG.js";import"./chunk-JWIEPCRG.js";import"./chunk-7WD7LEC6.js";import"./chunk-WTCPO44B.js";import"./chunk-SV7S5NYR.js";import"./chunk-L5T6STQ3.js";import"./chunk-3EJRMEWO.js";import"./chunk-GNOVVPTF.js";import"./chunk-BAKMWPBW.js";import"./chunk-5OMUW5VI.js";import"./chunk-OBXDPQ3V.js";import{h as c}from"./chunk-LNJ3S2LQ.js";var Z=(()=>{let g=class g{constructor(t,o,e,s,i,q){this.authService=t,this.router=o,this.http=e,this.platform=s,this.fcmService=i,this.alertController=q,this.user={full_name:"",email:"",password:"",confirmPassword:""},this.fcmToken="",this.getFCMToken()}getFCMToken(){if(!this.platform.is("cordova")&&!this.platform.is("capacitor")){console.log("Running in browser, using mock FCM token"),this.fcmToken="browser-mock-token-"+Math.random().toString(36).substring(2,15),console.log("Mock FCM Token:",this.fcmToken);return}this.fcmService.getToken().then(t=>{console.log("FCM Token from service:",t),this.fcmToken=t}).catch(t=>{console.error("Error getting FCM token from service:",t)})}onRegister(){return c(this,null,function*(){if(this.user.password!==this.user.confirmPassword){yield this.presentAlert("Registration Failed","Passwords do not match!");return}this.authService.register({full_name:this.user.full_name,email:this.user.email,password:this.user.password,password_confirmation:this.user.confirmPassword}).subscribe({next:t=>c(this,null,function*(){if(console.log("Registration successful:",t),this.fcmToken){console.log("Registering FCM token after registration:",this.fcmToken);let o={token:this.fcmToken,device_type:this.platform.is("ios")?"ios":"android",project_id:w.firebase.projectId};console.log("Token registration payload:",o),this.fcmService.registerTokenWithBackend(this.fcmToken)}else console.warn("No FCM token available to register after registration");yield this.presentAlert("Registration Successful","Your account has been created successfully. Please log in."),this.router.navigate(["/login"])}),error:t=>c(this,null,function*(){var o;console.error("Registration error:",t),yield this.presentAlert("Registration Failed","Registration failed: "+(((o=t.error)==null?void 0:o.message)||"Unknown error"))})})})}registerTokenWithEndpoints(t){return c(this,null,function*(){let o=[`${w.apiUrl}/device-token`,"http://localhost:8000/api/device-token","https://7af9-43-226-6-217.ngrok-free.app/api/device-token"];for(let e of o)try{let s=yield this.http.post(e,t).toPromise();console.log(`FCM token registered with ${e}:`,s),localStorage.setItem("fcm_token",this.fcmToken);break}catch(s){console.error(`Error registering token with ${e}:`,s)}})}presentAlert(t,o){return c(this,null,function*(){yield(yield this.alertController.create({header:t,message:o,buttons:["OK"]})).present()})}goToLogin(){this.router.navigate(["/login"])}};g.\u0275fac=function(o){return new(o||g)(l(B),l(k),l(C),l(R),l(z),l(j))},g.\u0275cmp=M({type:g,selectors:[["app-register"]],decls:33,vars:4,consts:[[1,"ion-padding","register-bg"],[1,"register-wrapper"],["src","assets/ALERTO.png","alt","App Logo",1,"register-logo"],[1,"register-title"],[1,"register-form",3,"ngSubmit"],["position","floating"],["type","text","name","full_name","required","",3,"ngModelChange","ngModel"],["type","email","name","email","required","",3,"ngModelChange","ngModel"],["type","password","name","password","required","",3,"ngModelChange","ngModel"],["type","password","name","confirmPassword","required","",3,"ngModelChange","ngModel"],["expand","block","type","submit",1,"register-btn"],[1,"ion-text-center","ion-margin-top"],[3,"click"]],template:function(o,e){o&1&&(n(0,"ion-content",0)(1,"div",1),h(2,"img",2),n(3,"h1",3),a(4,"Sign Up Here!"),r(),n(5,"form",4),_("ngSubmit",function(){return e.onRegister()}),n(6,"ion-item")(7,"ion-label",5),a(8,"Full Name:"),r(),n(9,"ion-input",6),u("ngModelChange",function(i){return p(e.user.full_name,i)||(e.user.full_name=i),i}),r()(),n(10,"ion-item")(11,"ion-label",5),a(12,"Email:"),r(),n(13,"ion-input",7),u("ngModelChange",function(i){return p(e.user.email,i)||(e.user.email=i),i}),r()(),n(14,"ion-item")(15,"ion-label",5),a(16,"Password:"),r(),n(17,"ion-input",8),u("ngModelChange",function(i){return p(e.user.password,i)||(e.user.password=i),i}),r()(),n(18,"ion-item")(19,"ion-label",5),a(20,"Confirm Password:"),r(),n(21,"ion-input",9),u("ngModelChange",function(i){return p(e.user.confirmPassword,i)||(e.user.confirmPassword=i),i}),r()(),h(22,"br")(23,"br"),n(24,"ion-button",10),a(25,"Register"),r()(),n(26,"div",11)(27,"ion-text"),a(28,"Already have an account? "),r(),n(29,"a",12),_("click",function(){return e.goToLogin()}),n(30,"strong")(31,"u"),a(32,"Log In"),r()()()()()()),o&2&&(m(9),d("ngModel",e.user.full_name),m(4),d("ngModel",e.user.email),m(4),d("ngModel",e.user.password),m(4),d("ngModel",e.user.confirmPassword))},dependencies:[N,O,E,F,W,A,L,I,x,b,y,P,v,T,S],styles:[".register-container[_ngcontent-%COMP%]{height:80vh;display:flex;align-items:center;justify-content:center}.register-wrapper[_ngcontent-%COMP%]{width:100%;max-width:420px;padding:32px 28px;margin:0 auto;display:flex;flex-direction:column;align-items:center;text-align:center}.register-logo[_ngcontent-%COMP%]{width:280px;height:280px}.register-title[_ngcontent-%COMP%]{font-size:2rem;font-weight:700}.register-desc[_ngcontent-%COMP%]{color:#888;font-size:1.1rem}.register-form[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-size:18px;color:#888}.register-form[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{width:100%;--highlight-color-focused: xz#000000;--min-height: 44px;--padding-start: 0;--padding-end: 0;--inner-padding-end: 0;--inner-padding-start: 0}.forgot-link[_ngcontent-%COMP%]{text-align:right;font-size:.95rem}.forgot-link[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]{color:#1565c0;text-decoration:none}.register-link[_ngcontent-%COMP%]{font-size:1rem;color:#444}.register-link[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]{color:#1565c0;text-decoration:none;font-weight:600}.register-btn[_ngcontent-%COMP%]{--border-radius: 25px;font-size:1.1rem;height:48px;width:100%}"]});let f=g;return f})();export{Z as RegisterPage};
