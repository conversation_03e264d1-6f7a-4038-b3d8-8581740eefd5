<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\DeviceToken;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class SimulatedNotificationController extends Controller
{
    /**
     * Send a simulated notification to the frontend.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function send(Request $request)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'message' => 'required|string',
            'category' => 'required|string',
            'severity' => 'required|string|in:low,medium,high',
            'token' => 'nullable|string',
        ]);

        try {
            // Create the notification payload
            $notification = [
                'title' => $request->title,
                'body' => $request->message,
                'category' => $request->category,
                'severity' => $request->severity,
                'time' => now()->toIso8601String(),
            ];

            // Log the notification
            Log::info('Simulated notification sent', [
                'title' => $request->title,
                'message' => $request->message,
                'category' => $request->category,
                'severity' => $request->severity,
                'token' => $request->token ? substr($request->token, 0, 10) . '...' : 'all',
            ]);

            // Store the notification in cache for polling
            $this->storePendingNotification($notification);

            // Return success response
            return response()->json([
                'success' => true,
                'message' => 'Simulated notification sent successfully',
                'notification' => $notification
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to send simulated notification', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to send simulated notification: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Check for pending notifications.
     * This endpoint is polled by the frontend to check for new notifications.
     *
     * @return \Illuminate\Http\Response
     */
    public function check()
    {
        try {
            // Check if there's a pending notification in the cache
            $pendingNotification = \Illuminate\Support\Facades\Cache::get('pending_notification');

            if ($pendingNotification) {
                // Clear the pending notification from cache
                \Illuminate\Support\Facades\Cache::forget('pending_notification');

                // Return the notification
                return response()->json([
                    'success' => true,
                    'message' => 'Pending notification found',
                    'notification' => $pendingNotification
                ]);
            }

            // No pending notification
            return response()->json([
                'success' => true,
                'message' => 'No pending notifications',
                'notification' => null
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to check for pending notifications', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to check for pending notifications: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Store a notification in the cache for polling.
     *
     * @param array $notification The notification data
     * @return bool
     */
    private function storePendingNotification($notification)
    {
        try {
            // Store the notification in cache for 5 minutes
            \Illuminate\Support\Facades\Cache::put('pending_notification', $notification, 300);
            return true;
        } catch (\Exception $e) {
            Log::error('Failed to store pending notification', [
                'error' => $e->getMessage(),
                'notification' => $notification
            ]);
            return false;
        }
    }
}
