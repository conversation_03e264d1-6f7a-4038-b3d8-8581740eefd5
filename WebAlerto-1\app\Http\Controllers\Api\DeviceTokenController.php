<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\DeviceToken;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Str;

class DeviceTokenController extends Controller
{
    protected $tokenValidationRules = [
        'token' => 'required|string|min:20|max:255',
        'device_type' => 'required|string|in:ios,android,web',
        'project_id' => 'required|string',
        'user_id' => 'nullable|integer'
    ];

    /**
     * Register or update a device token for push notifications.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function register(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'token' => 'required|string',
            'device_type' => 'required|string|in:android,ios,web',
            'device_name' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        // Get authenticated user
        $user = Auth::user();

        if (!$user) {
            return response()->json(['message' => 'Unauthenticated'], 401);
        }

        // Check if token already exists for this user
        $deviceToken = DeviceToken::updateOrCreate(
            [
                'user_id' => $user->id,
                'token' => $request->token,
            ],
            [
                'device_type' => $request->device_type,
                'device_name' => $request->device_name,
                'is_active' => true,
            ]
        );

        return response()->json([
            'message' => 'Device token registered successfully',
            'data' => $deviceToken
        ], 200);
    }

    /**
     * Deactivate a device token.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function deactivate(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'token' => 'required|string'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $token = DeviceToken::where('token', $request->token)->first();
            if (!$token) {
                return response()->json([
                    'success' => false,
                    'message' => 'Token not found'
                ], 404);
            }

            $token->is_active = false;
            $token->deactivated_at = now();
            $token->save();

            Log::info('Device token deactivated', [
                'token_hash' => hash('sha256', $request->token)
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Token deactivated successfully'
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to deactivate device token', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to deactivate token',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Store a device token (for mobile app).
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        try {
            // Validate request
            $validator = Validator::make($request->all(), $this->tokenValidationRules);
            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            // Rate limiting
            $key = 'token_registration_' . $request->ip();
            if (Cache::get($key) > 10) {
                return response()->json([
                    'success' => false,
                    'message' => 'Too many token registrations. Please try again later.'
                ], 429);
            }
            Cache::increment($key, 1, 3600);

            // Validate token format
            if (!$this->isValidTokenFormat($request->token)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Invalid token format'
                ], 422);
            }

            // Check for duplicate token
            $existingToken = DeviceToken::where('token', $request->token)->first();
            if ($existingToken) {
                // Update the token with new information
                $existingToken->is_active = true;
                $existingToken->last_used_at = now();

                // Update user_id if provided and different from current
                if ($request->has('user_id') && $request->user_id && $existingToken->user_id != $request->user_id) {
                    $existingToken->user_id = $request->user_id;
                    Log::info('Updated token with new user association', [
                        'token_hash' => hash('sha256', $request->token),
                        'old_user_id' => $existingToken->user_id,
                        'new_user_id' => $request->user_id
                    ]);
                }

                $existingToken->save();

                return response()->json([
                    'success' => true,
                    'message' => 'Token already registered and updated',
                    'data' => [
                        'id' => $existingToken->id,
                        'device_type' => $existingToken->device_type,
                        'project_id' => $existingToken->project_id,
                        'user_id' => $existingToken->user_id,
                        'updated_at' => $existingToken->updated_at
                    ]
                ]);
            }

            // Create new token with user_id if provided
            $tokenData = [
                'token' => $request->token,
                'device_type' => $request->device_type,
                'project_id' => $request->project_id,
                'is_active' => true,
                'last_used_at' => now()
            ];

            // Add user_id if provided
            if ($request->has('user_id') && $request->user_id) {
                $tokenData['user_id'] = $request->user_id;
                Log::info('Associating token with user', [
                    'user_id' => $request->user_id,
                    'token_hash' => hash('sha256', $request->token)
                ]);
            }

            $deviceToken = DeviceToken::create($tokenData);

            Log::info('Device token registered successfully', [
                'token_hash' => hash('sha256', $request->token),
                'device_type' => $request->device_type,
                'project_id' => $request->project_id
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Token registered successfully',
                'data' => [
                    'id' => $deviceToken->id,
                    'device_type' => $deviceToken->device_type,
                    'project_id' => $deviceToken->project_id,
                    'created_at' => $deviceToken->created_at
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to register device token', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to register token',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    public function refresh(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'old_token' => 'required|string',
                'new_token' => 'required|string',
                'device_type' => 'required|string|in:ios,android,web',
                'project_id' => 'required|string'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            // Validate new token format
            if (!$this->isValidTokenFormat($request->new_token)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Invalid new token format'
                ], 422);
            }

            // Check if new token already exists
            $existingToken = DeviceToken::where('token', $request->new_token)->first();
            if ($existingToken) {
                return response()->json([
                    'success' => false,
                    'message' => 'New token already registered'
                ], 409);
            }

            // Find and deactivate old token
            $oldToken = DeviceToken::where('token', $request->old_token)->first();
            if ($oldToken) {
                $oldToken->is_active = false;
                $oldToken->deactivated_at = now();
                $oldToken->save();
            }

            // Create new token
            $newToken = DeviceToken::create([
                'token' => $request->new_token,
                'device_type' => $request->device_type,
                'project_id' => $request->project_id,
                'is_active' => true,
                'last_used_at' => now()
            ]);

            Log::info('Device token refreshed', [
                'old_token_hash' => hash('sha256', $request->old_token),
                'new_token_hash' => hash('sha256', $request->new_token),
                'device_type' => $request->device_type,
                'project_id' => $request->project_id
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Token refreshed successfully',
                'data' => [
                    'id' => $newToken->id,
                    'device_type' => $newToken->device_type,
                    'project_id' => $newToken->project_id,
                    'created_at' => $newToken->created_at
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to refresh device token', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to refresh token',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    protected function isValidTokenFormat(string $token)
    {
        return preg_match('/^[a-zA-Z0-9:_\-]+$/', $token) && strlen($token) > 20;
    }
}
