ion-header {
  background: var(--ion-color-primary);
  
  ion-toolbar {
    --background: transparent;
    
    ion-title {
      color: white;
    }

    ion-back-button {
      --color: white;
    }
  }
}

.profile-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  gap: 16px;
  margin-bottom: 16px;
  padding: 24px 16px;
  background: #fff;

  ion-avatar {
    width: 60px;
    height: 60px;
  }

  .profile-info {
    h2 {
      margin: 0;
      font-size: 18px;
      font-weight: 500;
    }

    p {
      margin: 4px 0 0;
      color: var(--ion-color-medium);
      font-size: 14px;
    }
  }
}

ion-list {
  background: transparent;

  ion-item {
    --padding-start: 16px;
    --padding-end: 16px;
    --min-height: 56px;
    margin-bottom: 1px;

    ion-icon {
      font-size: 15px;
      margin-right: 16px;
      color: var(--ion-color-medium);
    }

    ion-label {
      font-size: 16px;
      font-weight: 400;
    }
  }
}

// Modal Styles
.terms-modal {
  --height: 90%;
  --border-radius: 16px;

  ion-header {
    ion-toolbar {
      --background: var(--ion-color-light);

      ion-title {
        font-size: 18px;
        font-weight: 600;
      }
    }
  }

  ion-content {
    h2 {
      color: var(--ion-color-dark);
      font-size: 24px;
      font-weight: 700;
      margin-bottom: 8px;
    }

    .effective-date {
      color: var(--ion-color-medium);
      font-size: 14px;
      margin-bottom: 24px;
    }

    h3 {
      color: var(--ion-color-dark);
      font-size: 18px;
      font-weight: 600;
      margin: 24px 0 12px;
    }

    p {
      color: var(--ion-color-medium);
      font-size: 16px;
      line-height: 1.5;
      margin-bottom: 16px;
    }

    ul {
      list-style: none;
      padding: 0;
      margin: 0;

      li {
        color: var(--ion-color-medium);
        font-size: 16px;
        line-height: 1.5;
        margin-bottom: 8px;
        padding-left: 24px;
        position: relative;

        &:before {
          content: "•";
          position: absolute;
          left: 8px;
          color: var(--ion-color-primary);
        }
      }
    }
  }

  .legend-title {
    color: var(--ion-color-dark);
    font-size: 20px;
    font-weight: 600;
    margin-bottom: 24px;
  }

  .legend-container {
    margin-top: 30px;
    display: flex;
    flex-direction: column;
    align-items: center;

    .legend-items {
      display: flex;
      flex-direction: column;
      gap: 16px;
      background: var(--ion-color-light);
      border-radius: 8px;

      .icon-wrapper {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 32px;
        height: 32px;

        .legend-icon {
          font-size: 20px;
        }
      }

      .legend-label {
        color: var(--ion-color-dark);
        font-size: 16px;
      }
    }
  }
}
