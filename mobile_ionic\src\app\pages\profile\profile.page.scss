ion-header {
  background: var(--ion-color-primary);
  
  ion-toolbar {
    --background: transparent;
    
    ion-title {
      color: white;
    }

    ion-back-button {
      --color: white;
    }
  }
}

.profile-header {
  position: relative;
  margin-bottom: 20px;
}

.profile-background {
  background: linear-gradient(135deg, #03b2dd 0%, #0891b2 100%);
  height: 200px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;

  // Add subtle pattern overlay
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"><g fill="none" fill-rule="evenodd"><g fill="%23ffffff" fill-opacity="0.05"><circle cx="30" cy="30" r="2"/></g></svg>');
    pointer-events: none;
  }
}

.profile-avatar {
  width: 80px;
  height: 80px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 16px;
  backdrop-filter: blur(10px);
  border: 3px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);

  .avatar-icon {
    font-size: 40px;
    color: white;
  }
}

.profile-info {
  text-align: center;
  color: white;

  h2 {
    margin: 0 0 8px 0;
    font-size: 24px;
    font-weight: 600;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  p {
    margin: 0;
    font-size: 16px;
    opacity: 0.9;
    font-weight: 400;
  }
}

.menu-list {
  background: transparent;
  margin-top: 20px;
  padding: 0 16px;

  ion-item {
    --padding-start: 16px;
    --padding-end: 16px;
    --min-height: 60px;
    margin-bottom: 8px;
    border-radius: 12px;
    --background: white;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
    }

    ion-icon {
      font-size: 20px;
      margin-right: 16px;
      color: var(--ion-color-primary);
    }

    ion-label {
      font-size: 16px;
      font-weight: 500;
      color: var(--ion-color-dark);
    }

    img {
      filter: brightness(0) saturate(100%) invert(34%) sepia(77%) saturate(2476%) hue-rotate(203deg) brightness(99%) contrast(92%);
    }
  }
}

// Modal Styles
.terms-modal {
  --height: 90%;
  --border-radius: 16px;

  ion-header {
    ion-toolbar {
      --background: var(--ion-color-light);

      ion-title {
        font-size: 18px;
        font-weight: 600;
      }
    }
  }

  ion-content {
    h2 {
      color: var(--ion-color-dark);
      font-size: 24px;
      font-weight: 700;
      margin-bottom: 8px;
    }

    .effective-date {
      color: var(--ion-color-medium);
      font-size: 14px;
      margin-bottom: 24px;
    }

    h3 {
      color: var(--ion-color-dark);
      font-size: 18px;
      font-weight: 600;
      margin: 24px 0 12px;
    }

    p {
      color: var(--ion-color-medium);
      font-size: 16px;
      line-height: 1.5;
      margin-bottom: 16px;
    }

    ul {
      list-style: none;
      padding: 0;
      margin: 0;

      li {
        color: var(--ion-color-medium);
        font-size: 16px;
        line-height: 1.5;
        margin-bottom: 8px;
        padding-left: 24px;
        position: relative;

        &:before {
          content: "•";
          position: absolute;
          left: 8px;
          color: var(--ion-color-primary);
        }
      }
    }
  }

  .legend-title {
    color: var(--ion-color-dark);
    font-size: 20px;
    font-weight: 600;
    margin-bottom: 24px;
  }

  .legend-container {
    margin-top: 30px;
    display: flex;
    flex-direction: column;
    align-items: center;

    .legend-items {
      display: flex;
      flex-direction: column;
      gap: 16px;
      background: var(--ion-color-light);
      border-radius: 8px;

      .icon-wrapper {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 32px;
        height: 32px;

        .legend-icon {
          font-size: 20px;
        }
      }

      .legend-label {
        color: var(--ion-color-dark);
        font-size: 16px;
      }
    }
  }
}
