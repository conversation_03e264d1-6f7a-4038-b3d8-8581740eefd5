# Notification Sounds

This directory contains sound files used for notifications in the WebAlerto app.

## Required Files

- `notification.mp3`: Default notification sound
- `emergency.mp3`: Sound for emergency/high severity notifications

## Usage

These sound files are played when notifications are received in the app. You should replace these placeholder files with actual sound files.

## Recommended Sound Specifications

- Format: MP3
- Duration: 1-3 seconds
- Size: < 100KB
- Sample Rate: 44.1kHz
- Bit Rate: 128kbps

## How to Add Sound Files

1. Download or create appropriate notification sounds
2. Name them according to the required filenames above
3. Place them in this directory
4. Make sure they are included in your app build
