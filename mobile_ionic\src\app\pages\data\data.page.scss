.profile-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  max-width: 420px;
  margin: 0 auto;
  padding: 32px 20px;
}

form {
  width: 100%;
}

ion-item {
  --background: #f9f9f9;
  --border-radius: 25px;
  --padding-start: 10px;
  --padding-end: 10px;
  --inner-padding-top: 5px;
  --inner-padding-bottom: 5px;
  margin-bottom: 10px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
  height: 65px;
}

ion-label {
  font-size: 5px;
  color: #333;
}

ion-input, ion-select {
  font-size: 15px;
}

.terms-checkbox {
  display: flex;
  align-items: center;
  margin-top: 12px;
  font-size: 14px;
  color: #444;
  flex-wrap: wrap;
  line-height: 1.4;
}

.terms-checkbox ion-checkbox {
  margin-right: 8px;
}

.terms-checkbox a {
  color: #1565c0;
  text-decoration: underline;
  font-weight: 500;
  margin-left: 4px;
}

ion-button {
  margin-top: 24px;
  --background: #1565c0;
  --border-radius: 10px;
  --box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.15);
}

.error-message {
  text-align: center;
  margin: 10px 0;
  color: #e53935;

  p {
    margin: 0;
    font-size: 14px;
  }
}
.header-logo {
  text-align: center;


  img {
    width: 80px;
    height: auto;
  }

  h2 {
    font-size: 18px;
    color: #222;
    font-weight: 600;
    text-shadow: 0px 2px 4px rgba(0,0,0,0.15);
  }
}

.home-title {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 15px;
  font-weight: 700;
  letter-spacing: 1px;
  text-shadow: 1px 2px 4px #ccc;
  padding-top: 59px;
}