<?php

/**
 * This script sends a test EARTHQUAKE notification to test the orange styling
 * Run it with: php test-earthquake-notification.php
 */

// Load the Laravel application
require __DIR__ . '/vendor/autoload.php';
$app = require_once __DIR__ . '/bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

// Get the FCM service
$fcmService = app(App\Services\FCMService::class);

echo "🧪 Testing EARTHQUAKE notification (Orange styling)...\n";

// Get the most recent token from the database
try {
    $deviceToken = App\Models\DeviceToken::where('is_active', true)
        ->orderBy('created_at', 'desc')
        ->first();

    if (!$deviceToken) {
        echo "❌ No active tokens found in the database.\n";
        echo "Make sure your mobile app is registered and has an FCM token.\n";
        exit(1);
    }

    $token = $deviceToken->token;
    echo "✅ Using token from database: " . substr($token, 0, 10) . "...\n";

} catch (Exception $e) {
    echo "❌ Database query failed: " . $e->getMessage() . "\n";
    exit(1);
}

// Create a test EARTHQUAKE notification
echo "📱 Creating earthquake test notification...\n";

$notification = new App\Models\Notification([
    'title' => 'EARTHQUAKE ALERT',
    'message' => 'Magnitude 6.5 earthquake detected. Seek shelter immediately and prepare for evacuation.',
    'category' => 'Earthquake',  // This will trigger ORANGE styling
    'severity' => 'high',
    'sent' => false
]);

$notification->save();

echo "✅ Earthquake notification created with ID: " . $notification->id . "\n";

// Send the notification
echo "🚨 Sending EARTHQUAKE notification...\n";

try {
    $result = $fcmService->send($token, [
        'title' => $notification->title,
        'message' => $notification->message,
        'data' => [
            'notification_id' => $notification->id,
            'category' => $notification->category,
            'severity' => $notification->severity,
            'time' => date('Y-m-d H:i:s'),
            'disaster_type' => 'earthquake'
        ]
    ]);

    echo "🎉 SUCCESS: EARTHQUAKE notification sent!\n";
    echo "📧 Message ID: " . $result . "\n";
    echo "🟠 This should appear as an ORANGE notification on your device!\n";

    // Update notification status
    $notification->sent = true;
    $notification->save();

} catch (Exception $e) {
    echo "❌ ERROR: Failed to send notification: " . $e->getMessage() . "\n";
    
    // Log the error
    \Illuminate\Support\Facades\Log::error("Earthquake FCM Test Error", [
        'error' => $e->getMessage(),
        'trace' => $e->getTraceAsString()
    ]);
}

echo "\n🏁 Earthquake notification test completed.\n";
echo "📱 Check your mobile device for the ORANGE earthquake alert!\n";
