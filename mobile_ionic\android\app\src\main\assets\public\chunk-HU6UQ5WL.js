import{a as i}from"./chunk-WMEG6PAA.js";var t=function(e){return e.Unimplemented="UNIMPLEMENTED",e.Unavailable="UNAVAILABLE",e}(t||{}),o=function(e){return e.Body="body",e.<PERSON><PERSON>="ionic",e.Native="native",e.None="none",e}(o||{}),a={getEngine(){let e=i();if(e!=null&&e.isPluginAvailable("Keyboard"))return e.Plugins.Keyboard},getResizeMode(){let e=this.getEngine();return e!=null&&e.getResizeMode?e.getResizeMode().catch(n=>{if(n.code!==t.Unimplemented)throw n}):Promise.resolve(void 0)}};export{o as a,a as b};
