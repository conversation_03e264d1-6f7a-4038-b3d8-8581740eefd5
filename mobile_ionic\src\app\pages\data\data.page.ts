import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { IonicModule } from '@ionic/angular';
import { Router } from '@angular/router';
import { MobileUserService } from '../../services/mobile-user.service';

@Component({
  selector: 'app-data',
  templateUrl: './data.page.html',
  styleUrls: ['./data.page.scss'],
  standalone: true,
  imports: [IonicModule, CommonModule, FormsModule]
})
export class DataPage {
  userData = {
    full_name: '',
    mobile_number: '',
    age: '',
    gender: '',
    address: ''
  };

  acceptedTerms = false;
  showError = false;
  errorMessage = '';

  constructor(private router: Router, private mobileUserService: MobileUserService) {}

  onSave() {
    console.log(this.userData); // Debug

    const requiredFields = ['full_name', 'mobile_number', 'age', 'gender', 'address'];
    for (const field of requiredFields) {
      const value = (this.userData as any)[field];
      if (value === undefined || value === null || value.toString().trim() === '') {
        this.showError = true;
        this.errorMessage = 'All fields are required.';
        return;
      }
    }

    if (!/^[0-9]{11}$/.test(this.userData.mobile_number)) {
      this.showError = true;
      this.errorMessage = 'Mobile number must be 11 digits.';
      return;
    }

    if (isNaN(Number(this.userData.age)) || Number(this.userData.age) <= 0) {
      this.showError = true;
      this.errorMessage = 'Age must be a positive number.';
      return;
    }

    if (!this.acceptedTerms) {
      this.showError = true;
      this.errorMessage = 'You must accept the Terms and Conditions.';
      return;
    }

    this.mobileUserService.createUser({
      full_name: this.userData.full_name,
      mobile_number: this.userData.mobile_number,
      age: Number(this.userData.age),
      gender: this.userData.gender,
      address: this.userData.address
    }).subscribe({
      next: () => {
        const userData = {
          full_name: this.userData.full_name,
          mobile_number: this.userData.mobile_number,
          age: this.userData.age,
          gender: this.userData.gender,
          address: this.userData.address,
        };
        localStorage.setItem('userData', JSON.stringify(userData));
        localStorage.setItem('onboardingComplete', 'true');
        this.router.navigate(['/tabs/home']);
      },
      error: (err: any) => {
        this.showError = true;
        this.errorMessage = err.error?.message || 'Failed to save user.';
      }
    });
  }

}

