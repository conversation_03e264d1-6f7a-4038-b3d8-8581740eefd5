
<ion-content class="ion-padding">
  <div class="profile-container">
    
    <div class="header-logo">
      <div class="home-title"><img src="assets/ALERTO.png" alt="App Logo" class="home-logo"/> Hi, Welcome to Safe Area!</div>
    </div>
    <form (ngSubmit)="onSave()">
      <ion-item>
        <ion-label position="floating" style="font-size: 15px;">Full Name:</ion-label>
        <ion-input type="text" [(ngModel)]="userData.full_name" name="full_name" style="font-size: 20px; padding-bottom: 10px;" required></ion-input>
      </ion-item>

      <ion-item>
        <ion-label position="floating" style="font-size: 15px;">Mobile Number:</ion-label>
        <ion-input type="text" [(ngModel)]="userData.mobile_number" name="mobile_number" style="font-size: 20px; padding-bottom: 10px;" required ></ion-input>
      </ion-item>

      <ion-item>
        <ion-label position="floating"  style="font-size: 15px;">Age:</ion-label>
        <ion-input type="number" [(ngModel)]="userData.age" name="age" style="font-size: 20px; padding-bottom: 10px; "required></ion-input>
      </ion-item>

      <ion-item>
        <ion-label position="floating" >Gender <ion-select [(ngModel)]="userData.gender" name="gender" required>
          <ion-select-option value="Male">Male</ion-select-option>
          <ion-select-option value="Female">Female</ion-select-option>
          <ion-select-option value="Other">Other</ion-select-option>
        </ion-select></ion-label>
        
      </ion-item>

      <ion-item>
        <ion-label position="floating" style="font-size: 15px;">Address:</ion-label>
        <ion-input type="text" [(ngModel)]="userData.address" name="address" style="font-size: 20px; padding-bottom: 10px;" required></ion-input>
      </ion-item>

      <ion-text color="danger" *ngIf="showError" class="error-message">
        <p>{{ errorMessage }}</p>
      </ion-text>

      <div class="terms-checkbox">
        <ion-checkbox slot="start" [(ngModel)]="acceptedTerms" name="acceptedTerms" required></ion-checkbox>
        <label>I accept the <a href="#">Terms and Conditions</a></label>
      </div>

      <ion-button expand="block" type="submit" class="ion-margin-top">
        Save
      </ion-button>
    </form>
  </div>
</ion-content>
