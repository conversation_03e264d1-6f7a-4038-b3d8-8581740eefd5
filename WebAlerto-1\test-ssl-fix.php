<?php
require __DIR__.'/vendor/autoload.php';

// Get the application instance
$app = require_once __DIR__.'/bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

// Test connection to Google OAuth endpoint
echo "Testing connection to Google OAuth endpoint...\n";
$ch = curl_init('https://oauth2.googleapis.com/token');
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_HEADER, true);
curl_setopt($ch, CURLOPT_NOBODY, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 10);
curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, true);
curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 2);

// Execute the request
$response = curl_exec($ch);
$error = curl_error($ch);
$info = curl_getinfo($ch);
curl_close($ch);

if ($response === false) {
    echo "Error connecting to Google OAuth endpoint: " . $error . "\n";
    echo "The SSL certificate issue is still present.\n";
} else {
    echo "Connection successful!\n";
    echo "HTTP Status Code: " . $info['http_code'] . "\n";
    echo "The SSL certificate issue has been fixed.\n";
    
    // Now test sending a notification
    echo "\nTesting FCM notification...\n";
    $fcmService = app(\App\Services\FCMService::class);
    
    // Create a test notification
    $notification = new \App\Models\Notification([
        'title' => 'SSL Test Notification',
        'message' => 'Testing after SSL certificate fix',
        'category' => 'General',
        'severity' => 'medium',
        'sent' => false
    ]);
    $notification->save();
    
    // Get all active tokens
    $tokens = \App\Models\DeviceToken::where('is_active', true)->pluck('token')->toArray();
    
    if (empty($tokens)) {
        echo "No active device tokens found. Please register a device token first.\n";
    } else {
        echo "Found " . count($tokens) . " active device tokens.\n";
        
        // Send notification
        $result = $fcmService->sendNotification($notification, $tokens);
        
        echo "Result: " . ($result['success'] ? 'Success' : 'Failure') . "\n";
        echo "Message: " . $result['message'] . "\n";
        echo "Success count: " . $result['success_count'] . "\n";
        echo "Failure count: " . $result['failure_count'] . "\n";
        echo "Invalid tokens: " . $result['invalid_tokens'] . "\n";
    }
}