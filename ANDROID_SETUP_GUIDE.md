# WebAlerto Android Setup Guide

This guide will help you set up the WebAlerto disaster alert system in your new Android project.

## System Overview

**WebAlerto** is a disaster alert/emergency management system with:
- **<PERSON><PERSON> Backend** (WebAlerto-1) with Firebase Cloud Messaging
- **Ionic Angular Frontend** (mobile_ionic) with real-time notifications
- **Features**: Disaster alerts, evacuation centers, maps, push notifications

## Setup Options

### Option 1: Use Existing Ionic Project (Recommended)

#### Step 1: Copy the Ionic Project
```bash
# Copy the mobile_ionic folder to your new project location
cp -r mobile_ionic /path/to/your/new/project/
cd /path/to/your/new/project/mobile_ionic
```

#### Step 2: Install Dependencies
```bash
npm install
```

#### Step 3: Set Up Firebase for Your New Project

1. **Create a new Firebase project** at https://console.firebase.google.com
2. **Add an Android app** to your Firebase project
3. **Download google-services.json** and place it in:
   - Root of mobile_ionic folder
   - `mobile_ionic/android/app/` folder

#### Step 4: Update Firebase Configuration

Update `src/environments/environment.ts`:
```typescript
export const environment = {
  production: false,
  apiUrl: 'http://YOUR_COMPUTER_IP:8000/api', // Update with your IP
  orsApiKey: 'YOUR_OPENROUTESERVICE_API_KEY',
  firebase: {
    projectId: 'YOUR_FIREBASE_PROJECT_ID',
    messagingSenderId: 'YOUR_MESSAGING_SENDER_ID',
    appId: 'YOUR_ANDROID_APP_ID',
    apiKey: 'YOUR_FIREBASE_API_KEY'
  }
};
```

#### Step 5: Update Backend Configuration

Update `WebAlerto-1/.env`:
```env
# Firebase Configuration - Update with your new project
FIREBASE_CREDENTIALS=firebase-service-account.json
FIREBASE_PROJECT_ID=YOUR_FIREBASE_PROJECT_ID
FIREBASE_DATABASE_URL=https://YOUR_PROJECT_ID-default-rtdb.firebaseio.com
FIREBASE_DYNAMIC_LINKS_DEFAULT_DOMAIN=YOUR_PROJECT_ID.page.link
FIREBASE_STORAGE_DEFAULT_BUCKET=YOUR_PROJECT_ID.firebasestorage.app
FIREBASE_MESSAGING_SENDER_ID=YOUR_MESSAGING_SENDER_ID
FIREBASE_APP_ID=YOUR_ANDROID_APP_ID
```

#### Step 6: Build and Run
```bash
# Build the Ionic app
ionic build

# Add Android platform
ionic capacitor add android

# Copy google-services.json to Android app
cp google-services.json android/app/

# Build Android app
ionic capacitor build android

# Open in Android Studio
ionic capacitor open android
```

### Option 2: Create Native Android App

If you prefer a native Android app, follow these steps:

#### Step 1: Add Firebase to Your Android Project

1. **Add Firebase SDK** to your `app/build.gradle`:
```gradle
dependencies {
    implementation 'com.google.firebase:firebase-messaging:23.4.0'
    implementation 'com.google.firebase:firebase-analytics:21.5.0'
}
```

2. **Add Google Services plugin** to your project-level `build.gradle`:
```gradle
buildscript {
    dependencies {
        classpath 'com.google.gms:google-services:4.4.0'
    }
}
```

3. **Apply plugin** in your app-level `build.gradle`:
```gradle
apply plugin: 'com.google.gms.google-services'
```

#### Step 2: Create FCM Service

Create `MyFirebaseMessagingService.java`:
```java
public class MyFirebaseMessagingService extends FirebaseMessagingService {
    @Override
    public void onMessageReceived(RemoteMessage remoteMessage) {
        // Handle FCM messages here
        if (remoteMessage.getNotification() != null) {
            showNotification(remoteMessage.getNotification().getTitle(),
                           remoteMessage.getNotification().getBody());
        }
    }

    @Override
    public void onNewToken(String token) {
        // Send token to your Laravel backend
        sendTokenToServer(token);
    }
}
```

#### Step 3: Add Permissions and Service to AndroidManifest.xml
```xml
<uses-permission android:name="android.permission.INTERNET" />
<uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
<uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />

<service
    android:name=".MyFirebaseMessagingService"
    android:exported="false">
    <intent-filter>
        <action android:name="com.google.firebase.MESSAGING_EVENT" />
    </intent-filter>
</service>
```

## Backend Setup (Laravel)

### Step 1: Install Dependencies
```bash
cd WebAlerto-1
composer install
```

### Step 2: Set Up Database
```bash
php artisan migrate
php artisan db:seed
```

### Step 3: Configure Firebase Service Account

1. **Download service account key** from Firebase Console
2. **Place it** in `storage/firebase-service-account.json`
3. **Update .env** with your Firebase configuration

### Step 4: Start Laravel Server
```bash
php artisan serve --host=0.0.0.0 --port=8000
```

## Testing the Setup

### Test FCM Notifications
```bash
# From WebAlerto-1 directory
php test-fcm-notification.php YOUR_FCM_TOKEN
```

### Test API Endpoints
```bash
# Test device token registration
curl -X POST http://YOUR_IP:8000/api/device-token \
  -H "Content-Type: application/json" \
  -d '{"token": "YOUR_FCM_TOKEN", "device_type": "android"}'
```

## Key Features to Implement

1. **Push Notifications**: Real-time disaster alerts
2. **Map Integration**: Show evacuation centers and routes
3. **User Authentication**: Login/register functionality
4. **Disaster Categories**: Earthquake, typhoon, flood, etc.
5. **Location Services**: GPS tracking and geofencing
6. **Offline Support**: Cache important data

#### Step 4: Implement API Communication

Create `ApiService.java` to communicate with Laravel backend:
```java
public class ApiService {
    private static final String BASE_URL = "http://YOUR_IP:8000/api/";
    private OkHttpClient client = new OkHttpClient();

    public void registerDeviceToken(String token, ApiCallback callback) {
        JSONObject json = new JSONObject();
        try {
            json.put("token", token);
            json.put("device_type", "android");
        } catch (JSONException e) {
            e.printStackTrace();
        }

        RequestBody body = RequestBody.create(
            json.toString(),
            MediaType.get("application/json; charset=utf-8")
        );

        Request request = new Request.Builder()
            .url(BASE_URL + "device-token")
            .post(body)
            .build();

        client.newCall(request).enqueue(new Callback() {
            @Override
            public void onResponse(Call call, Response response) {
                callback.onSuccess(response.body().string());
            }

            @Override
            public void onFailure(Call call, IOException e) {
                callback.onError(e.getMessage());
            }
        });
    }

    public void getEvacuationCenters(ApiCallback callback) {
        Request request = new Request.Builder()
            .url(BASE_URL + "evacuation-centers")
            .build();

        client.newCall(request).enqueue(new Callback() {
            @Override
            public void onResponse(Call call, Response response) {
                callback.onSuccess(response.body().string());
            }

            @Override
            public void onFailure(Call call, IOException e) {
                callback.onError(e.getMessage());
            }
        });
    }
}

interface ApiCallback {
    void onSuccess(String response);
    void onError(String error);
}
```

#### Step 5: Create Main Activity

Create `MainActivity.java`:
```java
public class MainActivity extends AppCompatActivity {
    private ApiService apiService;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_main);

        apiService = new ApiService();

        // Initialize Firebase
        FirebaseApp.initializeApp(this);

        // Get FCM token
        FirebaseMessaging.getInstance().getToken()
            .addOnCompleteListener(new OnCompleteListener<String>() {
                @Override
                public void onComplete(@NonNull Task<String> task) {
                    if (!task.isSuccessful()) {
                        Log.w("FCM", "Fetching FCM registration token failed", task.getException());
                        return;
                    }

                    String token = task.getResult();
                    Log.d("FCM", "FCM Registration Token: " + token);

                    // Register token with backend
                    registerTokenWithBackend(token);
                }
            });
    }

    private void registerTokenWithBackend(String token) {
        apiService.registerDeviceToken(token, new ApiCallback() {
            @Override
            public void onSuccess(String response) {
                Log.d("API", "Token registered successfully: " + response);
            }

            @Override
            public void onError(String error) {
                Log.e("API", "Failed to register token: " + error);
            }
        });
    }
}
```

#### Step 6: Add Required Dependencies

Add to your `app/build.gradle`:
```gradle
dependencies {
    implementation 'com.google.firebase:firebase-messaging:23.4.0'
    implementation 'com.google.firebase:firebase-analytics:21.5.0'
    implementation 'com.squareup.okhttp3:okhttp:4.12.0'
    implementation 'org.osmdroid:osmdroid-android:6.1.17' // For maps
    implementation 'androidx.recyclerview:recyclerview:1.3.2'
    implementation 'androidx.cardview:cardview:1.0.0'
    implementation 'com.google.android.material:material:1.11.0'
}
```

## Complete Implementation Example

### 1. Create Disaster Alert Model
```java
public class DisasterAlert {
    private String title;
    private String message;
    private String category;
    private String severity;
    private long timestamp;

    // Constructors, getters, and setters
}
```

### 2. Create Notification Handler
```java
public class NotificationHelper {
    private static final String CHANNEL_ID = "disaster_alerts";
    private Context context;

    public NotificationHelper(Context context) {
        this.context = context;
        createNotificationChannel();
    }

    private void createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            CharSequence name = "Disaster Alerts";
            String description = "Emergency disaster notifications";
            int importance = NotificationManager.IMPORTANCE_HIGH;
            NotificationChannel channel = new NotificationChannel(CHANNEL_ID, name, importance);
            channel.setDescription(description);

            NotificationManager notificationManager = context.getSystemService(NotificationManager.class);
            notificationManager.createNotificationChannel(channel);
        }
    }

    public void showNotification(String title, String message, String severity) {
        Intent intent = new Intent(context, MainActivity.class);
        PendingIntent pendingIntent = PendingIntent.getActivity(context, 0, intent, PendingIntent.FLAG_IMMUTABLE);

        NotificationCompat.Builder builder = new NotificationCompat.Builder(context, CHANNEL_ID)
            .setSmallIcon(R.drawable.ic_alert)
            .setContentTitle(title)
            .setContentText(message)
            .setPriority(getSeverityPriority(severity))
            .setContentIntent(pendingIntent)
            .setAutoCancel(true);

        NotificationManagerCompat notificationManager = NotificationManagerCompat.from(context);
        notificationManager.notify(1, builder.build());
    }

    private int getSeverityPriority(String severity) {
        switch (severity) {
            case "high": return NotificationCompat.PRIORITY_HIGH;
            case "medium": return NotificationCompat.PRIORITY_DEFAULT;
            case "low": return NotificationCompat.PRIORITY_LOW;
            default: return NotificationCompat.PRIORITY_DEFAULT;
        }
    }
}
```

## Next Steps

1. Choose your preferred option (Ionic or Native Android)
2. Set up Firebase project and configuration
3. Update API endpoints and Firebase credentials
4. Test the notification system
5. Customize the UI for your specific needs

## Quick Start Commands

### For Ionic Approach:
```bash
cd mobile_ionic
npm install
ionic capacitor add android
ionic capacitor build android
ionic capacitor open android
```

### For Native Android:
1. Create new Android Studio project
2. Add Firebase configuration
3. Implement the code examples above
4. Test with your Laravel backend

Would you like me to help you with any specific step or create additional implementation files?
