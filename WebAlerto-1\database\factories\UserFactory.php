<?php

namespace Database\Factories;

use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

class UserFactory extends Factory
{
    protected $model = User::class;

    public function definition()
    {
        $barangays = [
            'Mabolo',
            'San Antonio',
            'San Jose',
            '<PERSON>',
            'Lahug',
            'Capitol Site',
        ];

        $positions = [
            'Chairman',
            'Disaster Risk Reduction Officer',
            'Emergency Response Coordinator',
            'Community Resilience Officer',
            'Disaster Management Specialist',
        ];

        return [
            'full_name' => $this->faker->name(),
            'email' => $this->faker->unique()->safeEmail(),
            'password' => bcrypt('password'),
            'position' => $this->faker->randomElement($positions),
            'barangay' => $this->faker->randomElement($barangays),
            'created_at' => now(),
            'updated_at' => now(),
        ];
    }

    public function chairman()
    {
        return $this->state(function (array $attributes) {
            return [
                'position' => 'Chairman',
            ];
        });
    }
}