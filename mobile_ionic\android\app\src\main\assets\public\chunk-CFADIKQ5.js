import{a as G}from"./chunk-25KYI4JX.js";import{$a as L,A as I,Ab as z,C as w,D as p,Db as Q,E as v,F as e,G as n,H as m,I as x,J as g,K as h,M as o,Ma as T,N as f,Na as O,O as b,Oa as M,Pa as R,Qa as $,Ra as U,Sa as F,Ta as D,V as k,W as A,Wa as B,X as y,ab as V,cb as j,ea as P,fb as N,gb as q,q as E,r as S,ub as K,vb as W,y as c,z as _,zb as H}from"./chunk-YFIZFQXH.js";import"./chunk-EFGZ4QZO.js";import"./chunk-VKNYRYKJ.js";import"./chunk-YLGV66M3.js";import"./chunk-VD4VFVTQ.js";import"./chunk-HC6MZPB3.js";import"./chunk-IP3IRUG3.js";import"./chunk-UKIOCGZG.js";import"./chunk-RMJ7PCZJ.js";import"./chunk-K54AU7WQ.js";import"./chunk-VBU7NMPV.js";import"./chunk-JIZJ6CAM.js";import"./chunk-MCRJI3T3.js";import"./chunk-6OW47QMA.js";import"./chunk-HBC3IQSD.js";import"./chunk-RXV5ZBOB.js";import"./chunk-ODNE7IRY.js";import"./chunk-F4H6ZFEG.js";import"./chunk-JWIEPCRG.js";import"./chunk-WTKF7BA6.js";import"./chunk-SV7S5NYR.js";import"./chunk-WTCPO44B.js";import"./chunk-L5T6STQ3.js";import"./chunk-3EJRMEWO.js";import"./chunk-GNOVVPTF.js";import"./chunk-BAKMWPBW.js";import"./chunk-5OMUW5VI.js";import"./chunk-OBXDPQ3V.js";import{h as u}from"./chunk-LNJ3S2LQ.js";function X(s,d){if(s&1&&(e(0,"p"),o(1),n()),s&2){let a=h().$implicit;v("success-message",a.testResult.success)("error-message",!a.testResult.success),c(),b(" ",a.testResult.message," ")}}function Y(s,d){if(s&1){let a=x();e(0,"ion-button",19),g("click",function(i){E(a);let r=h().$implicit;return h().selectEndpoint(r),S(i.stopPropagation())}),m(1,"ion-icon",23),n()}}function Z(s,d){s&1&&(e(0,"ion-badge",24),o(1," Active "),n())}function tt(s,d){if(s&1){let a=x();e(0,"ion-item",14),m(1,"ion-icon",15),e(2,"ion-label")(3,"h2"),o(4),n(),e(5,"p"),o(6),n(),e(7,"p",16),o(8),n(),w(9,X,2,5,"p",17),n(),e(10,"ion-buttons",18)(11,"ion-button",19),g("click",function(i){let r=E(a).$implicit;return h().testEndpoint(r),S(i.stopPropagation())}),m(12,"ion-icon",20),n(),w(13,Y,2,0,"ion-button",21)(14,Z,2,0,"ion-badge",22),n()()}if(s&2){let a=d.$implicit,t=h();v("active-endpoint",a.isActive),c(),p("name",t.getStatusIcon(a))("color",t.getStatusColor(a)),c(3),f(a.name),c(2),f(a.description),c(2),f(a.url),c(),p("ngIf",a.testResult),c(4),p("ngIf",!a.isActive),c(),p("ngIf",a.isActive)}}var lt=(()=>{let d=class d{constructor(t,i,r,l){this.envSwitcher=t,this.alertController=i,this.loadingController=r,this.router=l,this.endpoints=[],this.currentApiUrl="",this.isLoading=!1}ngOnInit(){this.loadEndpoints(),this.currentApiUrl=this.envSwitcher.getCurrentApiUrl()}loadEndpoints(){this.endpoints=this.envSwitcher.getApiEndpoints()}selectEndpoint(t){return u(this,null,function*(){yield(yield this.alertController.create({header:"Switch API Endpoint",message:`Switch to ${t.name}?

${t.description}`,buttons:[{text:"Cancel",role:"cancel"},{text:"Switch",handler:()=>{this.envSwitcher.setApiUrl(t.url),this.currentApiUrl=t.url,this.loadEndpoints(),this.presentSuccessAlert("API endpoint switched successfully!")}}]})).present()})}testEndpoint(t){return u(this,null,function*(){let i=yield this.loadingController.create({message:`Testing ${t.name}...`,duration:1e4});yield i.present();try{let r=yield this.envSwitcher.testEndpoint(t.url),l=this.endpoints.findIndex(J=>J.url===t.url);l!==-1&&(this.endpoints[l].testResult=r),yield i.dismiss(),yield(yield this.alertController.create({header:"Connection Test",message:`${t.name}

${r.message}`,buttons:["OK"]})).present()}catch{yield i.dismiss(),this.presentErrorAlert("Test failed","Unable to test endpoint")}})}testAllEndpoints(){return u(this,null,function*(){let t=yield this.loadingController.create({message:"Testing all endpoints...",duration:3e4});yield t.present();try{let i=yield this.envSwitcher.testAllEndpoints();this.endpoints=i,yield t.dismiss();let r=i.filter(l=>l.testResult.success);if(r.length>0){let l=`Found ${r.length} working endpoint(s):

`+r.map(C=>`\u2705 ${C.name}`).join(`
`);this.presentSuccessAlert(l)}else this.presentErrorAlert("No Working Endpoints","All endpoints failed connectivity test")}catch{yield t.dismiss(),this.presentErrorAlert("Test Failed","Unable to test endpoints")}})}autoDetect(){return u(this,null,function*(){let t=yield this.loadingController.create({message:"Auto-detecting best endpoint...",duration:3e4});yield t.present();try{let i=yield this.envSwitcher.autoDetectBestEndpoint();yield t.dismiss(),i?(this.currentApiUrl=i.url,this.loadEndpoints(),this.presentSuccessAlert(`Auto-detected and switched to: ${i.name}`)):this.presentErrorAlert("Auto-Detection Failed","No working endpoints found")}catch{yield t.dismiss(),this.presentErrorAlert("Auto-Detection Failed","Unable to detect working endpoint")}})}getStatusIcon(t){return t.testResult?t.testResult.success?"checkmark-circle":"close-circle":"help-circle-outline"}getStatusColor(t){return t.testResult?t.testResult.success?"success":"danger":"medium"}goBack(){this.router.navigate(["/login"])}presentSuccessAlert(t){return u(this,null,function*(){yield(yield this.alertController.create({header:"Success",message:t,buttons:["OK"]})).present()})}presentErrorAlert(t,i){return u(this,null,function*(){yield(yield this.alertController.create({header:t,message:i,buttons:["OK"]})).present()})}};d.\u0275fac=function(i){return new(i||d)(_(G),_(H),_(z),_(P))},d.\u0275cmp=I({type:d,selectors:[["app-environment-switcher"]],decls:65,vars:4,consts:[[3,"translucent"],["slot","start"],[3,"click"],["name","chevron-back-outline"],[1,"ion-padding",3,"fullscreen"],[1,"switcher-container"],["expand","block","fill","outline",3,"click"],["name","search-outline","slot","start"],["expand","block",3,"click"],["name","flash-outline","slot","start"],["button","",3,"active-endpoint",4,"ngFor","ngForOf"],["name","wifi-outline","slot","start","color","primary"],["name","home-outline","slot","start","color","warning"],["name","desktop-outline","slot","start","color","medium"],["button",""],["slot","start",3,"name","color"],[1,"endpoint-url"],[3,"success-message","error-message",4,"ngIf"],["slot","end"],["fill","clear",3,"click"],["name","refresh-outline"],["fill","clear",3,"click",4,"ngIf"],["color","primary",4,"ngIf"],["name","checkmark-outline"],["color","primary"]],template:function(i,r){i&1&&(e(0,"ion-header",0)(1,"ion-toolbar")(2,"ion-buttons",1)(3,"ion-button",2),g("click",function(){return r.goBack()}),m(4,"ion-icon",3),n()(),e(5,"ion-title"),o(6,"API Endpoint Switcher"),n()()(),e(7,"ion-content",4)(8,"div",5)(9,"ion-card")(10,"ion-card-header")(11,"ion-card-title"),o(12,"Current API Endpoint"),n()(),e(13,"ion-card-content")(14,"p")(15,"strong"),o(16,"URL:"),n(),o(17),n(),e(18,"ion-button",6),g("click",function(){return r.autoDetect()}),m(19,"ion-icon",7),o(20," Auto-Detect Best Endpoint "),n()()(),e(21,"ion-card")(22,"ion-card-header")(23,"ion-card-title"),o(24,"Quick Actions"),n()(),e(25,"ion-card-content")(26,"ion-button",8),g("click",function(){return r.testAllEndpoints()}),m(27,"ion-icon",9),o(28," Test All Endpoints "),n()()(),e(29,"ion-card")(30,"ion-card-header")(31,"ion-card-title"),o(32,"Available Endpoints"),n(),e(33,"ion-card-subtitle"),o(34,"Select an endpoint to switch to"),n()(),e(35,"ion-card-content")(36,"ion-list"),w(37,tt,15,10,"ion-item",10),n()()(),e(38,"ion-card")(39,"ion-card-header")(40,"ion-card-title"),o(41,"Troubleshooting Tips"),n()(),e(42,"ion-card-content")(43,"ion-list")(44,"ion-item"),m(45,"ion-icon",11),e(46,"ion-label")(47,"h3"),o(48,"ngrok (Recommended)"),n(),e(49,"p"),o(50,"Most reliable for device testing. Works from anywhere."),n()()(),e(51,"ion-item"),m(52,"ion-icon",12),e(53,"ion-label")(54,"h3"),o(55,"Local IP"),n(),e(56,"p"),o(57,"Requires same WiFi network and firewall configuration."),n()()(),e(58,"ion-item"),m(59,"ion-icon",13),e(60,"ion-label")(61,"h3"),o(62,"Localhost"),n(),e(63,"p"),o(64,"Only works in web browser, not on mobile devices."),n()()()()()()()()),i&2&&(p("translucent",!0),c(7),p("fullscreen",!0),c(10),b(" ",r.currentApiUrl,""),c(20),p("ngForOf",r.endpoints))},dependencies:[Q,T,O,M,R,$,U,F,D,B,L,V,j,N,q,K,W,y,k,A],styles:[".switcher-container[_ngcontent-%COMP%]{max-width:600px;margin:0 auto}.active-endpoint[_ngcontent-%COMP%]{--background: var(--ion-color-primary-tint);--border-color: var(--ion-color-primary);border-left:4px solid var(--ion-color-primary)}.endpoint-url[_ngcontent-%COMP%]{font-family:monospace;font-size:.8em;color:var(--ion-color-medium);word-break:break-all}.success-message[_ngcontent-%COMP%]{color:var(--ion-color-success);font-weight:500}.error-message[_ngcontent-%COMP%]{color:var(--ion-color-danger);font-weight:500}ion-card[_ngcontent-%COMP%]{margin-bottom:16px}ion-card-title[_ngcontent-%COMP%]{color:var(--ion-color-primary)}ion-item[_ngcontent-%COMP%]{--padding-start: 16px;--padding-end: 16px}ion-button[_ngcontent-%COMP%]{--border-radius: 8px}.quick-actions[_ngcontent-%COMP%]{display:flex;gap:8px;margin-bottom:16px}.quick-actions[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%]{flex:1}"]});let s=d;return s})();export{lt as EnvironmentSwitcherPage};
