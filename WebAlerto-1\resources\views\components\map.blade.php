@extends('layout.app')

@section('title', 'Evacuation Centers Map')

@section('content')
<div class="min-h-screen bg-gradient-to-br from-gray-50 to-blue-50">
    <!-- Top Navigation -->
    <nav class="bg-white/80 backdrop-blur-sm shadow-sm">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <div class="flex-shrink-0 flex items-center">
                        <svg class="h-8 w-8 text-sky-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"/>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"/>
                        </svg>
                        <span class="ml-2 text-xl font-bold text-gray-900">Evacuation Centers Map</span>
                    </div>
                </div>
                <div class="flex items-center">
                    <div class="flex items-center space-x-4">
                        <button class="bg-white text-gray-700 px-4 py-2 rounded-lg text-sm font-medium border border-gray-300 hover:bg-gray-50 transition-colors flex items-center gap-2">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"/>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
                            </svg>
                            Settings
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <!-- Map Header -->
        <div class="mb-8">
            <div class="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900">Evacuation Centers Map</h1>
                    <p class="mt-1 text-sm text-gray-500">View and manage evacuation centers in your area</p>
                </div>
                <div class="flex items-center gap-4">
                    <div class="bg-white/80 backdrop-blur-sm rounded-xl shadow-sm p-4 border border-gray-100">
                        <div class="flex items-center gap-3">
                            <div class="p-2 bg-sky-100 rounded-lg">
                                <svg class="w-6 h-6 text-sky-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"/>
                                </svg>
                            </div>
                            <div>
                                <p class="text-sm font-medium text-sky-600">Total Centers</p>
                                <p class="text-2xl font-bold text-sky-700" id="total-centers">0</p>
                            </div>
                        </div>
                    </div>
                    <div class="bg-white/80 backdrop-blur-sm rounded-xl shadow-sm p-4 border border-gray-100">
                        <div class="flex items-center gap-3">
                            <div class="p-2 bg-sky-100 rounded-lg">
                                <svg class="w-6 h-6 text-sky-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                </svg>
                            </div>
                            <div>
                                <p class="text-sm font-medium text-sky-600">Active Centers</p>
                                <p class="text-2xl font-bold text-sky-700" id="active-centers">0</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Controls Section -->
        <div class="mb-8 grid grid-cols-1 md:grid-cols-4 gap-4">
            <!-- Search Container -->
            <div class="col-span-1 md:col-span-2 bg-white/80 backdrop-blur-sm rounded-xl shadow-sm p-4 border border-gray-100">
                <label class="block text-sm font-medium text-gray-700 mb-2">Search Center</label>
                <div class="relative">
                    <input
                        type="text"
                        id="search"
                        placeholder="Enter center name or address..."
                        class="w-full px-4 py-3 border border-gray-200 rounded-xl shadow-sm focus:ring-2 focus:ring-blue-300 focus:border-blue-300 focus:outline-none transition-all font-medium"
                    >
                    <svg class="absolute right-3 top-3 h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
                    </svg>
                </div>
            </div>

            <!-- Filter Container -->
            <div class="bg-white/80 backdrop-blur-sm rounded-xl shadow-sm p-4 border border-gray-100">
                <label class="block text-sm font-medium text-gray-700 mb-2">Filter by Status</label>
                <select
                    id="status-filter"
                    class="w-full px-4 py-3 border border-gray-200 rounded-xl shadow-sm focus:ring-2 focus:ring-blue-300 focus:border-blue-300 focus:outline-none transition-all font-medium"
                >
                    <option value="All">All Status</option>
                    <option value="Active">Active</option>
                    <option value="Full">Full</option>
                </select>
            </div>

            <!-- Disaster Type Filter -->
            <div class="bg-white/80 backdrop-blur-sm rounded-xl shadow-sm p-4 border border-gray-100">
                <label class="block text-sm font-medium text-gray-700 mb-2">Disaster Type</label>
                <select
                    id="disaster-filter"
                    class="w-full px-4 py-3 border border-gray-200 rounded-xl shadow-sm focus:ring-2 focus:ring-blue-300 focus:border-blue-300 focus:outline-none transition-all font-medium"
                >
                    <option value="All">All Disasters</option>
                    <option value="Typhoon">Typhoon (Red)</option>
                    <option value="Flood">Flood (Green)</option>
                    <option value="Earthquake">Earthquake (Orange)</option>
                </select>
            </div>
        </div>

        <!-- Loading Spinner -->
        <div id="loading" class="text-center my-6 hidden">
            <div class="w-12 h-12 border-4 border-blue-300 border-t-transparent rounded-full animate-spin mx-auto"></div>
            <p class="text-sm mt-3 text-gray-500 font-medium">Loading markers...</p>
        </div>

        <!-- Main Content Grid -->
        <div class="grid grid-cols-1 gap-6">
            <!-- Map Container -->
            <div class="col-span-1">
                <div class="bg-white/80 backdrop-blur-sm rounded-xl shadow-xl p-4 border border-gray-100">
                    <div class="flex items-center justify-between mb-4">
                        <h2 class="text-lg font-semibold text-sky-700">Evacuation Centers Map</h2>
                        <div class="flex items-center gap-2">
                            <button id="reset-map" class="p-2 text-sky-500 hover:text-sky-600 rounded-lg hover:bg-sky-50 transition-colors">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"/>
                                </svg>
                            </button>
                        </div>
                    </div>
                    <div id="map" class="rounded-xl shadow-lg h-[700px] w-full overflow-hidden"></div>
                </div>
            </div>
        </div>

        <!-- Legend -->
        <div class="mt-6 bg-white/80 backdrop-blur-sm rounded-xl shadow-sm p-4 border border-gray-100">
            <h3 class="text-sm font-medium text-gray-700 mb-2">Legend</h3>
            <div class="flex flex-wrap gap-4">
                <div class="flex items-center">
                    <div class="w-4 h-4 rounded-full bg-red-500 mr-2"></div>
                    <span class="text-sm text-gray-600">Typhoon Centers</span>
                </div>
                <div class="flex items-center">
                    <div class="w-4 h-4 rounded-full bg-green-500 mr-2"></div>
                    <span class="text-sm text-gray-600">Flood Centers</span>
                </div>
                <div class="flex items-center">
                    <div class="w-4 h-4 rounded-full bg-orange-500 mr-2"></div>
                    <span class="text-sm text-gray-600">Earthquake Centers</span>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@section('styles')
<link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css"
      integrity="sha256-o9N1j+n1D2dLkUobZLCOoMOGMNF9OL8W8BO6FhJdguI=" crossorigin=""/>
<link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700&display=swap" rel="stylesheet">
<style>
    body {
        font-family: 'Poppins', sans-serif;
    }
    #map {
        height: 700px !important;
        width: 100% !important;
        z-index: 1;
    }
    .leaflet-container {
        position: relative !important;
        outline: none;
    }
    .leaflet-popup-content {
        margin: 0;
        padding: 0.75rem;
        font-family: 'Poppins', sans-serif;
    }
    .leaflet-popup-content-wrapper {
        border-radius: 0.75rem;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    }
    .leaflet-popup-tip {
        background: white;
    }
    .leaflet-popup-content h3 {
        font-weight: 600;
        color: #0ea5e9;
        margin-bottom: 0.5rem;
    }
    .leaflet-popup-content p {
        margin-bottom: 0.25rem;
        color: #4b5563;
    }
    .leaflet-tile-pane {
        filter: grayscale(0.2) brightness(0.95);
    }
    .leaflet-control-zoom {
        border: none !important;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1) !important;
    }
    .leaflet-control-zoom a {
        background-color: white !important;
        border: none !important;
        color: #4b5563 !important;
    }
    .leaflet-control-zoom a:hover {
        background-color: #f0f9ff !important;
    }
    .leaflet-marker-icon {
        filter: drop-shadow(0 2px 4px rgba(0,0,0,0.1));
    }
</style>
@endsection

@section('scripts')
<script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"
        integrity="sha256-V1c7cAYM3vzT4Uj4iURWeeK6fsvx6v+h5VoEVquT3T8=" crossorigin=""></script>
<script src="https://unpkg.com/leaflet.markercluster@1.4.1/dist/leaflet.markercluster.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function () {
    // Initialize map centered on Cebu City
    const map = L.map('map').setView([10.3157, 123.8854], 13);

    // Add OpenStreetMap tiles
    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
        attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors',
        maxZoom: 19,
    }).addTo(map);

    // Get centers data
    const existingCenters = @json($centers);
    let currentMarkers = [];
    let markerCluster;

    const loading = document.getElementById('loading');
    const searchInput = document.getElementById('search');
    const statusFilter = document.getElementById('status-filter');
    const disasterFilter = document.getElementById('disaster-filter');
    const totalCenters = document.getElementById('total-centers');
    const activeCenters = document.getElementById('active-centers');

    // Initialize marker cluster group
    markerCluster = L.markerClusterGroup({
        maxClusterRadius: 50,
        spiderfyOnMaxZoom: true,
        showCoverageOnHover: false,
        zoomToBoundsOnClick: true
    });

    // Marker icons
    const markerIcons = {
        'Typhoon': L.divIcon({
            html: `<div style="background-color: #ef4444; width: 24px; height: 24px; border-radius: 50%; border: 2px solid white;"></div>`,
            className: 'custom-div-icon',
            iconSize: [24, 24],
            iconAnchor: [12, 12]
        }),
        'Flood': L.divIcon({
            html: `<div style="background-color: #22c55e; width: 24px; height: 24px; border-radius: 50%; border: 2px solid white;"></div>`,
            className: 'custom-div-icon',
            iconSize: [24, 24],
            iconAnchor: [12, 12]
        }),
        'Earthquake': L.divIcon({
            html: `<div style="background-color: #f97316; width: 24px; height: 24px; border-radius: 50%; border: 2px solid white;"></div>`,
            className: 'custom-div-icon',
            iconSize: [24, 24],
            iconAnchor: [12, 12]
        })
    };

    function getDirections(destLat, destLng) {
        if ("geolocation" in navigator) {
            navigator.geolocation.getCurrentPosition(
                function(position) {
                    const userLat = position.coords.latitude;
                    const userLng = position.coords.longitude;
                    const url = `https://www.google.com/maps/dir/?api=1&origin=${userLat},${userLng}&destination=${destLat},${destLng}`;
                    window.open(url, '_blank');
                },
                function(error) {
                    console.error("Geolocation error:", error);
                    const url = `https://www.google.com/maps/dir/?api=1&destination=${destLat},${destLng}`;
                    window.open(url, '_blank');
                }
            );
        } else {
            const url = `https://www.google.com/maps/dir/?api=1&destination=${destLat},${destLng}`;
            window.open(url, '_blank');
        }
    }

    function updateStats(filtered) {
        const active = filtered.filter(center => center.status === 'Active').length;
        totalCenters.textContent = filtered.length;
        activeCenters.textContent = active;
    }

    function clearMarkers() {
        if (markerCluster) {
            map.removeLayer(markerCluster);
        }
        markerCluster = L.markerClusterGroup({
            maxClusterRadius: 50,
            spiderfyOnMaxZoom: true,
            showCoverageOnHover: false,
            zoomToBoundsOnClick: true
        });
        currentMarkers = [];
    }

    function showInfo(center) {
        map.setView([center.latitude, center.longitude], 16);
    }

    function filterCenters() {
        const query = searchInput.value.toLowerCase().trim();
        const status = statusFilter.value;
        const disaster = disasterFilter.value;

        return existingCenters.filter(center => {
            const matchesSearch = query === '' || 
                center.name.toLowerCase().includes(query) ||
                center.address.toLowerCase().includes(query);
            
            const matchesStatus = status === 'All' || center.status === status;
            const matchesDisaster = disaster === 'All' || center.disaster_type === disaster;
            
            return matchesSearch && matchesStatus && matchesDisaster;
        });
    }

    function updateMarkers(filtered) {
        clearMarkers();
        
        if (filtered.length === 0) {
            // Remove the info panel update since we removed the panel
        } else {
            filtered.forEach(center => {
                const markerIcon = markerIcons[center.disaster_type];
                
                const marker = L.marker([center.latitude, center.longitude], {
                    icon: markerIcon
                });

                marker.bindPopup(`
                    <div class="font-medium">
                        <h3 class="text-blue-800">${center.name}</h3>
                        <p class="text-gray-700">${center.address}</p>
                        <p class="text-gray-700">Capacity: ${center.capacity}</p>
                        <p class="text-gray-700">Contact: ${center.contact}</p>
                        <p class="text-gray-700">Status: 
                            <span class="px-2 py-1 rounded-full text-xs font-medium ${
                                center.status === 'Active' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                            }">${center.status}</span>
                        </p>
                        <p class="text-gray-700">Disaster Type: 
                            <span class="px-2 py-1 rounded-full text-xs font-medium ${
                                center.disaster_type === 'Typhoon' ? 'bg-red-100 text-red-800' :
                                center.disaster_type === 'Flood' ? 'bg-green-100 text-green-800' :
                                'bg-orange-100 text-orange-800'
                            }">${center.disaster_type}</span>
                        </p>
                        <button onclick="getDirections(${center.latitude}, ${center.longitude})"
                                class="mt-2 w-full bg-blue-600 text-white px-3 py-1 rounded text-sm hover:bg-blue-700 transition-colors">
                            Get Directions
                        </button>
                    </div>
                `);

                marker.on('click', () => {
                    showInfo(center);
                });

                markerCluster.addLayer(marker);
                currentMarkers.push(marker);
            });

            map.addLayer(markerCluster);
            updateStats(filtered);
        }
    }

    // Event Listeners
    let searchTimeout;
    searchInput.addEventListener('input', () => {
        clearTimeout(searchTimeout);
        searchTimeout = setTimeout(() => {
            updateMarkers(filterCenters());
        }, 300);
    });

    statusFilter.addEventListener('change', () => {
        updateMarkers(filterCenters());
    });

    disasterFilter.addEventListener('change', () => {
        updateMarkers(filterCenters());
    });

    document.getElementById('reset-map').addEventListener('click', () => {
        map.setView([10.3157, 123.8854], 13);
        updateMarkers(filterCenters());
    });

    // Make getDirections function globally available
    window.getDirections = getDirections;

    // Initial render
    updateMarkers(filterCenters());
});
</script>
@endsection

