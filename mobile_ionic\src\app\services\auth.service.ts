import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from '../../environments/environment';

@Injectable({ providedIn: 'root' })
export class AuthService {
  private apiUrl = `${environment.apiUrl}/auth`;

  constructor(private http: HttpClient) {
    console.log('Auth Service initialized with API URL:', this.apiUrl);
  }

  login(credentials: { email: string, password: string }): Observable<any> {
    return this.http.post(`${this.apiUrl}/login`, credentials);
  }

  register(data: { full_name: string, email: string, password: string, password_confirmation?: string }): Observable<any> {
    return this.http.post(`${this.apiUrl}/signup`, data);
  }

  setToken(token: string) {
    localStorage.setItem('access_token', token);
  }
}