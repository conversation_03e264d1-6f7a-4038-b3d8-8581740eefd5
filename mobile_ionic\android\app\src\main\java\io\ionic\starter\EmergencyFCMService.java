package io.ionic.starter;

import android.app.ActivityManager;
import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.PendingIntent;
import android.content.Context;
import android.content.Intent;
import android.graphics.Color;
import android.media.RingtoneManager;
import android.net.Uri;
import android.os.Build;
import android.util.Log;

import androidx.core.app.NotificationCompat;

import com.google.firebase.messaging.FirebaseMessagingService;
import com.google.firebase.messaging.RemoteMessage;

import java.util.Map;

public class EmergencyFCMService extends FirebaseMessagingService {
    private static final String TAG = "EmergencyFCMService";
    private static final String EMERGENCY_CHANNEL_ID = "emergency-alerts";
    private static final String GENERAL_CHANNEL_ID = "general-notifications";

    @Override
    public void onCreate() {
        super.onCreate();
        createNotificationChannels();
    }

    @Override
    public void onMessageReceived(RemoteMessage remoteMessage) {
        Log.d(TAG, "From: " + remoteMessage.getFrom());

        // Check if message contains data payload
        if (remoteMessage.getData().size() > 0) {
            Log.d(TAG, "Message data payload: " + remoteMessage.getData());

            Map<String, String> data = remoteMessage.getData();
            String category = data.get("category");
            String severity = data.get("severity");
            String title = data.get("title");
            String message = data.get("body");

            // Check if this is an emergency notification
            if (isEmergencyNotification(category, severity)) {
                Log.d(TAG, "Emergency notification detected - showing full screen alert");
                showEmergencyAlert(title, message, category, severity);
            } else {
                Log.d(TAG, "Regular notification - showing normal notification");
                showRegularNotification(title, message, category, severity);
            }
        }

        // Check if message contains a notification payload
        if (remoteMessage.getNotification() != null) {
            Log.d(TAG, "Message Notification Body: " + remoteMessage.getNotification().getBody());

            String title = remoteMessage.getNotification().getTitle();
            String body = remoteMessage.getNotification().getBody();

            // For notification payload, also check data for emergency status
            Map<String, String> data = remoteMessage.getData();
            String category = data.get("category");
            String severity = data.get("severity");

            if (isEmergencyNotification(category, severity)) {
                showEmergencyAlert(title, body, category, severity);
            } else {
                showRegularNotification(title, body, category, severity);
            }
        }
    }

    private boolean isEmergencyNotification(String category, String severity) {
        // Consider it emergency if:
        // 1. High severity, OR
        // 2. Disaster category (earthquake, flood, typhoon, fire)

        if ("high".equalsIgnoreCase(severity)) {
            return true;
        }

        if (category != null) {
            String cat = category.toLowerCase();
            return cat.contains("earthquake") || cat.contains("flood") ||
                   cat.contains("typhoon") || cat.contains("fire");
        }

        return false;
    }

    private void showEmergencyAlert(String title, String message, String category, String severity) {
        // Create intent for emergency alert activity
        Intent emergencyIntent = new Intent(this, EmergencyAlertActivity.class);
        emergencyIntent.putExtra("title", title);
        emergencyIntent.putExtra("message", message);
        emergencyIntent.putExtra("category", category);
        emergencyIntent.putExtra("severity", severity);
        emergencyIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK |
                                Intent.FLAG_ACTIVITY_CLEAR_TOP |
                                Intent.FLAG_ACTIVITY_SINGLE_TOP);

        try {
            // Only start the emergency alert activity if app is not in foreground
            // This prevents crashes when app is already running
            if (!isAppInForeground()) {
                startActivity(emergencyIntent);
            }
        } catch (Exception e) {
            Log.e(TAG, "Error starting emergency alert activity", e);
        }

        // Always create a high-priority notification as primary method
        createEmergencyNotification(title, message, category, emergencyIntent);
    }

    private void showRegularNotification(String title, String message, String category, String severity) {
        Intent intent = new Intent(this, MainActivity.class);
        intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP);

        PendingIntent pendingIntent = PendingIntent.getActivity(this, 0, intent,
                PendingIntent.FLAG_ONE_SHOT | PendingIntent.FLAG_IMMUTABLE);

        String channelId = GENERAL_CHANNEL_ID;
        Uri defaultSoundUri = RingtoneManager.getDefaultUri(RingtoneManager.TYPE_NOTIFICATION);

        NotificationCompat.Builder notificationBuilder =
                new NotificationCompat.Builder(this, channelId)
                        .setSmallIcon(R.drawable.ic_notification)
                        .setContentTitle(title)
                        .setContentText(message)
                        .setAutoCancel(true)
                        .setSound(defaultSoundUri)
                        .setContentIntent(pendingIntent)
                        .setPriority(NotificationCompat.PRIORITY_DEFAULT);

        // Set color based on category
        if (category != null) {
            notificationBuilder.setColor(getDisasterColor(category));
        }

        NotificationManager notificationManager =
                (NotificationManager) getSystemService(Context.NOTIFICATION_SERVICE);

        notificationManager.notify(0, notificationBuilder.build());
    }

    private void createEmergencyNotification(String title, String message, String category, Intent emergencyIntent) {
        PendingIntent pendingIntent = PendingIntent.getActivity(this, 0, emergencyIntent,
                PendingIntent.FLAG_UPDATE_CURRENT | PendingIntent.FLAG_IMMUTABLE);

        // Create full-screen intent for emergency
        PendingIntent fullScreenPendingIntent = PendingIntent.getActivity(this, 0, emergencyIntent,
                PendingIntent.FLAG_UPDATE_CURRENT | PendingIntent.FLAG_IMMUTABLE);

        NotificationCompat.Builder notificationBuilder =
                new NotificationCompat.Builder(this, EMERGENCY_CHANNEL_ID)
                        .setSmallIcon(R.drawable.ic_notification)
                        .setContentTitle(title)
                        .setContentText(message)
                        .setPriority(NotificationCompat.PRIORITY_MAX)
                        .setCategory(NotificationCompat.CATEGORY_ALARM)
                        .setFullScreenIntent(fullScreenPendingIntent, true)
                        .setAutoCancel(false)
                        .setOngoing(true)
                        .setContentIntent(pendingIntent)
                        .setColor(getDisasterColor(category))
                        .setVibrate(new long[]{0, 1000, 500, 1000})
                        .setLights(Color.RED, 3000, 3000);

        NotificationManager notificationManager =
                (NotificationManager) getSystemService(Context.NOTIFICATION_SERVICE);

        notificationManager.notify(1, notificationBuilder.build());
    }

    private int getDisasterColor(String category) {
        if (category == null) return Color.parseColor("#FFA500");

        switch (category.toLowerCase()) {
            case "earthquake":
                return Color.parseColor("#FFA500"); // Orange
            case "flood":
                return Color.parseColor("#0066CC"); // Blue
            case "typhoon":
                return Color.parseColor("#008000"); // Green
            case "fire":
                return Color.parseColor("#FF0000"); // Red
            default:
                return Color.parseColor("#FFA500"); // Default orange
        }
    }

    private void createNotificationChannels() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            // Emergency channel
            NotificationChannel emergencyChannel = new NotificationChannel(
                    EMERGENCY_CHANNEL_ID,
                    "Emergency Alerts",
                    NotificationManager.IMPORTANCE_HIGH);
            emergencyChannel.setDescription("Critical emergency notifications");
            emergencyChannel.enableLights(true);
            emergencyChannel.setLightColor(Color.RED);
            emergencyChannel.enableVibration(true);
            emergencyChannel.setVibrationPattern(new long[]{0, 1000, 500, 1000});
            emergencyChannel.setBypassDnd(true);
            emergencyChannel.setLockscreenVisibility(NotificationCompat.VISIBILITY_PUBLIC);

            // General channel
            NotificationChannel generalChannel = new NotificationChannel(
                    GENERAL_CHANNEL_ID,
                    "General Notifications",
                    NotificationManager.IMPORTANCE_DEFAULT);
            generalChannel.setDescription("General app notifications");

            NotificationManager notificationManager = getSystemService(NotificationManager.class);
            notificationManager.createNotificationChannel(emergencyChannel);
            notificationManager.createNotificationChannel(generalChannel);
        }
    }

    private boolean isAppInForeground() {
        try {
            ActivityManager activityManager = (ActivityManager) getSystemService(Context.ACTIVITY_SERVICE);
            if (activityManager != null) {
                for (ActivityManager.RunningAppProcessInfo processInfo : activityManager.getRunningAppProcesses()) {
                    if (processInfo.processName.equals(getPackageName()) &&
                        processInfo.importance == ActivityManager.RunningAppProcessInfo.IMPORTANCE_FOREGROUND) {
                        return true;
                    }
                }
            }
        } catch (Exception e) {
            Log.e(TAG, "Error checking if app is in foreground", e);
        }
        return false;
    }

    @Override
    public void onNewToken(String token) {
        Log.d(TAG, "Refreshed token: " + token);
        // Send token to server if needed
    }
}
