import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { IonicModule } from '@ionic/angular';
import { Router } from '@angular/router';

@Component({
  selector: 'app-loading',
  templateUrl: './loading.page.html',
  styleUrls: ['./loading.page.scss'],
  standalone: true,
  imports: [IonicModule, CommonModule, FormsModule]
})
export class LoadingPage implements OnInit {
  isOnline: boolean = false;

  constructor(private router: Router) {}

  ngOnInit() {
    // Add debug logging
    console.log('LoadingPage ngOnInit');
    const win = window as any;
    if (win.appDebug) {
      win.appDebug('LoadingPage ngOnInit');
    }
    this.checkInternetConnection();
  }

  checkInternetConnection() {
    this.isOnline = navigator.onLine;
    // Add debug logging
    console.log('LoadingPage checkInternetConnection, isOnline:', this.isOnline);
    const win = window as any;
    if (win.appDebug) {
      win.appDebug('LoadingPage checkInternetConnection, isOnline: ' + this.isOnline);
    }

    if (this.isOnline) {
      // Add debug logging
      if (win.appDebug) {
        win.appDebug('LoadingPage setting timeout to navigate to login');
      }
      setTimeout(() => {
        // Add debug logging
        if (win.appDebug) {
          win.appDebug('LoadingPage navigating to login');
        }
        this.router.navigate(['/login']);
      }, 3000); // Wait for 3 seconds before redirecting
    }
  }

  ionViewWillEnter() {
    // Add event listeners for online/offline status
    window.addEventListener('online', this.updateOnlineStatus.bind(this));
    window.addEventListener('offline', this.updateOnlineStatus.bind(this));
  }

  ionViewWillLeave() {
    // Remove event listeners
    window.removeEventListener('online', this.updateOnlineStatus.bind(this));
    window.removeEventListener('offline', this.updateOnlineStatus.bind(this));
  }

  private updateOnlineStatus() {
    this.isOnline = navigator.onLine;
    const win = window as any;
    if (win.appDebug) {
      win.appDebug('LoadingPage updateOnlineStatus, isOnline: ' + this.isOnline);
    }
    if (this.isOnline) {
      this.checkInternetConnection();
    }
  }
}