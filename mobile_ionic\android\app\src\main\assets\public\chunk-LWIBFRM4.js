import{a as g}from"./chunk-25KYI4JX.js";import{a as n}from"./chunk-FULEFYAM.js";import{$ as h,_ as p,m as c,o as r}from"./chunk-YFIZFQXH.js";import{a,b as l}from"./chunk-LNJ3S2LQ.js";var v=(()=>{let e=class e{get apiUrl(){var s;return`${((s=this.envSwitcher)==null?void 0:s.getCurrentApiUrl())||n.apiUrl}/auth`}constructor(t,i){this.http=t,this.envSwitcher=i,console.log("Auth Service initialized"),console.log("Environment API URL:",n.apiUrl),console.log("Dynamic API URL:",this.envSwitcher.getCurrentApiUrl()),console.log("Final API URL:",this.apiUrl)}getHeaders(){return new p({"Content-Type":"application/json",Accept:"application/json","X-Requested-With":"XMLHttpRequest"})}login(t){return console.log("\u{1F510} Making login request to:",`${this.apiUrl}/auth/login`),console.log("\u{1F4E7} Credentials:",{email:t.email,password:"***"}),this.http.post(`${this.apiUrl}/auth/login`,t,{headers:this.getHeaders()})}register(t){return console.log("\u{1F4DD} Making registration request to:",`${this.apiUrl}/auth/signup`),console.log("\u{1F464} Registration data:",l(a({},t),{password:"***",password_confirmation:"***"})),this.http.post(`${this.apiUrl}/auth/signup`,t,{headers:this.getHeaders()})}setToken(t){localStorage.setItem("token",t),console.log("\u{1F511} Token stored successfully")}};e.\u0275fac=function(i){return new(i||e)(r(h),r(g))},e.\u0275prov=c({token:e,factory:e.\u0275fac,providedIn:"root"});let o=e;return o})();export{v as a};
