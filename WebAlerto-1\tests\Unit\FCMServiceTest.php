<?php

namespace Tests\Unit;

use Tests\TestCase;
use App\Services\FCMService;
use App\Models\Notification;
use App\Models\DeviceToken;
use Illuminate\Support\Facades\Log;
use Kreait\Firebase\Messaging\CloudMessage;
use Kreait\Firebase\Messaging\SendReport;
use Kreait\Firebase\Messaging\MulticastSendReport;
use Kreait\Firebase\Exception\MessagingException;

class FCMServiceTest extends TestCase
{
    protected $fcmService;
    protected $messaging;

    protected function setUp(): void
    {
        parent::setUp();
        $this->fcmService = $this->app->make(FCMService::class);
    }

    public function test_send_notification_success()
    {
        // Create test notification
        $notification = Notification::create([
            'title' => 'Test Notification',
            'message' => 'This is a test notification',
            'severity' => 'normal',
            'category' => 'test'
        ]);

        // Create test device token
        $token = DeviceToken::create([
            'token' => 'test_token_123',
            'device_type' => 'android',
            'project_id' => 'test_project'
        ]);

        // Mock the messaging service
        $this->mockMessagingService();

        // Send notification
        $result = $this->fcmService->sendNotification($notification, [$token->token]);

        // Assert success
        $this->assertTrue($result['success']);
        $this->assertEquals(1, $result['success_count']);
        $this->assertEquals(0, $result['failure_count']);
        $this->assertEquals(0, $result['invalid_tokens']);

        // Assert notification was marked as sent
        $this->assertTrue($notification->fresh()->sent);
    }

    public function test_send_notification_with_invalid_token()
    {
        // Create test notification
        $notification = Notification::create([
            'title' => 'Test Notification',
            'message' => 'This is a test notification',
            'severity' => 'normal',
            'category' => 'test'
        ]);

        // Create test device token
        $token = DeviceToken::create([
            'token' => 'invalid_token',
            'device_type' => 'android',
            'project_id' => 'test_project'
        ]);

        // Mock the messaging service to simulate invalid token
        $this->mockMessagingServiceWithInvalidToken();

        // Send notification
        $result = $this->fcmService->sendNotification($notification, [$token->token]);

        // Assert failure
        $this->assertTrue($result['success']);
        $this->assertEquals(0, $result['success_count']);
        $this->assertEquals(1, $result['failure_count']);
        $this->assertEquals(1, $result['invalid_tokens']);

        // Assert token was deactivated
        $this->assertFalse($token->fresh()->is_active);
    }

    public function test_send_notification_with_rate_limit()
    {
        // Create test notification
        $notification = Notification::create([
            'title' => 'Test Notification',
            'message' => 'This is a test notification',
            'severity' => 'normal',
            'category' => 'test'
        ]);

        // Create test device token
        $token = DeviceToken::create([
            'token' => 'test_token_123',
            'device_type' => 'android',
            'project_id' => 'test_project'
        ]);

        // Mock the messaging service
        $this->mockMessagingService();

        // Send multiple notifications to trigger rate limit
        for ($i = 0; $i < 61; $i++) {
            $result = $this->fcmService->sendNotification($notification, [$token->token]);
        }

        // Assert rate limit was hit
        $this->assertFalse($result['success']);
        $this->assertEquals('Rate limit exceeded. Please try again later.', $result['message']);
    }

    protected function mockMessagingService()
    {
        $sendReport = $this->createMock(SendReport::class);
        $sendReport->method('successes')->willReturn([$this->createMock(CloudMessage::class)]);
        $sendReport->method('failures')->willReturn([]);

        $this->messaging = $this->createMock(\Kreait\Firebase\Messaging::class);
        $this->messaging->method('sendAll')->willReturn($sendReport);

        $this->app->instance(\Kreait\Firebase\Messaging::class, $this->messaging);
    }

    protected function mockMessagingServiceWithInvalidToken()
    {
        $failure = $this->createMock(\Kreait\Firebase\Messaging\FailedToSendMessage::class);
        $failure->method('error')->willReturn(new MessagingException('Invalid registration token'));

        $sendReport = $this->createMock(SendReport::class);
        $sendReport->method('successes')->willReturn([]);
        $sendReport->method('failures')->willReturn([$failure]);

        $this->messaging = $this->createMock(\Kreait\Firebase\Messaging::class);
        $this->messaging->method('sendAll')->willReturn($sendReport);

        $this->app->instance(\Kreait\Firebase\Messaging::class, $this->messaging);
    }
} 