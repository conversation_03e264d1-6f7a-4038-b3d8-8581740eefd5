<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // First, check if the user_id column exists
        if (Schema::hasColumn('device_tokens', 'user_id')) {
            // Try to drop the foreign key constraint if it exists
            try {
                Schema::table('device_tokens', function (Blueprint $table) {
                    $table->dropForeign(['user_id']);
                });
            } catch (\Exception $e) {
                // Foreign key constraint doesn't exist, continue
            }

            // Make the user_id column nullable
            Schema::table('device_tokens', function (Blueprint $table) {
                $table->unsignedBigInteger('user_id')->nullable()->change();
            });

            // Add the foreign key constraint, but with nullable allowed
            Schema::table('device_tokens', function (Blueprint $table) {
                $table->foreign('user_id')
                      ->references('id')
                      ->on('users')
                      ->onDelete('cascade');
            });
        } else {
            // If the column doesn't exist, add it
            Schema::table('device_tokens', function (Blueprint $table) {
                $table->unsignedBigInteger('user_id')->nullable()->after('id');

                $table->foreign('user_id')
                      ->references('id')
                      ->on('users')
                      ->onDelete('cascade');
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Check if the user_id column exists
        if (Schema::hasColumn('device_tokens', 'user_id')) {
            // Try to drop the foreign key constraint if it exists
            try {
                Schema::table('device_tokens', function (Blueprint $table) {
                    $table->dropForeign(['user_id']);
                });
            } catch (\Exception $e) {
                // Foreign key constraint doesn't exist, continue
            }

            // Make the user_id column non-nullable again
            Schema::table('device_tokens', function (Blueprint $table) {
                $table->unsignedBigInteger('user_id')->nullable(false)->change();
            });

            // Add the original foreign key constraint back
            Schema::table('device_tokens', function (Blueprint $table) {
                $table->foreign('user_id')
                      ->references('id')
                      ->on('users')
                      ->onDelete('cascade');
            });
        }
    }
};
