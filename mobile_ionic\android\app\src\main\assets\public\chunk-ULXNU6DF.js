import{A as d,D as c,Db as v,F as a,G as s,H as r,Ka as f,M as p,T as u,Wa as O,X as m,ea as h,oa as b,y as l,z as g}from"./chunk-YFIZFQXH.js";import"./chunk-EFGZ4QZO.js";import"./chunk-VKNYRYKJ.js";import"./chunk-YLGV66M3.js";import"./chunk-VD4VFVTQ.js";import"./chunk-HC6MZPB3.js";import"./chunk-IP3IRUG3.js";import"./chunk-UKIOCGZG.js";import"./chunk-RMJ7PCZJ.js";import"./chunk-K54AU7WQ.js";import"./chunk-VBU7NMPV.js";import"./chunk-JIZJ6CAM.js";import"./chunk-MCRJI3T3.js";import"./chunk-6OW47QMA.js";import"./chunk-HBC3IQSD.js";import"./chunk-RXV5ZBOB.js";import"./chunk-ODNE7IRY.js";import"./chunk-F4H6ZFEG.js";import"./chunk-JWIEPCRG.js";import"./chunk-WTKF7BA6.js";import"./chunk-SV7S5NYR.js";import"./chunk-WTCPO44B.js";import"./chunk-L5T6STQ3.js";import"./chunk-3EJRMEWO.js";import"./chunk-GNOVVPTF.js";import"./chunk-BAKMWPBW.js";import"./chunk-5OMUW5VI.js";import"./chunk-OBXDPQ3V.js";import"./chunk-LNJ3S2LQ.js";var w=()=>["OK"],k=(()=>{let t=class t{constructor(n){this.router=n,this.isOnline=!1}ngOnInit(){console.log("LoadingPage ngOnInit");let n=window;n.appDebug&&n.appDebug("LoadingPage ngOnInit"),this.checkInternetConnection()}checkInternetConnection(){this.isOnline=navigator.onLine,console.log("LoadingPage checkInternetConnection, isOnline:",this.isOnline);let n=window;n.appDebug&&n.appDebug("LoadingPage checkInternetConnection, isOnline: "+this.isOnline);let e=localStorage.getItem("token"),i=localStorage.getItem("onboardingComplete");console.log("Auth status - Token:",!!e,"Onboarding complete:",i==="true","Online:",this.isOnline),setTimeout(()=>{e&&i==="true"?(console.log("User is authenticated and onboarding complete - navigating to tabs/home"),n.appDebug&&n.appDebug("LoadingPage navigating to tabs/home (authenticated & onboarded)"),this.router.navigate(["/tabs/home"])):e?(console.log("User is authenticated but onboarding incomplete - navigating to welcome"),n.appDebug&&n.appDebug("LoadingPage navigating to welcome (authenticated but not onboarded)"),this.router.navigate(["/welcome"])):(console.log("User is not authenticated - navigating to login"),n.appDebug&&n.appDebug("LoadingPage navigating to login (not authenticated)"),this.router.navigate(["/login"]))},1e3)}ionViewWillEnter(){window.addEventListener("online",this.updateOnlineStatus.bind(this)),window.addEventListener("offline",this.updateOnlineStatus.bind(this))}ionViewWillLeave(){window.removeEventListener("online",this.updateOnlineStatus.bind(this)),window.removeEventListener("offline",this.updateOnlineStatus.bind(this))}updateOnlineStatus(){this.isOnline=navigator.onLine;let n=window;n.appDebug&&n.appDebug("LoadingPage updateOnlineStatus, isOnline: "+this.isOnline),this.checkInternetConnection()}};t.\u0275fac=function(e){return new(e||t)(g(h))},t.\u0275cmp=d({type:t,selectors:[["app-loading"]],decls:6,vars:3,consts:[[1,"ion-padding"],[1,"loading-container"],[1,"loader"],["header","No Internet Connection","message","Please check your internet connection and try again.",3,"isOpen","buttons"]],template:function(e,i){e&1&&(a(0,"ion-content",0)(1,"div",1),r(2,"div",2),a(3,"h2"),p(4,"Loading..."),s(),r(5,"ion-alert",3),s()()),e&2&&(l(5),c("isOpen",!i.isOnline)("buttons",u(2,w)))},dependencies:[v,f,O,m,b],styles:[".loading-container[_ngcontent-%COMP%]{display:flex;flex-direction:column;justify-content:center;align-items:center;height:100%;text-align:center}.loader[_ngcontent-%COMP%]{width:48px;height:48px;border:5px solid #FFF;border-bottom-color:#3880ff;border-radius:50%;display:inline-block;box-sizing:border-box;animation:_ngcontent-%COMP%_rotation 1s linear infinite;margin-bottom:20px}@keyframes _ngcontent-%COMP%_rotation{0%{transform:rotate(0)}to{transform:rotate(360deg)}}h2[_ngcontent-%COMP%]{color:#3880ff;font-size:24px;margin:0}"]});let o=t;return o})();export{k as LoadingPage};
