@extends('layout.app')

@section('content')
<div class="container mx-auto px-4 py-8">
    <div class="bg-white rounded-lg shadow-md p-6">
        <h1 class="text-2xl font-bold mb-6">Register FCM Token Manually</h1>
        
        @if(session('success'))
            <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
                {{ session('success') }}
            </div>
        @endif
        
        @if(session('error'))
            <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
                {{ session('error') }}
            </div>
        @endif
        
        <form action="{{ route('test-fcm-register-token') }}" method="POST" class="space-y-4">
            @csrf
            <div>
                <label for="token" class="block text-sm font-medium text-gray-700">FCM Token</label>
                <input type="text" name="token" id="token" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500" required>
                <p class="mt-1 text-xs text-gray-500">Enter the FCM token from your mobile device</p>
            </div>
            
            <div>
                <label for="device_type" class="block text-sm font-medium text-gray-700">Device Type</label>
                <select name="device_type" id="device_type" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                    <option value="android">Android</option>
                    <option value="ios">iOS</option>
                    <option value="web">Web</option>
                </select>
            </div>
            
            <div>
                <label for="user_id" class="block text-sm font-medium text-gray-700">User ID (Optional)</label>
                <input type="number" name="user_id" id="user_id" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                <p class="mt-1 text-xs text-gray-500">Associate this token with a specific user (leave empty for anonymous tokens)</p>
            </div>
            
            <div>
                <button type="submit" class="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    Register Token
                </button>
            </div>
        </form>
        
        <div class="mt-8">
            <h2 class="text-xl font-semibold mb-4">How to Get Your FCM Token</h2>
            
            <div class="bg-gray-50 p-4 rounded-md">
                <ol class="list-decimal list-inside space-y-2">
                    <li>Open the Ionic app on your device</li>
                    <li>Go to the login page</li>
                    <li>Open the browser developer console (if testing in browser)</li>
                    <li>Look for a log message that says "FCM Token: [your-token]"</li>
                    <li>Copy the token and paste it in the form above</li>
                </ol>
            </div>
            
            <div class="mt-4">
                <a href="{{ route('test-fcm') }}" class="text-blue-600 hover:text-blue-800">
                    &larr; Back to Test FCM
                </a>
            </div>
        </div>
    </div>
</div>
@endsection
