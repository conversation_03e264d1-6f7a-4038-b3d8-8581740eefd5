@extends('layout.app')

@section('title', 'Edit Evacuation Center')

@section('content')
<div class="container mx-auto p-6">
    <div class="relative bg-white p-6 rounded shadow-md">
        <a href="{{ route('components.evacuation_management.evacuation-dashboard') }}"
            class="absolute top-6 right-6 text-gray-500 hover:text-red-500 text-4xl font-bold leading-none">
            &times;
        </a>

        <h2 class="text-2xl font-bold mb-4">Edit Evacuation Center</h2>
        <p class="text-gray-600 mb-4">Update the information for the evacuation center.</p>

        <form action="{{ route('components.evacuation_management.update', $evacuationCenter->id) }}" method="POST">
            @csrf
            @method('PUT')
            
            @if ($errors->any())
                <div class="mb-4 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
                    <ul class="list-disc pl-5">
                        @foreach ($errors->all() as $error)
                            <li>{{ $error }}</li>
                        @endforeach
                    </ul>
                </div>
            @endif

            <div class="mb-4">
                <label for="name" class="block text-gray-700 font-bold mb-2">Center Name</label>
                <input type="text" id="name" name="name" value="{{ old('name', $evacuationCenter->name) }}" class="border border-gray-300 rounded px-4 py-2 w-full" required>
            </div>

            <div class="mb-4">
                <label for="capacity" class="block text-gray-700 font-bold mb-2">Capacity</label>
                <input type="number" id="capacity" name="capacity" value="{{ old('capacity', $evacuationCenter->capacity) }}" class="border border-gray-300 rounded px-4 py-2 w-full" required>
            </div>

            <div class="mb-4">
                <label for="contact" class="block text-gray-700 font-bold mb-2">Contact Number</label>
                <input type="text" id="contact" name="contact" value="{{ old('contact', $evacuationCenter->contact) }}" class="border border-gray-300 rounded px-4 py-2 w-full" required>
            </div>

            <div class="mb-4">
                <label for="disaster_type" class="block text-gray-700 font-bold mb-2">Disaster Type</label>
                <select id="disaster_type" name="disaster_type" class="border border-gray-300 rounded px-4 py-2 w-full" required>
                    <option value="Typhoon" {{ old('disaster_type', $evacuationCenter->disaster_type) == 'Typhoon' ? 'selected' : '' }}>Typhoon (Red Marker)</option>
                    <option value="Flood" {{ old('disaster_type', $evacuationCenter->disaster_type) == 'Flood' ? 'selected' : '' }}>Flood (Green Marker)</option>
                    <option value="Earthquake" {{ old('disaster_type', $evacuationCenter->disaster_type) == 'Earthquake' ? 'selected' : '' }}>Earthquake (Yellow-Orange Marker)</option>
                </select>
            </div>

            <div class="mb-4">
                <label for="status" class="block text-gray-700 font-bold mb-2">Status</label>
                <select id="status" name="status" class="border border-gray-300 rounded px-4 py-2 w-full" required>
                    <option value="Active" {{ old('status', $evacuationCenter->status) == 'Active' ? 'selected' : '' }}>Active</option>
                    <option value="Inactive" {{ old('status', $evacuationCenter->status) == 'Inactive' ? 'selected' : '' }}>Inactive</option>
                </select>
            </div>

            <div class="mb-4">
                <label for="street_name" class="block text-gray-700 font-bold mb-2">Street Name</label>
                <input type="text" id="street_name" name="street_name" value="{{ old('street_name', $evacuationCenter->street_name) }}" class="border border-gray-300 rounded px-4 py-2 w-full" required>
            </div>

            <div class="mb-4">
                <label for="barangay" class="block text-gray-700 font-bold mb-2">Barangay</label>
                <input type="text" id="barangay" name="barangay" value="{{ old('barangay', $evacuationCenter->barangay) }}" class="border border-gray-300 rounded px-4 py-2 w-full" required>
            </div>

            <div class="mb-4">
                <label for="city" class="block text-gray-700 font-bold mb-2">City</label>
                <input type="text" id="city" name="city" value="{{ old('city', $evacuationCenter->city) }}" class="border border-gray-300 rounded px-4 py-2 w-full" required>
            </div>

            <div class="mb-4">
                <label for="province" class="block text-gray-700 font-bold mb-2">Province</label>
                <input type="text" id="province" name="province" value="{{ old('province', $evacuationCenter->province) }}" class="border border-gray-300 rounded px-4 py-2 w-full" required>
            </div>

            <div class="mb-4">
                <label for="postal_code" class="block text-gray-700 font-bold mb-2">Postal Code</label>
                <input type="text" id="postal_code" name="postal_code" value="{{ old('postal_code', $evacuationCenter->postal_code) }}" class="border border-gray-300 rounded px-4 py-2 w-full" required>
            </div>

            <input type="hidden" id="latitude" name="latitude" value="{{ old('latitude', $evacuationCenter->latitude) }}" required>
            <input type="hidden" id="longitude" name="longitude" value="{{ old('longitude', $evacuationCenter->longitude) }}" required>

            <div class="flex justify-end gap-4">
                <a href="{{ route('components.evacuation_management.evacuation-dashboard') }}"
                   class="bg-gray-500 text-white px-4 py-2 rounded hover:bg-gray-600">Cancel</a>
                <button type="submit" class="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600">Update Center</button>
            </div>
        </form>
    </div>
</div>
@endsection