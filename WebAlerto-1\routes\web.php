<?php
use App\Http\Controllers\LoginController;
use App\Http\Controllers\RegisterController;
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\EvacuationManagementController;
use App\Http\Controllers\UserController;
use App\Http\Controllers\NotificationController;
use App\Http\Controllers\TestFCMController;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\MappingSystemController;



Route::get('/map', [MappingSystemController::class, 'index'])->name('map');





// Login Routes
Route::get('/login', [LoginController::class, 'showLoginForm'])->name('login');
Route::post('/login', [LoginController::class, 'login']);
Route::post('/logout', [LoginController::class, 'logout'])->name('logout');
Route::get('/logout', [LoginController::class, 'logout'])->name('logout');

// Register Routes
Route::get('/register', [RegisterController::class, 'showRegistrationForm'])->name('register');
Route::post('/register', [RegisterController::class, 'register']);


Route::get('/dashboard',[DashboardController::class,'index'])->name('components.dashboard');

Route::get('/app', function () {
    return view('layout.app');
});

Route::get('/evac-management', [EvacuationManagementController::class, 'showDashboard'])->name('components.evacuation_management.evacuation-dashboard');
 Route::get('/evacuation/add', [EvacuationManagementController::class, 'showAddForm'])->name('components.evacuation_management.add-evacuation-center');
 Route::post('/evacuation/store', [EvacuationManagementController::class, 'store'])->name('components.evacuation_management.store');
 Route::get('/evacuation/{id}/edit', [EvacuationManagementController::class, 'showEditForm'])->name('components.evacuation_management.edit-evacuation-center');
 Route::post('/evacuation/{id}/edit', [EvacuationManagementController::class, 'edit'])->name('components.evacuation_management.edit-evacuation-center');
 Route::put('/evacuation/{id}', [EvacuationManagementController::class, 'update'])->name('components.evacuation_management.update');
 Route::delete('/evacuation/{id}', [EvacuationManagementController::class, 'destroy'])->name('components.evacuation_management.destroy');
 Route::get('/evacuation/{id}', [EvacuationManagementController::class, 'show'])->name('components.evacuation_management.view-map');
 Route::get('/centers/{type?}', [EvacuationManagementController::class, 'centersList'])
     ->name('components.evacuation_management.centers-list')
     ->where('type', 'all|active|inactive')
     ->defaults('type', 'all');

// User Management Routes
Route::get('/user-management', [UserController::class, 'index'])->name('components.user-management');
Route::get('/user/{id}', [UserController::class, 'view'])->name('components.user.view');
Route::patch('/user/{id}/verify', [UserController::class, 'verify'])->name('components.user.verify');
Route::patch('/users/{id}/deactivate', [UserController::class, 'deactivate'])->name('users.deactivate');
Route::get('user-management/search', [UserController::class, 'search'])->name('components.user-management.search');



// Notifications

Route::get('/notification/{id}', [NotificationController::class, 'view'])->name('components.notification.view'); // For viewing a single notification
Route::get('/notification', [NotificationController::class, 'index'])->name('components.notification.index'); // For listing all notifications
Route::get('/notification/create', [NotificationController::class, 'create'])->name('components.notification.create'); // For creating a notification
Route::post('/notification/store', [NotificationController::class, 'store'])->name('components.notification.store'); // For storing a notification


Route::post('/mapping/store', [MappingSystemController::class, 'store'])->name('mapping.store');
Route::post('/mapping/store-batch', [MappingSystemController::class, 'storeBatch'])->name('mapping.store-batch');
Route::get('/mapping/search', [MappingSystemController::class, 'search'])->name('mapping.search');



Route::get('/api/evacuation-centers/{id}', [EvacuationManagementController::class, 'getCenterDetails']);

// SSE Route
Route::get('/sse', [App\Http\Controllers\SseController::class, 'stream']);

// FCM Test Routes
Route::get('/test-fcm', [TestFCMController::class, 'index'])->name('test-fcm');
Route::post('/test-fcm/send', [TestFCMController::class, 'send'])->name('test-fcm-send');
Route::get('/test-fcm/register-token', [TestFCMController::class, 'showRegisterTokenForm'])->name('test-fcm-register-token-form');
Route::post('/test-fcm/register-token', [TestFCMController::class, 'registerToken'])->name('test-fcm-register-token');

// Test simulated notifications
Route::get('/test-simulated-notification', function () {
    return view('test-simulated-notification');
})->name('test-simulated-notification');

