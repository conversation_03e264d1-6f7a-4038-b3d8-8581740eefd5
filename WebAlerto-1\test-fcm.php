<?php
require __DIR__.'/vendor/autoload.php';
require __DIR__.'/bootstrap/app.php';

// Get the application instance
$app = $app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

// Get the FCM service from the container
$fcmService = $app->make(App\Services\FCMService::class);

// Replace this with your actual FCM token from the app
$token = "PASTE_YOUR_FCM_TOKEN_HERE";

// Create a test notification
$notification = new App\Models\Notification([
    'title' => 'Direct Test Notification',
    'message' => 'This is a direct test from PHP script',
    'category' => 'General',
    'severity' => 'high',
    'sent' => false
]);
$notification->save();

// Send the notification to the specific token
try {
    $result = $fcmService->send($token, [
        'title' => $notification->title,
        'message' => $notification->message,
        'data' => [
            'notification_id' => $notification->id,
            'category' => $notification->category,
            'severity' => $notification->severity,
            'time' => date('Y-m-d H:i:s')
        ]
    ]);
    
    echo "Notification sent successfully!\n";
    echo "Message ID: " . $result . "\n";
    
    // Update notification status
    $notification->sent = true;
    $notification->save();
    
} catch (Exception $e) {
    echo "Error sending notification: " . $e->getMessage() . "\n";
    
    // Log the error
    error_log("FCM Test Error: " . $e->getMessage());
}
