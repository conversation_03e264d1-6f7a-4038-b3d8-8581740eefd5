<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Support\Facades\Log;

class DeviceToken extends Model
{
    use HasFactory;

    protected $fillable = [
        'token',
        'device_type',
        'project_id',
        'is_active',
        'last_used_at',
        'deactivated_at',
        'user_id'
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'last_used_at' => 'datetime',
        'deactivated_at' => 'datetime'
    ];

    protected $attributes = [
        'is_active' => true
    ];

    public function isValid()
    {
        return $this->is_active && !$this->isExpired();
    }

    public function isExpired()
    {
        // Tokens are considered expired if not used for 30 days
        return $this->last_used_at && $this->last_used_at->diffInDays(now()) > 30;
    }

    public function markAsUsed()
    {
        $this->last_used_at = now();
        $this->save();
    }

    public function deactivate()
    {
        $this->is_active = false;
        $this->deactivated_at = now();
        $this->save();

        Log::info('Device token deactivated', [
            'token_hash' => hash('sha256', $this->token)
        ]);
    }

    public function reactivate()
    {
        $this->is_active = true;
        $this->deactivated_at = null;
        $this->last_used_at = now();
        $this->save();

        Log::info('Device token reactivated', [
            'token_hash' => hash('sha256', $this->token)
        ]);
    }

    public function scopeActive($query)
    {
        return $query->where('is_active', true)
            ->where(function ($q) {
                $q->whereNull('last_used_at')
                    ->orWhere('last_used_at', '>=', now()->subDays(30));
            });
    }

    public function scopeByProject($query, $projectId)
    {
        return $query->where('project_id', $projectId);
    }

    public function scopeByDeviceType($query, $deviceType)
    {
        return $query->where('device_type', $deviceType);
    }

    public function scopeExpired($query)
    {
        return $query->where('last_used_at', '<', now()->subDays(30));
    }

    public function scopeInactive($query)
    {
        return $query->where('is_active', false);
    }

    public function scopeActiveAndValid($query)
    {
        return $query->active()->where('is_active', true);
    }

    /**
     * Get tokens for a specific user
     */
    public function scopeForUser($query, $userId)
    {
        return $query->where('user_id', $userId);
    }

    public function getTokenHashAttribute()
    {
        return hash('sha256', $this->token);
    }

    /**
     * Get the user that owns the device token
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($token) {
            $token->last_used_at = now();
        });

        static::updating(function ($token) {
            if ($token->isDirty('is_active') && !$token->is_active) {
                $token->deactivated_at = now();
            }
        });
    }
}
