<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Create the device_tokens table if it doesn't exist
        if (!Schema::hasTable('device_tokens')) {
            Schema::create('device_tokens', function (Blueprint $table) {
                $table->id();
                $table->unsignedBigInteger('user_id')->nullable();
                $table->string('token', 255);
                $table->string('device_type', 20)->default('web');
                $table->string('project_id', 100)->nullable();
                $table->boolean('is_active')->default(true);
                $table->timestamp('last_used_at')->nullable();
                $table->timestamps();
                
                $table->foreign('user_id')
                      ->references('id')
                      ->on('users')
                      ->onDelete('cascade');
                
                $table->index('token');
                $table->index('device_type');
                $table->index('is_active');
            });
        } else {
            // Ensure the table has all the required columns
            $columns = Schema::getColumnListing('device_tokens');
            
            // Add user_id column if it doesn't exist
            if (!in_array('user_id', $columns)) {
                Schema::table('device_tokens', function (Blueprint $table) {
                    $table->unsignedBigInteger('user_id')->nullable()->after('id');
                    
                    try {
                        $table->foreign('user_id')
                              ->references('id')
                              ->on('users')
                              ->onDelete('cascade');
                    } catch (\Exception $e) {
                        // Foreign key already exists or can't be created
                    }
                });
            }
            
            // Add other columns if they don't exist
            if (!in_array('device_type', $columns)) {
                Schema::table('device_tokens', function (Blueprint $table) {
                    $table->string('device_type', 20)->default('web')->after('token');
                });
            }
            
            if (!in_array('project_id', $columns)) {
                Schema::table('device_tokens', function (Blueprint $table) {
                    $table->string('project_id', 100)->nullable()->after('device_type');
                });
            }
            
            if (!in_array('is_active', $columns)) {
                Schema::table('device_tokens', function (Blueprint $table) {
                    $table->boolean('is_active')->default(true)->after('project_id');
                });
            }
            
            if (!in_array('last_used_at', $columns)) {
                Schema::table('device_tokens', function (Blueprint $table) {
                    $table->timestamp('last_used_at')->nullable()->after('is_active');
                });
            }
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // We don't want to drop the table or columns in the down method
        // as it might be used by other migrations
    }
};
