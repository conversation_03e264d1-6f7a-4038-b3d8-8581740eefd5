 // Prepare data from backend
const rawData = JSON.parse(document.getElementById('monthlyCategoryCounts').textContent);
 // Extract unique months and categories
 const months = [...new Set(rawData.map(item => item.month))];
 const categories = [...new Set(rawData.map(item => item.category))];
 // Prepare datasets for each category
 const datasets = categories.map(category => {
     return {
         label: category,
         data: months.map(month => {
             const found = rawData.find(item => item.month === month && item.category === category);
             return found ? found.count : 0;
         }),
         backgroundColor: `rgba(${Math.floor(Math.random()*255)},${Math.floor(Math.random()*255)},${Math.floor(Math.random()*255)},0.7)`
     };
 });
 const ctx = document.getElementById('notifChart').getContext('2d');
 new Chart(ctx, {
     type: 'bar',
     data: {
         labels: months,
         datasets: datasets
     },
     options: {
         responsive: true,
         plugins: {
             legend: { display: true, position: 'bottom' },
             title: { display: true, text: 'Notifications per Month & Category' }
         },
         scales: {
             x: { stacked: false },
             y: { stacked: false, beginAtZero: true }
         }
     }
 });